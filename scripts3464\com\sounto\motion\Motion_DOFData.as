package com.sounto.motion
{
   public class Motion_DOFData
   {
      
      public var up:int = 0;
      
      public var down:int = 0;
      
      public var left:int = 0;
      
      public var right:int = 0;
      
      public var up_back:Number = 0;
      
      public var down_back:Number = 0;
      
      public var left_back:Number = 0;
      
      public var right_back:Number = 0;
      
      public var left_out:Number = 0;
      
      public var right_out:Number = 0;
      
      public var hitType:String = DofHitType.no;
      
      public var slopeAngle:Number = 0;
      
      public var slopeY:Number = 10000;
      
      public var minSlopeY:Number = -10000;
      
      public function Motion_DOFData()
      {
         super();
      }
      
      public function init() : void
      {
         this.up = 0;
         this.down = 0;
         this.left = 0;
         this.right = 0;
         this.up_back = 0;
         this.down_back = 0;
         this.left_back = 0;
         this.right_back = 0;
         this.left_out = 0;
         this.right_out = 0;
         this.hitType = "";
         this.slopeAngle = 0;
         this.slopeY = 10000;
         this.minSlopeY = -10000;
      }
      
      public function limitBack(max0:int) : *
      {
         if(this.up_back > max0)
         {
            this.up_back = max0;
         }
         if(this.up_back < 0)
         {
            this.up_back = 0;
         }
         if(this.down_back > max0)
         {
            this.down_back = max0;
         }
         if(this.down_back < 0)
         {
            this.down_back = 0;
         }
         if(this.left_back > max0)
         {
            this.left_back = max0;
         }
         if(this.left_back < 0)
         {
            this.left_back = 0;
         }
         if(this.right_back > max0)
         {
            this.right_back = max0;
         }
         if(this.right_back < 0)
         {
            this.right_back = 0;
         }
      }
      
      public function toString() : String
      {
         var n:* = undefined;
         var name0:String = null;
         var pro_arr:Array = ["left_back","right_back","up_back","down_back"];
         var str0:String = "";
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            str0 += name0 + ":" + this[name0] + ",";
         }
         return str0;
      }
   }
}

