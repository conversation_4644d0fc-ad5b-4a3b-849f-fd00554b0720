package dataAll._app.active
{
   import dataAll._app.active.define.ActiveTaskDefine;
   
   public class ActiveTaskData
   {
      
      public var def:ActiveTaskDefine;
      
      public var now:int = 0;
      
      public function ActiveTaskData()
      {
         super();
      }
      
      public function getCompleteB() : Bo<PERSON>an
      {
         return this.now >= this.def.num;
      }
   }
}

