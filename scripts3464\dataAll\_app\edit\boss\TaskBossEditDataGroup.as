package dataAll._app.edit.boss
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit.EditSaveGroup;
   import dataAll._app.task.TaskState;
   import dataAll._player.PlayerData;
   import dataAll.ui.GatherColor;
   
   public class TaskBossEditDataGroup extends CodeBossEditDataGroup
   {
      
      private var bossSaveG:BossEditSaveGroup = null;
      
      public function TaskBossEditDataGroup()
      {
         super();
      }
      
      public function openUI(saveG0:BossEditSaveGroup, pd0:PlayerData) : void
      {
         var d0:BossTaskDefine = null;
         this.bossSaveG = saveG0;
         editSave = new EditSaveGroup();
         inData_bySave(editSave);
         BossTaskCtrl.initData();
         var darr0:Array = BossTaskCtrl.getArrWeek(pd0.time.getWeekIndex());
         for each(d0 in darr0)
         {
            this.newDataByTaskDefine(d0);
         }
         saveG0.dealBossScore();
      }
      
      private function newDataByTaskDefine(d0:BossTaskDefine) : BossEditData
      {
         var da0:BossEditData = newDataByCode(d0.o);
         var state0:String = d0.getState(playerData.time.getReadTimeDate());
         da0.setTaskDefine(d0,state0,this.bossSaveG);
         return da0;
      }
      
      public function getCanMax() : int
      {
         var da0:BossEditData = null;
         var state0:String = null;
         var num0:int = 0;
         for each(da0 in arr)
         {
            state0 = da0.getTaskState();
            if(TaskState.noInTimeArr.indexOf(state0) == -1)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getWinNum() : int
      {
         var da0:BossEditData = null;
         var state0:String = null;
         var num0:int = 0;
         for each(da0 in arr)
         {
            state0 = da0.getTaskState();
            if(state0 == TaskState.complete || state0 == TaskState.over)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getLeftText() : String
      {
         var s0:String = "";
         var max0:int = this.getCanMax();
         var win0:int = this.getWinNum();
         s0 += "本周挑战次数：" + ComMethod.mustColor(win0,max0,false,"#00FF00","#00CCFF");
         s0 += "\n累计击败首领：" + ComMethod.color("<b>" + this.bossSaveG.bA + "个</b>","#00FFFF");
         s0 += "\n击败最高难度：" + ComMethod.color("<b>" + this.bossSaveG.mBA + "个</b>","#FFFF00");
         s0 += "\n战场积分：" + ComMethod.color("<b>" + this.bossSaveG.bs + "分</b>","#00FF00");
         return s0 + ("\n" + ComMethod.color("积分=击败首领的难度之和，同时获得对应积分的抽魂卡次数。",GatherColor.graydarkColor));
      }
   }
}

