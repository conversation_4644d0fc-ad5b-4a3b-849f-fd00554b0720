package dataAll.pet.dispatch
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pet.PetData;
   import dataAll.pet.PetDataGroup;
   
   public class PetDispatchData
   {
      
      private var dataG:PetDataGroup = null;
      
      private var save:PetDispatchSave;
      
      private var tempSaveObj:Object = null;
      
      private var tempGift:GiftAddDefineGroup = null;
      
      public function PetDispatchData()
      {
         super();
      }
      
      public function setDataGroup(dataG0:PetDataGroup) : void
      {
         this.dataG = dataG0;
      }
      
      public function inData_bySave(s0:PetDispatchSave) : void
      {
         this.save = s0;
      }
      
      public function getCanAddNum() : int
      {
         return PetDispatchSave.getDispatchMax() - this.save.idArr.length;
      }
      
      public function getDataArr() : Array
      {
         var id0:String = null;
         var da0:PetData = null;
         var arr0:Array = [];
         var newIdArr0:Array = [];
         var idArr0:Array = this.save.idArr;
         var num0:int = 0;
         for each(id0 in idArr0)
         {
            da0 = this.dataG.getDataById(id0);
            if(Boolean(da0) && num0 < PetDispatchSave.getDispatchMax())
            {
               arr0.push(da0);
               newIdArr0.push(id0);
               num0++;
            }
         }
         this.save.setNewIdArr(newIdArr0);
         return arr0;
      }
      
      public function getNoDataArr() : Array
      {
         var da0:PetData = null;
         var arr0:Array = [];
         for each(da0 in this.dataG.arr)
         {
            if(!this.panDataIn(da0))
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getAllHour() : int
      {
         var da0:PetData = null;
         var v0:int = 0;
         var arr0:Array = this.getDataArr();
         for each(da0 in arr0)
         {
            v0 += da0.getDispatchHour();
         }
         return v0;
      }
      
      public function addDataArr(arr0:Array) : void
      {
         var da0:PetData = null;
         for each(da0 in arr0)
         {
            this.save.addId(da0.getId());
         }
      }
      
      public function removeData(da0:PetData) : void
      {
         this.save.removeId(da0.getId());
      }
      
      public function panDataIn(da0:PetData) : Boolean
      {
         var idArr0:Array = this.save.idArr;
         return idArr0.indexOf(da0.getId()) >= 0;
      }
      
      public function getSurplusMin(nowTime0:String) : Number
      {
         var allMin0:Number = this.getAllHour() * 60;
         var useMin0:Number = this.getUseMin(nowTime0);
         var v0:Number = allMin0 - useMin0;
         if(v0 < 0)
         {
            v0 = 0;
         }
         return v0;
      }
      
      public function getUseMin(nowTime0:String) : Number
      {
         var now0:StringDate = null;
         var nowValue0:Number = NaN;
         var startValue0:* = undefined;
         var min0:Number = 0;
         if(this.isIng())
         {
            now0 = new StringDate(nowTime0);
            nowValue0 = Number(now0.getDateClass().getTime());
            startValue0 = this.save.startTime;
            if(nowValue0 > startValue0 && startValue0 != 0)
            {
               min0 = Math.floor((nowValue0 - startValue0) / 1000 / 60);
            }
         }
         return min0;
      }
      
      public function start(timeStr0:String) : void
      {
         var timeDa0:StringDate = new StringDate(timeStr0);
         this.save.startTime = timeDa0.getDateClass().getTime();
         this.save.todayB = true;
      }
      
      public function cancel() : void
      {
         this.save.todayB = false;
         this.save.startTime = 0;
      }
      
      public function getState() : String
      {
         if(this.save.isDispatchB())
         {
            return PetDispatchSave.ING;
         }
         if(this.save.todayB)
         {
            return PetDispatchSave.OVER;
         }
         return PetDispatchSave.NO;
      }
      
      public function isNo() : Boolean
      {
         return this.getState() == PetDispatchSave.NO;
      }
      
      public function isIng() : Boolean
      {
         return this.getState() == PetDispatchSave.ING;
      }
      
      public function isOver() : Boolean
      {
         return this.getState() == PetDispatchSave.OVER;
      }
      
      public function isIngOver() : Boolean
      {
         var state0:String = this.getState();
         return state0 == PetDispatchSave.ING || state0 == PetDispatchSave.OVER;
      }
      
      public function getTodayB() : Boolean
      {
         return this.save.todayB;
      }
      
      public function getGiftEvent(timeStr0:String) : GiftAddDefineGroup
      {
         this.tempSaveObj = ClassProperty.copyObj(this.save);
         this.tempGift = PetDispatchGift.countGift(this.getUseMin(timeStr0),this.getDataArr());
         this.save.startTime = 0;
         return this.tempGift;
      }
      
      public function saveSuccessEvent() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = this.tempGift;
         this.tempGift = null;
         this.tempSaveObj = null;
         return g0;
      }
      
      public function saveFailEvent() : GiftAddDefineGroup
      {
         this.save.inData_byObj(this.tempSaveObj);
         var g0:GiftAddDefineGroup = this.tempGift;
         this.tempGift = null;
         this.tempSaveObj = null;
         return g0;
      }
   }
}

