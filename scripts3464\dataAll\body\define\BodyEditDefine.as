package dataAll.body.define
{
   import com.sounto.utils.ClassProperty;
   
   public class BodyEditDefine
   {
      
      public static var pro_arr:Array = null;
      
      public var raIfNoMapB:Boolean = true;
      
      public function BodyEditDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

