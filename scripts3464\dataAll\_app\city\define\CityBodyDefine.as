package dataAll._app.city.define
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.NormalDefine;
   import flash.geom.Point;
   
   public class CityBodyDefine extends NormalDefine
   {
      
      public static var pro_arr:Array = null;
      
      public static var fatherPro_arr:Array = ["type"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var type:String = "";
      
      public var coverP:Point = new Point();
      
      public var mouseB:Boolean = true;
      
      public var imgUrl:String = "";
      
      public var iconUrl:String = "";
      
      public var shadow:String = "";
      
      public function CityBodyDefine()
      {
         super();
         this.space = 1;
      }
      
      public function get space() : Number
      {
         return this.CF.getAttribute("space");
      }
      
      public function set space(v0:Number) : void
      {
         this.CF.setAttribute("space",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         if(this.type == CityBodyType.decora)
         {
            if(this.iconUrl == "")
            {
               this.iconUrl = "CityDecora/" + name + "Icon";
            }
            if(this.imgUrl == "")
            {
               this.imgUrl = "CityDecora/" + name;
            }
            this.mouseB = false;
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      public function inFatherXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,fatherPro_arr);
      }
   }
}

