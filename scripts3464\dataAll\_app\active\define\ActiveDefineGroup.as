package dataAll._app.active.define
{
   public class ActiveDefineGroup
   {
      
      private var taskObj:Object = {};
      
      public var taskArr:Array = [];
      
      private var giftObj:Object = {};
      
      public var giftArr:Array = [];
      
      public function ActiveDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(x0:XML) : void
      {
         this.inTaskXml(x0.task[0]);
         this.inGiftXml(x0.gift[0]);
      }
      
      private function inTaskXml(x0:XML) : void
      {
         var x1:XML = null;
         var d0:ActiveTaskDefine = null;
         for each(x1 in x0.one)
         {
            d0 = new ActiveTaskDefine();
            d0.inData_byXML(x1);
            this.taskObj[d0.name] = d0;
            this.taskArr.push(d0);
         }
      }
      
      private function inGiftXml(x0:XML) : void
      {
         var x1:XML = null;
         var d0:ActiveGiftDefine = null;
         for each(x1 in x0.one)
         {
            d0 = new ActiveGiftDefine();
            d0.inData_byXML(x1);
            this.giftObj[d0.name] = d0;
            this.giftArr.push(d0);
         }
      }
   }
}

