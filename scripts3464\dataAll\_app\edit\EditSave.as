package dataAll._app.edit
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit.boss.BossEditMethod;
   
   public class EditSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var id:String = "";
      
      public var winB:Boolean = false;
      
      public var lockB:Boolean = false;
      
      public var obj:Object = {};
      
      public function EditSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         if(ObjectMethod.isMustObjectB(this.obj) == false)
         {
            this.obj = {};
         }
      }
      
      public function inObjData_byBase64(code0:String) : void
      {
         this.obj = BossEditMethod.codeToObj(code0);
      }
      
      public function getName() : String
      {
         return this.getValue("n","");
      }
      
      public function setName(v0:String) : void
      {
         this.setValue("n",v0);
      }
      
      public function getValue(pro0:String, noValue0:* = null) : *
      {
         if(this.obj.hasOwnProperty(pro0))
         {
            return this.obj[pro0];
         }
         return noValue0;
      }
      
      public function getValueAndNoSet(pro0:String, noValue0:*) : *
      {
         if(this.obj.hasOwnProperty(pro0) == false)
         {
            this.obj[pro0] = noValue0;
         }
         return this.obj[pro0];
      }
      
      public function getValueArrayAndNoSet(pro0:String) : Array
      {
         if(this.obj.hasOwnProperty(pro0) == false)
         {
            this.obj[pro0] = [];
         }
         return this.obj[pro0];
      }
      
      public function getValueByDef(d0:EditProDefine) : *
      {
         var pro0:String = d0.name;
         if(this.obj.hasOwnProperty(pro0))
         {
            return this.obj[pro0];
         }
         return d0.getInit();
      }
      
      public function setValue(pro0:String, v0:*) : void
      {
         this.obj[pro0] = v0;
      }
   }
}

