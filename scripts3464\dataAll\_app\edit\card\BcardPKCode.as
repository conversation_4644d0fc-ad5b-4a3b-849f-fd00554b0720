package dataAll._app.edit.card
{
   import UI.UIOrder;
   import com.adobe.crypto.MD5;
   import com.common.data.Base64;
   import com.sounto.oldUtils.ComMethod;
   import flash.system.System;
   
   public class BcardPKCode
   {
      
      private var code:String = "";
      
      private var xmlStr:String = "";
      
      private var md:String = "";
      
      public function BcardPKCode()
      {
         super();
      }
      
      public static function getMd(xmlStr0:String, id0:String) : String
      {
         return MD5.hash(xmlStr0 + id0);
      }
      
      public static function codeToDataAndPan(code0:String) : BossCardData
      {
         var c0:BcardPKCode = null;
         var bb0:Boolean = false;
         var error0:String = "";
         if(code0 == "")
         {
            error0 = "代码不能为空！";
         }
         else
         {
            c0 = new BcardPKCode();
            try
            {
               bb0 = c0.inCode(code0);
               if(bb0 != false)
               {
                  return c0.getData();
               }
               error0 = "代码验证错误。";
            }
            catch(e:Error)
            {
               error0 = "代码格式错误。";
            }
         }
         if(error0 != "")
         {
            UIOrder.alertError(error0);
         }
         return null;
      }
      
      public static function copyCode(da0:BossCardData) : void
      {
         var c0:BcardPKCode = new BcardPKCode();
         c0.inSave(da0.getCardSave());
         Gaming.uiGroup.alertBox.textInput.showTextInput("确认复制 " + ComMethod.color(da0.cnName,"#FFFF00") + " 的魂卡代码？",c0.getCode(),yesCode,"yesAndNo",10000);
      }
      
      private static function yesCode(str0:String) : void
      {
         System.setClipboard(str0);
         Gaming.uiGroup.alertBox.showSuccess("魂卡代码已复制到剪贴板！发给你的朋友，\n让他们在“魂卡PK>斗魂卡”中输入该代码，即可挑战。");
      }
      
      public function inSave(s0:BossCardSave) : void
      {
         this.xmlStr = s0.getXMLStr();
         this.md = getMd(this.xmlStr,s0.id);
         this.code = Base64.encodeString(this.xmlStr) + "_" + this.md;
      }
      
      public function inCode(code0:String) : Boolean
      {
         this.code = code0;
         var ar:Array = code0.split("_");
         var base64:String = ar[0];
         var xmlStr0:String = Base64.decodeString(base64);
         var xml0:XML = XML(xmlStr0);
         this.xmlStr = xmlStr0;
         this.md = ar[1];
         var trueMd0:String = getMd(xmlStr0,xml0.@id);
         if(trueMd0 != this.md)
         {
            return false;
         }
         return true;
      }
      
      public function getCode() : String
      {
         return this.code;
      }
      
      public function getSave() : BossCardSave
      {
         var s0:BossCardSave = new BossCardSave();
         var xml0:XML = XML(this.xmlStr);
         s0.inData_byXML(xml0);
         return s0;
      }
      
      public function getData() : BossCardData
      {
         var da0:BossCardData = new BossCardData();
         da0.inData_bySave(this.getSave());
         return da0;
      }
   }
}

