package dataAll._app.login
{
   public class SaveData4399
   {
      
      public var base:SaveBaseData4399 = new SaveBaseData4399();
      
      public var data:Object = null;
      
      public function SaveData4399()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         this.base.inData_byObj(obj0["base"]);
         this.data = obj0["data"];
      }
   }
}

