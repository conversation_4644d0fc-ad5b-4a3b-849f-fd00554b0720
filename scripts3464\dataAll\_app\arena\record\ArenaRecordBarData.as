package dataAll._app.arena.record
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.top.TopBarData;
   
   public class ArenaRecordBarData
   {
      
      public var save:OneArenaRecordSave;
      
      public var playerName:String = "";
      
      public var winB:Boolean = false;
      
      public var num:Number = 0;
      
      public var time:String = "";
      
      public var sortIndex:Number = 0;
      
      public function ArenaRecordBarData()
      {
         super();
      }
      
      public function inData(s0:OneArenaRecordSave, winB0:Boolean) : void
      {
         this.save = s0;
         this.winB = winB0;
         this.playerName = s0.playerName;
         if(winB0)
         {
            this.num = s0.killNum;
            this.time = s0.killTime;
            this.sortIndex = s0.killSortIndex;
         }
         else
         {
            this.num = s0.dieNum;
            this.time = s0.dieTime;
            this.sortIndex = s0.dieSortIndex;
         }
      }
      
      public function getTopBarData() : TopBarData
      {
         var da0:TopBarData = this.save.topSave.getTopBarData();
         da0.rank = 0;
         da0.define = null;
         return da0;
      }
      
      public function canShowBtnB() : Boolean
      {
         var da0:TopBarData = this.getTopBarData();
         return !Gaming.PG.loginData.panUidOrUname(da0.uid,da0.uname);
      }
      
      public function getFirst() : String
      {
         return this.save.score + "";
      }
      
      public function getString() : String
      {
         var s0:String = ComMethod.color("我","#00FF00");
         var s1:String = ComMethod.color(this.playerName,"#FF6600");
         return (this.winB ? s0 : s1) + " 战胜了 " + (this.winB ? s1 : s0) + ComMethod.color(" " + this.num + "次","#00FFFF");
      }
   }
}

