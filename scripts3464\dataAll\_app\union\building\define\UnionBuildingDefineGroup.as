package dataAll._app.union.building.define
{
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class UnionBuildingDefineGroup
   {
      
      public var arr:Array = [];
      
      private var obj:Object = {};
      
      public var watchmenArr:Array = [];
      
      private var watchmenObj:Object = {};
      
      public var cookingArr:Array = [];
      
      private var cookingObj:Object = {};
      
      public var sendTaskArr:Array = [];
      
      private var sendTaskObj:Object = {};
      
      public var geologyBase:UnionGeologyDefine = new UnionGeologyDefine();
      
      public var property:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public function UnionBuildingDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.inBuildingXML(xml0.building[0]);
         this.inWatchmenXML(xml0.watchmen[0]);
         this.inCookingXML(xml0.cooking[0]);
         this.inTaskXML(xml0.sendTask[0]);
         this.inGeologyXML(xml0.geology[0]);
      }
      
      private function inBuildingXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionBuildingDefine = null;
         var xml_list0:XMLList = xml0.bu;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionBuildingDefine();
            d0.inData_byXML(x0);
            this.arr.push(d0);
            this.obj[d0.name] = d0;
         }
      }
      
      private function inWatchmenXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionWatchmenDefine = null;
         var xml_list0:XMLList = xml0.wa;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionWatchmenDefine();
            d0.inData_byXML(x0);
            d0.unlockLv = int(i) + 1;
            this.watchmenArr.push(d0);
            this.watchmenObj[d0.name] = d0;
         }
      }
      
      private function inCookingXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionCookingDefine = null;
         var xml_list0:XMLList = xml0.co;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionCookingDefine();
            d0.inData_byXML(x0);
            this.cookingArr.push(d0);
            this.cookingObj[d0.name] = d0;
         }
      }
      
      private function inGeologyXML(xml0:XML) : void
      {
         this.geologyBase.inData_byXML(xml0);
      }
      
      private function inTaskXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:UnionSendTaskDefine = null;
         var xml_list0:XMLList = xml0.task;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new UnionSendTaskDefine();
            d0.inData_byXML(x0);
            this.sendTaskArr.push(d0);
            this.sendTaskObj[d0.name] = d0;
         }
      }
      
      public function fleshDefine() : void
      {
         var d0:UnionWatchmenDefine = null;
         for each(d0 in this.watchmenArr)
         {
            d0.fleshDefine();
         }
      }
      
      public function getDefine(name0:String) : UnionBuildingDefine
      {
         return this.obj[name0];
      }
      
      public function getCookingDefine(name0:String) : UnionCookingDefine
      {
         return this.cookingObj[name0];
      }
      
      public function getSendTaskDefine(name0:String) : UnionSendTaskDefine
      {
         return this.sendTaskObj[name0];
      }
      
      public function getGeologyDefineByLv(lv0:int) : UnionGeologyDefine
      {
         var d0:UnionGeologyDefine = this.geologyBase.copy();
         d0.inLv(lv0);
         return d0;
      }
   }
}

