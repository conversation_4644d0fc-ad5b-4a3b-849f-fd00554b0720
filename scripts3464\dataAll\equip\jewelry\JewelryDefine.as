package dataAll.equip.jewelry
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.define.LvEquipDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class JewelryDefine extends LvEquipDefine
   {
      
      public static var pro_arr:Array = [];
      
      public function JewelryDefine()
      {
         super();
      }
      
      public function get maxLv() : Number
      {
         return CF.getAttribute("maxLv");
      }
      
      public function set maxLv(v0:Number) : void
      {
         CF.setAttribute("maxLv",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         father = father0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(iconLabel == "")
         {
            iconLabel = "equipIcon/" + baseLabel;
         }
         if(name == "")
         {
            name = baseLabel + "_" + lv;
         }
         type = EquipType.JEWELRY;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : JewelryDefine
      {
         var d0:JewelryDefine = new JewelryDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      public function cloneBase() : JewelryDefine
      {
         var d0:JewelryDefine = null;
         d0 = this.clone();
         d0.iconLabel = "";
         d0.name = "";
         return d0;
      }
      
      override public function getTrueCnName() : String
      {
         return cnName + lv + "级";
      }
      
      override public function getSortIndex() : int
      {
         var index0:int = index;
         var d0:JewelryDefine = Gaming.defineGroup.jewelry.getDefine(baseLabel);
         if(Boolean(d0))
         {
            index0 = d0.index;
         }
         return int(index0 * 1000 + 99 - lv);
      }
      
      public function getUpgradeName() : String
      {
         return baseLabel + "_" + (lv + 1);
      }
      
      public function getUpgradeMustThingsName() : String
      {
         return baseLabel + "_1";
      }
      
      override public function getComposeThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(this.getUpgradeMustThingsName());
      }
      
      private function getFirstSkill() : String
      {
         return skillArr[0];
      }
      
      public function getSkillDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.getFirstSkill()) as HeroSkillDefine;
      }
      
      public function getNextSkillDefine() : HeroSkillDefine
      {
         if(lv >= this.maxLv || skillArr.length == 0)
         {
            return null;
         }
         return this.getSkillDefine().getNextDefine();
      }
      
      public function getGatherTip() : String
      {
         return getNormalGatherTip();
      }
   }
}

