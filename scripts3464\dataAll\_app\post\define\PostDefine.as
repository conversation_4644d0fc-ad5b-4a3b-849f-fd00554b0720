package dataAll._app.post.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.OldObjectCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.pro.PropertyArrayDefine;
   
   public class PostDefine
   {
      
      public static const platinumMonthCard:String = "platinumMonthCard";
      
      public static const platinumMonthCard3:String = "platinumMonthCard3";
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      private var objectCF:OldObjectCF = new OldObjectCF();
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var havePresidentB:Boolean = false;
      
      public var noDeductLoveB:Boolean = false;
      
      public var autoDesB:Boolean = false;
      
      public var dropFollowB:Boolean = false;
      
      public function PostDefine()
      {
         super();
         this.bodyExpMul = 0;
         this.postExpMul = 0;
         this.lv = 0;
         this.handupTime = 0;
         this.sweepingNum = 0;
         this.loveAdd = 0;
         this.lotteryNum = 0;
         this.noDeductLoveB = false;
         this.proName = "";
      }
      
      public static function getPostName(goodsName0:String) : String
      {
         if(goodsName0 == platinumMonthCard3)
         {
            return platinumMonthCard;
         }
         return goodsName0;
      }
      
      public static function getOnlyDay(goodsName0:String) : int
      {
         if(goodsName0 == platinumMonthCard3)
         {
            return 3;
         }
         return -1;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get bodyExpMul() : Number
      {
         return this.CF.getAttribute("bodyExpMul");
      }
      
      public function set bodyExpMul(v0:Number) : void
      {
         this.CF.setAttribute("bodyExpMul",v0);
      }
      
      public function get postExpMul() : Number
      {
         return this.CF.getAttribute("postExpMul");
      }
      
      public function set postExpMul(v0:Number) : void
      {
         this.CF.setAttribute("postExpMul",v0);
      }
      
      public function get handupTime() : Number
      {
         return this.CF.getAttribute("handupTime");
      }
      
      public function set handupTime(v0:Number) : void
      {
         this.CF.setAttribute("handupTime",v0);
      }
      
      public function get sweepingNum() : Number
      {
         return this.CF.getAttribute("sweepingNum");
      }
      
      public function set sweepingNum(v0:Number) : void
      {
         this.CF.setAttribute("sweepingNum",v0);
      }
      
      public function get loveAdd() : Number
      {
         return this.CF.getAttribute("loveAdd");
      }
      
      public function set loveAdd(v0:Number) : void
      {
         this.CF.setAttribute("loveAdd",v0);
      }
      
      public function get lotteryNum() : Number
      {
         return this.CF.getAttribute("lotteryNum");
      }
      
      public function set lotteryNum(v0:Number) : void
      {
         this.CF.setAttribute("lotteryNum",v0);
      }
      
      public function get proName() : String
      {
         return this.codeCF.getAttribute("proName");
      }
      
      public function set proName(str0:String) : void
      {
         this.codeCF.setAttribute("proName",str0);
      }
      
      public function inData_byXML(xml0:XML, father0:String, lv0:int) : void
      {
         if(!xml0)
         {
            return;
         }
         this.lv = lv0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
      }
      
      public function getPostExp(onlyDay0:Number = -1) : Number
      {
         if(onlyDay0 > 0 && onlyDay0 < 30)
         {
            return 0;
         }
         return Math.ceil(this.postExpMul * 150);
      }
      
      public function getBodyExp(heroLv0:int) : Number
      {
         var exp0:Number = Gaming.defineGroup.normal.getHeroExp(heroLv0);
         return Math.ceil(exp0 / 10 * this.bodyExpMul);
      }
      
      public function getPrivilegeStr() : String
      {
         var arr0:Array = [];
         if(this.sweepingNum > 0)
         {
            arr0.push("每日扫荡次数" + ComMethod.color("+" + this.sweepingNum,"#00FF00"));
         }
         if(this.loveAdd > 0)
         {
            arr0.push("每次送礼物好感度" + ComMethod.color("+" + this.loveAdd,"#00FF00"));
         }
         if(this.lotteryNum > 0)
         {
            arr0.push("通关后抽奖次数" + ComMethod.color("+" + this.lotteryNum,"#00FF00"));
         }
         if(this.noDeductLoveB)
         {
            arr0.push("一周不送礼物不会扣除好感度");
         }
         if(this.autoDesB)
         {
            arr0.push("掉落物自动拆解(一键拆解中设置）");
         }
         if(this.dropFollowB)
         {
            arr0.push("掉落物跟踪功能");
         }
         var str0:String = "";
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            str0 += (i == 0 ? "" : "\n") + (i + 1) + "、" + arr0[i];
         }
         return str0;
      }
      
      public function getProAddObj(postLv0:int) : Object
      {
         var n:String = null;
         var nameArr0:Array = this.proName.split(",");
         var obj0:Object = {};
         for each(n in nameArr0)
         {
            obj0[n] = Gaming.defineGroup.post.proData.getPropertyValue(n,postLv0);
         }
         return obj0;
      }
      
      private function getProCnArr() : Array
      {
         var n:String = null;
         var d0:PropertyArrayDefine = null;
         var nameArr0:Array = this.proName.split(",");
         var cnArr0:Array = [];
         for each(n in nameArr0)
         {
            d0 = EquipPropertyDataCreator.getPropertyArrayDefine(n);
            cnArr0.push(d0.cnName);
         }
         return cnArr0;
      }
      
      public function getGatherTip(onlyDay0:int = -1) : String
      {
         var str0:String = "";
         str0 += "购买后将获得" + (onlyDay0 > 0 ? onlyDay0 : 30) + "天的<green " + this.cnName + "/>职务，在职期间可获得以下特权：";
         str0 += "\n\n<orange <b>职务经验：</b>/>" + this.getPostExp(onlyDay0);
         str0 += "\n\n<orange <b>每日礼包：</b>/>";
         str0 += "\n1、联邦工资包：可随机获得指定黄金道具。";
         if(this.havePresidentB)
         {
            str0 += "\n2、元首礼包：可随机获得一种双倍掉率卡片。";
         }
         str0 += "\n\n<orange <b>等级飞升：</b>/>";
         str0 += "\n1、获得" + this.bodyExpMul + "倍的人物经验奖励。";
         str0 += "\n\n<orange <b>职务特权：</b>/>";
         str0 += "\n" + this.getPrivilegeStr();
         str0 += "\n\n<orange <b>附加属性：</b>/>";
         return str0 + ("\n" + TextWay.mixedStringArr(this.getProCnArr(),99));
      }
   }
}

