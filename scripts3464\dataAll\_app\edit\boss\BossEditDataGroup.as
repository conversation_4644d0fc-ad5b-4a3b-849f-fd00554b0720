package dataAll._app.edit.boss
{
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.edit.EditSave;
   import dataAll._app.edit.EditSaveGroup;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.PlayerData;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.event.LevelEventOrderDefine;
   import dataAll.pro.dataList.DataListDefine;
   import gameAll.body.IO_NormalBody;
   
   public class BossEditDataGroup extends CodeBossEditDataGroup
   {
      
      protected var save:BossEditSaveGroup = null;
      
      protected var historyG:CodeBossEditDataGroup = new CodeBossEditDataGroup();
      
      protected var collectG:CodeBossEditDataGroup = new CodeBossEditDataGroup();
      
      protected var matchG:CodeBossEditDataGroup = null;
      
      protected var taskG:TaskBossEditDataGroup = new TaskBossEditDataGroup();
      
      protected var bossListAgent:EditListAgent = null;
      
      protected var mapListAgent:EditListAgent = null;
      
      protected var levelBoss:BossEditData = null;
      
      private var tempSummonData:BossEditData = null;
      
      public function BossEditDataGroup()
      {
         super();
         dataClass = BossEditData;
      }
      
      override public function inData_bySave(sg0:EditSaveGroup) : void
      {
         super.inData_bySave(sg0);
         this.save = sg0 as BossEditSaveGroup;
         this.historyG.inData_bySave(this.save.history);
         this.collectG.inData_bySave(this.save.collect);
      }
      
      override public function setPlayerData(pd0:PlayerData, outB0:Boolean) : void
      {
         super.setPlayerData(pd0,outB0);
         this.historyG.setPlayerData(pd0,true);
         this.collectG.setPlayerData(pd0,true);
         this.taskG.setPlayerData(pd0,true);
      }
      
      public function getSave() : BossEditSaveGroup
      {
         return this.save;
      }
      
      public function getHistory() : CodeBossEditDataGroup
      {
         return this.historyG;
      }
      
      public function getCollect() : CodeBossEditDataGroup
      {
         return this.collectG;
      }
      
      public function getTask() : TaskBossEditDataGroup
      {
         return this.taskG;
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.save.newDayCtrl();
      }
      
      public function newWeek() : void
      {
         this.save.newWeek();
      }
      
      public function openUI() : void
      {
         this.taskG.openUI(this.save,playerData);
      }
      
      public function overGamingClear() : void
      {
         var da0:BossEditData = null;
         for each(da0 in arr)
         {
            da0.overGamingClear();
         }
      }
      
      public function overLevelEvent() : void
      {
         this.tempSummonData = null;
      }
      
      public function getBarrenNum() : int
      {
         var max0:int = this.taskG.getCanMax();
         if(max0 >= 7)
         {
            return 2;
         }
         return 1;
      }
      
      public function setLevelBoss(da0:BossEditData) : void
      {
         var same0:BossEditData = null;
         this.levelBoss = da0;
         if(Boolean(da0.getTempTopData()))
         {
            if(da0.haveDataB())
            {
               same0 = this.historyG.getDataByTempCode(da0.getTempCode());
               if(same0 == null)
               {
                  this.historyG.addData(da0);
                  this.historyG.afterInHistroy();
               }
            }
         }
      }
      
      public function killBossEvent(led0:LevelEventOrderDefine) : void
      {
         var da0:BossEditData = this.levelBoss;
         if(Boolean(da0))
         {
            da0.winEvent();
            if(findDataIndex(da0) >= 0)
            {
               da0.setLockB(true);
            }
         }
      }
      
      public function getTaskDiffHave() : int
      {
         var da0:BossEditData = this.levelBoss;
         if(Boolean(da0))
         {
            if(Boolean(da0.getTaskDefine()))
            {
               return da0.getTaskDiff();
            }
         }
         return -1;
      }
      
      public function newBossData(name0:String) : BossEditData
      {
         var map0:String = null;
         var s0:EditSave = new EditSave();
         s0.setName(name0);
         var bodyD0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(name0);
         if(Boolean(bodyD0))
         {
            map0 = bodyD0.map;
            if(map0 == "")
            {
               map0 = "WoTu";
            }
            s0.setValue(BossEditPro.mp,map0);
            s0.setValue(BossEditPro.lv,playerData.level);
         }
         return addSave(s0) as BossEditData;
      }
      
      public function addBossPan() : String
      {
         var max0:int = 24;
         if(this.save.arr.length >= max0)
         {
            return "最多只能添加" + max0 + "个首领。";
         }
         return "";
      }
      
      public function mainPan(da0:BossEditData) : Boolean
      {
         return da0.getWinB() && da0.id == this.save.mainId;
      }
      
      public function setMain(da0:BossEditData) : void
      {
         this.save.mainId = da0.id;
         da0.setLockB(true);
      }
      
      public function clearMain() : void
      {
         this.save.mainId = "";
      }
      
      public function getMain() : BossEditData
      {
         var da0:BossEditData = null;
         if(Boolean(this.save))
         {
            da0 = getDataById(this.save.mainId) as BossEditData;
            if(Boolean(da0))
            {
               if(this.mainPan(da0))
               {
                  return da0;
               }
            }
            return null;
         }
         return null;
      }
      
      public function changeArmsTorDeal(name0:String) : void
      {
         var da0:BossEditData = null;
         for each(da0 in arr)
         {
            if(da0.getArmsRangeName() == name0)
            {
               da0.setWinB(false);
            }
         }
      }
      
      public function dealUploadBtn(btn0:NormalBtn) : void
      {
         btn0.actived = this.save.upNum < this.getUploadMax();
         btn0.tipString = "今日上传次数：" + ComMethod.moreRed(this.save.upNum,this.getUploadMax());
      }
      
      public function getUploadMax() : int
      {
         return 5;
      }
      
      public function uploadScoreEvent() : void
      {
         ++this.save.upNum;
      }
      
      public function getBossListAgent() : EditListAgent
      {
         var a0:EditListAgent = null;
         var fatherArr0:Array = null;
         var localB0:Boolean = false;
         var cnArr0:Array = null;
         var father0:String = null;
         var arr0:Array = null;
         var narr0:Array = null;
         var d0:NormalBodyDefine = null;
         if(Boolean(this.bossListAgent))
         {
            this.bossListAgent.clearFun();
            return this.bossListAgent;
         }
         a0 = new EditListAgent();
         fatherArr0 = BodyFather.editArr;
         localB0 = Gaming.isLocal();
         if(localB0)
         {
            fatherArr0 = fatherArr0.concat(BodyFather.bosseditLocalArr);
         }
         cnArr0 = BodyFather.getCnArrByArr(fatherArr0);
         a0.inTitle(fatherArr0,cnArr0);
         a0.info = "选择一个单位作为首领";
         for each(father0 in fatherArr0)
         {
            arr0 = Gaming.defineGroup.body.getArrByFather(father0);
            narr0 = [];
            for each(d0 in arr0)
            {
               if(localB0 || d0.showLevel < 9999 && d0.canBossB)
               {
                  a0.addDataLast(d0,father0);
               }
            }
         }
         a0.createAllText();
         this.bossListAgent = a0;
         return a0;
      }
      
      public function getMapListAgent() : EditListAgent
      {
         var a0:EditListAgent = null;
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var father0:String = null;
         var arr0:Array = null;
         var d0:WorldMapDefine = null;
         if(Boolean(this.mapListAgent))
         {
            this.mapListAgent.clearFun();
            return this.mapListAgent;
         }
         a0 = new EditListAgent();
         fatherArr0 = WorldMapDefine.EDIT_ARR;
         cnArr0 = ["大地图","秘境","任务"];
         a0.inTitle(fatherArr0,cnArr0);
         a0.info = "选择地图";
         for each(father0 in fatherArr0)
         {
            arr0 = Gaming.defineGroup.worldMap.getArrByFather(father0);
            for each(d0 in arr0)
            {
               if(d0.canEditB())
               {
                  a0.addDataLast(d0,father0);
               }
            }
         }
         a0.createAllText();
         this.mapListAgent = a0;
         return a0;
      }
      
      public function getCnListAgent() : EditListAgent
      {
         return BossEditSkill.getCnAgent();
      }
      
      public function setTempSummonData(da0:BossEditData) : void
      {
         this.tempSummonData = da0;
      }
      
      public function getSummonArr(levelD0:LevelDefine) : Array
      {
         var levelName0:String = null;
         var main0:BossEditData = null;
         var arr0:Array = [];
         if(Boolean(levelD0))
         {
            levelName0 = levelD0.name;
            if(levelName0 == BossEditMethod.semmonBoss)
            {
               if(Boolean(this.tempSummonData))
               {
                  arr0.push(this.tempSummonData);
               }
            }
            else if(levelName0 == BossEditMethod.sumBossTask)
            {
               main0 = this.getMain();
               if(Boolean(main0) && main0.sumPan())
               {
                  arr0.push(main0);
               }
               else
               {
                  arr0.push(this.getSumBossTaskInit());
               }
            }
         }
         return arr0;
      }
      
      public function getSummonBodyArr() : Array
      {
         var b0:IO_NormalBody = null;
         var arr2:Array = [];
         if(Boolean(this.tempSummonData))
         {
            b0 = this.tempSummonData.getSumBody();
            if(Boolean(b0))
            {
               arr2.push(b0);
            }
         }
         return arr2;
      }
      
      public function getSumBossTaskInit() : BossEditData
      {
         return BossEditMethod.getTaskBoss(playerData.level);
      }
      
      public function getMatchG() : CodeBossEditDataGroup
      {
         var list0:DataListDefine = null;
         var larr0:Array = null;
         if(this.matchG == null)
         {
            this.matchG = new CodeBossEditDataGroup();
            this.matchG.inData_bySave(new EditSaveGroup());
            list0 = Gaming.defineGroup.dataList.getDefine("bossMatchList");
            larr0 = list0.getTrueValueArr().concat();
            larr0.reverse();
            this.matchG.newDataByCodeArr(larr0);
         }
         return this.matchG;
      }
   }
}

