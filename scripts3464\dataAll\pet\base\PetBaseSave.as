package dataAll.pet.base
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.save.GeneSave;
   
   public class PetBaseSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var addArr:Array = ["dps","life","head"];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var id:String = "";
      
      public var name:String = "";
      
      private var _level:String = "";
      
      public var playerName:String = "";
      
      public var suppleFunB:Boolean = false;
      
      public function PetBaseSave()
      {
         super();
         this.level = 1;
         this.exp = 0;
         this.lifeAdd = PetCount.getFirstLevel();
         this.dpsAdd = this.lifeAdd;
         this.headAdd = this.lifeAdd;
      }
      
      public function get exp() : Number
      {
         return this.CF.getAttribute("exp");
      }
      
      public function set exp(v0:Number) : void
      {
         this.CF.setAttribute("exp",v0);
      }
      
      public function get lifeAdd() : Number
      {
         return this.CF.getAttribute("lifeAdd");
      }
      
      public function set lifeAdd(v0:Number) : void
      {
         this.CF.setAttribute("lifeAdd",v0);
      }
      
      public function get dpsAdd() : Number
      {
         return this.CF.getAttribute("dpsAdd");
      }
      
      public function set dpsAdd(v0:Number) : void
      {
         this.CF.setAttribute("dpsAdd",v0);
      }
      
      public function get headAdd() : Number
      {
         return this.CF.getAttribute("headAdd");
      }
      
      public function set headAdd(v0:Number) : void
      {
         this.CF.setAttribute("headAdd",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.playerName = TextWay.clearHtmlNo(this.playerName);
      }
      
      public function initData_byGeneData(geneDa0:GeneData) : void
      {
         var s0:GeneSave = geneDa0.save;
         var d0:GeneDefine = s0.getDefine();
         this.name = s0.name;
         this.playerName = d0.cnName;
         this.level = s0.itemsLevel;
      }
      
      public function setIdBy(idIndex0:int) : void
      {
         this.id = idIndex0 + "";
      }
      
      public function set level(v0:Number) : void
      {
         this._level = Sounto64.encode(String(v0));
      }
      
      public function get level() : Number
      {
         return Number(Sounto64.decode(this._level));
      }
      
      public function getAddLv(name0:String) : int
      {
         return this[name0 + "Add"];
      }
      
      public function setAddLv(name0:String, lv0:int) : void
      {
         this[name0 + "Add"] = lv0;
      }
      
      public function addAddLv(name0:String, lv0:int = 1) : void
      {
         this[name0 + "Add"] += lv0;
      }
      
      public function transferOther(s0:PetBaseSave) : void
      {
         var name0:String = null;
         var v0:int = 0;
         for each(name0 in addArr)
         {
            v0 = this.getAddLv(name0);
            this.setAddLv(name0,s0.getAddLv(name0));
            s0.setAddLv(name0,v0);
         }
      }
   }
}

