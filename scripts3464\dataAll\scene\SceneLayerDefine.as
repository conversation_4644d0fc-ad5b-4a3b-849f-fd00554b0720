package dataAll.scene
{
   import dataAll._data.ConstantDefine;
   
   public class SceneLayerDefine
   {
      
      public var imgName:String = "";
      
      public var width:int;
      
      public var height:int;
      
      public var moveRateX:Number;
      
      public var moveRateY:Number;
      
      public var beforeMoveRateX:Number;
      
      public var containerName:String;
      
      public var cx:Number = 0;
      
      public var cy:Number = 0;
      
      public function SceneLayerDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.imgName = xml0.imgName;
         this.width = int(xml0.width);
         this.height = int(xml0.height);
         this.moveRateX = Number(xml0.moveRateX);
         this.moveRateY = Number(xml0.moveRateY);
         this.containerName = String(xml0.containerName);
         if(this.containerName == "")
         {
            this.containerName = "back";
         }
         if(this.height > 0 && this.height < ConstantDefine.HEIGHT)
         {
            this.height = ConstantDefine.HEIGHT;
         }
      }
   }
}

