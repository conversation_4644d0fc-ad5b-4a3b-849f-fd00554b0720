package dataAll._app.union.building
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.building.define.UnionBuildingDefine;
   
   public class UnionBuildingSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public function UnionBuildingSave()
      {
         super();
         this.lv = 1;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get exp() : Number
      {
         return this.CF.getAttribute("exp");
      }
      
      public function set exp(v0:Number) : void
      {
         this.CF.setAttribute("exp",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(this.lv < 1)
         {
            this.lv = 1;
         }
      }
      
      public function inDataByDefine(d0:UnionBuildingDefine) : void
      {
         this.name = d0.name;
      }
      
      public function getAllSuppliesNum() : int
      {
         var lv0:int = this.lv;
         var v0:Number = 0;
         for(var i:int = 1; i < lv0; i++)
         {
            v0 += Gaming.defineGroup.union.building.property.getPropertyValue("upgradeMust",i);
         }
         return v0;
      }
   }
}

