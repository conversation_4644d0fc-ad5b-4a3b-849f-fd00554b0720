package dataAll._app.post
{
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.post.define.PostDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class PostSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var MAX_LEVEL:int = 10;
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var cardRecordArr:Array = [];
      
      public var allCardRecordArr:Array = [];
      
      public var tipStr:String = "";
      
      public var dropB:Boolean = true;
      
      public function PostSave()
      {
         super();
         this.nowPost = "";
         this.firstTime = "";
         this.extraDayNum = 0;
         this.postExp = 0;
         this.postLv = 0;
         this.giftTime = "";
         this.expTime = "";
         this.expDay = 0;
         this.bribe = 0;
         this.normalGiftBribe = 0;
         this.superGiftBribe = 0;
         this.normalGiftAdd = 0;
         this.superGiftAdd = 0;
      }
      
      public function get nowPost() : String
      {
         return this.codeCF.getAttribute("nowPost");
      }
      
      public function set nowPost(str0:String) : void
      {
         this.codeCF.setAttribute("nowPost",str0);
      }
      
      public function get postExp() : Number
      {
         return this.CF.getAttribute("postExp");
      }
      
      public function set postExp(v0:Number) : void
      {
         this.CF.setAttribute("postExp",v0);
      }
      
      public function get postLv() : Number
      {
         return this.CF.getAttribute("postLv");
      }
      
      public function set postLv(v0:Number) : void
      {
         this.CF.setAttribute("postLv",v0);
      }
      
      public function get firstTime() : String
      {
         return this.codeCF.getAttribute("firstTime");
      }
      
      public function set firstTime(str0:String) : void
      {
         this.codeCF.setAttribute("firstTime",str0);
      }
      
      public function get extraDayNum() : Number
      {
         return this.CF.getAttribute("extraDayNum");
      }
      
      public function set extraDayNum(v0:Number) : void
      {
         this.CF.setAttribute("extraDayNum",v0);
      }
      
      public function get totalGiftExp() : Number
      {
         return this.CF.getAttribute("totalGiftExp");
      }
      
      public function set totalGiftExp(v0:Number) : void
      {
         this.CF.setAttribute("totalGiftExp",v0);
      }
      
      public function get expTime() : String
      {
         return this.codeCF.getAttribute("expTime");
      }
      
      public function set expTime(str0:String) : void
      {
         this.codeCF.setAttribute("expTime",str0);
      }
      
      public function get expDay() : Number
      {
         return this.CF.getAttribute("expDay");
      }
      
      public function set expDay(v0:Number) : void
      {
         this.CF.setAttribute("expDay",v0);
      }
      
      public function get giftTime() : String
      {
         return this.codeCF.getAttribute("giftTime");
      }
      
      public function set giftTime(str0:String) : void
      {
         this.codeCF.setAttribute("giftTime",str0);
      }
      
      public function get bribe() : Number
      {
         return this.CF.getAttribute("bribe");
      }
      
      public function set bribe(v0:Number) : void
      {
         this.CF.setAttribute("bribe",v0);
      }
      
      public function get normalGiftBribe() : Number
      {
         return this.CF.getAttribute("normalGiftBribe");
      }
      
      public function set normalGiftBribe(v0:Number) : void
      {
         this.CF.setAttribute("normalGiftBribe",v0);
      }
      
      public function get superGiftBribe() : Number
      {
         return this.CF.getAttribute("superGiftBribe");
      }
      
      public function set superGiftBribe(v0:Number) : void
      {
         this.CF.setAttribute("superGiftBribe",v0);
      }
      
      public function get normalGiftAdd() : Number
      {
         return this.CF.getAttribute("normalGiftAdd");
      }
      
      public function set normalGiftAdd(v0:Number) : void
      {
         this.CF.setAttribute("normalGiftAdd",v0);
      }
      
      public function get superGiftAdd() : Number
      {
         return this.CF.getAttribute("superGiftAdd");
      }
      
      public function set superGiftAdd(v0:Number) : void
      {
         this.CF.setAttribute("superGiftAdd",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function openUI(timeStr0:String, heroLv0:int) : void
      {
         var day0:int = this.getSurplusDay(timeStr0);
         if(day0 <= 0)
         {
            if(this.firstTime != "")
            {
               this.tipStr = "你的职务已过期。";
            }
            this.clearPost();
         }
         if(this.nowPost != "")
         {
            this.bodyExpPan(timeStr0,heroLv0,false);
            this.giftPan(timeStr0);
         }
      }
      
      public function getDefine() : PostDefine
      {
         return Gaming.defineGroup.post.getDefine(this.nowPost);
      }
      
      private function clearPost() : void
      {
         this.nowPost = "";
         this.firstTime = "";
         this.extraDayNum = 0;
         this.postExp = 0;
         this.cardRecordArr.length = 0;
         this.totalGiftExp = 0;
         this.expDay = 0;
         this.expTime = "";
         this.bribe = 0;
         this.giftTime = "";
         this.normalGiftBribe = 0;
         this.normalGiftAdd = 0;
         this.superGiftBribe = 0;
         this.superGiftAdd = 0;
      }
      
      public function useCardDefine(d0:PostDefine, num0:int, timeStr0:String, heroLv0:int, onlyDay0:Number = -1) : void
      {
         var noToHaveB0:Boolean = this.nowPost == "";
         var newPostB0:Boolean = d0.name != this.nowPost;
         var extraDay0:int = this.converNewPostDay(timeStr0,d0.name);
         this.nowPost = d0.name;
         if(newPostB0)
         {
            this.firstTime = timeStr0;
            this.cardRecordArr.length = 0;
            this.extraDayNum = extraDay0;
         }
         if(onlyDay0 > 0)
         {
            this.extraDayNum += onlyDay0 * num0;
            this.allCardRecordArr.push("【" + timeStr0 + "】" + d0.name + onlyDay0 + "*" + num0);
         }
         else
         {
            this.inCardRecord(d0.name,num0,timeStr0,extraDay0);
         }
         this.addPostExp(d0.getPostExp(onlyDay0) * num0);
         this.bodyExpPan(timeStr0,heroLv0,true);
         if(noToHaveB0)
         {
            this.giftTime = new StringDate(timeStr0).addDay(-1).getStr();
         }
      }
      
      public function converNewPostDay(timeStr0:String, newPost0:String) : int
      {
         var goodD0:GoodsDefine = null;
         var nowPrice0:Number = NaN;
         var newGoodsD0:GoodsDefine = null;
         var day0:int = 0;
         var nowD0:PostDefine = this.getDefine();
         if(Boolean(nowD0))
         {
            goodD0 = Gaming.defineGroup.goods.getDefine(nowD0.name);
            nowPrice0 = goodD0.price / 30 * this.getSurplusDay(timeStr0);
            newGoodsD0 = Gaming.defineGroup.goods.getDefine(newPost0);
            return int(nowPrice0 / (newGoodsD0.price / 30) * 0.5);
         }
         return 0;
      }
      
      private function inCardRecord(name0:String, num0:int, timeStr0:String, extraDay0:int) : void
      {
         var str0:String = null;
         for(var i:int = 0; i < num0; i++)
         {
            str0 = "(" + timeStr0 + ") " + name0 + "+" + extraDay0;
            this.cardRecordArr.push(str0);
            this.allCardRecordArr.push(str0);
         }
      }
      
      public function getSurplusDay(timeStr0:String) : int
      {
         var first0:StringDate = null;
         var now0:StringDate = null;
         var extra0:int = 0;
         var have0:int = 0;
         var len0:int = 0;
         var firstTime0:String = this.firstTime;
         if(firstTime0 == "")
         {
            return 0;
         }
         first0 = new StringDate(firstTime0);
         now0 = new StringDate(timeStr0);
         extra0 = this.extraDayNum;
         have0 = now0.reductionOne(first0);
         len0 = this.cardRecordArr.length * 30;
         return len0 - have0 + extra0;
      }
      
      public function addPostExp(v0:Number) : Boolean
      {
         v0 = Math.round(v0);
         var level0:int = this.postLv;
         if(level0 < 1)
         {
            level0 = 1;
         }
         var level2:int = level0;
         var max0:Number = this.getMaxPostExp(level0);
         var now0:Number = this.postExp;
         if(level0 >= MAX_LEVEL)
         {
            now0 += v0;
            this.postExp = now0;
         }
         else
         {
            now0 += v0;
            while(now0 >= max0)
            {
               now0 -= max0;
               level0++;
               if(level0 >= MAX_LEVEL)
               {
                  break;
               }
               max0 = this.getMaxPostExp(level0);
            }
            if(level0 >= MAX_LEVEL)
            {
               if(now0 > max0)
               {
                  now0 = max0 - 1;
               }
            }
            this.postExp = now0;
            if(level0 > level2)
            {
               this.postLv = level0;
               return true;
            }
         }
         return false;
      }
      
      public function getNowMaxPostExp() : int
      {
         var lv0:int = this.postLv;
         if(lv0 < 0)
         {
            lv0 = 0;
         }
         return this.getMaxPostExp(lv0);
      }
      
      private function getMaxPostExp(lv0:int) : int
      {
         return Gaming.defineGroup.post.proData.getPropertyValue("postExp",lv0);
      }
      
      private function bodyExpPan(timeStr0:String, heroLv0:int, noToHaveB0:Boolean) : void
      {
         var expTime0:String = null;
         var c0:int = 0;
         var addExp0:Number = NaN;
         if(this.expDay < 7)
         {
            expTime0 = this.expTime;
            if(expTime0 == "")
            {
               expTime0 = timeStr0;
            }
            c0 = StringDate.compareDateByStr(expTime0,timeStr0);
            if(c0 > 0)
            {
               addExp0 = this.getDefine().getBodyExp(heroLv0) * c0;
               this.totalGiftExp += addExp0;
               ++this.expDay;
               expTime0 = timeStr0;
            }
            else if(noToHaveB0)
            {
               this.totalGiftExp = this.getDefine().getBodyExp(heroLv0);
               this.expDay = 1;
               expTime0 = timeStr0;
            }
            this.expTime = expTime0;
         }
      }
      
      public function getBodyExpEvent(timeStr0:String) : void
      {
         this.expTime = timeStr0;
         this.expDay = 0;
         this.totalGiftExp = 0;
      }
      
      private function giftPan(timeStr0:String) : void
      {
         var giftTime0:String = this.giftTime;
         if(giftTime0 == "")
         {
            giftTime0 = timeStr0;
         }
         var c0:int = StringDate.compareDateByStr(giftTime0,timeStr0);
         var bribe0:int = this.bribe;
         if(c0 >= 1 && bribe0 > 0)
         {
            this.tipStr = "你游说成功了！你将额外获得：\n" + this.normalGiftBribe + "个联邦工资包";
            if(this.superGiftBribe > 0 && this.getDefine().havePresidentB)
            {
               this.tipStr += "、" + this.superGiftBribe + "个元首礼包。";
            }
            else
            {
               this.tipStr += "。";
            }
            this.normalGiftAdd += this.normalGiftBribe;
            this.superGiftAdd += this.superGiftBribe;
            bribe0 = 0;
            this.normalGiftBribe = 0;
            this.superGiftBribe = 0;
         }
         this.bribe = bribe0;
      }
      
      public function getGift(timeStr0:*) : GiftAddDefineGroup
      {
         var giftTime0:String = this.giftTime;
         if(giftTime0 == "")
         {
            giftTime0 = timeStr0;
         }
         var c0:int = StringDate.compareDateByStr(giftTime0,timeStr0);
         if(c0 > 7)
         {
            c0 = 7;
         }
         var num1:int = c0 + this.normalGiftAdd;
         var num2:int = c0 + this.superGiftAdd;
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         if(num1 > 0)
         {
            gift0.addGiftByStr("things;postNormalChest;" + num1);
         }
         if(num2 > 0 && this.getDefine().havePresidentB)
         {
            gift0.addGiftByStr("things;postPlatinumChest;" + num2);
         }
         return gift0;
      }
      
      public function addBribe(v0:int) : void
      {
         this.bribe += v0;
         this.normalGiftBribe += int((1 + Math.random() * 3) * v0);
         if(this.getDefine().havePresidentB)
         {
            this.superGiftBribe += int((1 + Math.random() * 2) * v0);
         }
      }
      
      public function getGiftEvent(timeStr0:String) : void
      {
         this.normalGiftAdd = 0;
         this.superGiftAdd = 0;
         this.giftTime = timeStr0;
      }
   }
}

