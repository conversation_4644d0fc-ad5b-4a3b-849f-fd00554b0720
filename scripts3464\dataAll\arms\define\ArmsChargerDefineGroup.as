package dataAll.arms.define
{
   import dataAll._base.NormalDefineGroup;
   import dataAll.arms.*;
   import dataAll.arms.creator.*;
   import dataAll.arms.skin.ArmsSkinDefine;
   
   public class ArmsChargerDefineGroup
   {
      
      public var obj:Object = {};
      
      public var typeArr:Array = [];
      
      public var joinPropertyArr:Array = [];
      
      public var skin:NormalDefineGroup = new NormalDefineGroup();
      
      public function ArmsChargerDefineGroup()
      {
         super();
         this.skin.defineClass = ArmsSkinDefine;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var charger_xml0:XML = null;
         var d0:ArmsChargerDefine = null;
         var xml_list0:XMLList = xml0.charger;
         for(n in xml_list0)
         {
            charger_xml0 = xml_list0[n];
            d0 = new ArmsChargerDefine();
            d0.inData_byXML(charger_xml0);
            this.obj[d0.name] = d0;
            this.typeArr.push(d0.name);
            if(d0.joinProperty == 1)
            {
               this.joinPropertyArr.push(d0.name);
            }
         }
      }
      
      public function getDefine(name0:String) : ArmsChargerDefine
      {
         return this.obj[name0];
      }
      
      public function getCn(name0:String) : String
      {
         var d0:ArmsChargerDefine = this.getDefine(name0);
         if(d0 is ArmsChargerDefine)
         {
            return d0.cnName;
         }
         return "";
      }
      
      public function getSkinByUrl(url0:String) : ArmsSkinDefine
      {
         var d0:ArmsSkinDefine = null;
         var arr0:Array = this.skin.arr;
         for each(d0 in arr0)
         {
            if(d0.getUrl() == url0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getSkin(name0:String) : ArmsSkinDefine
      {
         return this.skin.getNormalDefine(name0) as ArmsSkinDefine;
      }
      
      public function getSkinArr(armsName0:String) : Array
      {
         if(armsName0 == "pistol2")
         {
            armsName0 = "pistol1";
         }
         return this.skin.getArrByFather(armsName0);
      }
   }
}

