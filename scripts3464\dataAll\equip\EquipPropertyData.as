package dataAll.equip
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.define.EquipPro;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipPropertyData
   {
      
      public static var pro_arr:Array = [];
      
      public static var zeroVipDef:VipLevelDefine = new VipLevelDefine();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var V:Number = Math.random() / 5 + 0.01;
      
      public var vipDef:VipLevelDefine = zeroVipDef;
      
      private var _dpsWhole:Number = 0;
      
      private var _dps:Number = 0;
      
      private var _dps_rifle:Number = 0;
      
      private var _dps_sniper:Number = 0;
      
      private var _dps_shotgun:Number = 0;
      
      private var _dps_pistol:Number = 0;
      
      private var _dps_rocket:Number = 0;
      
      private var _dps_crossbow:Number = 0;
      
      private var _dps_flamer:Number = 0;
      
      private var _dpsMul:Number = 0;
      
      private var _dpsMul_rifle:Number = 0;
      
      private var _dpsMul_sniper:Number = 0;
      
      private var _dpsMul_shotgun:Number = 0;
      
      private var _dpsMul_pistol:Number = 0;
      
      private var _dpsMul_rocket:Number = 0;
      
      private var _dpsMul_crossbow:Number = 0;
      
      private var _dpsMul_flamer:Number = 0;
      
      private var _dpsMul_wavegun:Number = 0;
      
      private var _dpsAll:Number = 0;
      
      private var _hurt:Number = 0;
      
      private var _hurt_rifle:Number = 0;
      
      private var _hurt_sniper:Number = 0;
      
      private var _hurt_shotgun:Number = 0;
      
      private var _hurt_pistol:Number = 0;
      
      private var _hurt_rocket:Number = 0;
      
      private var _hurt_crossbow:Number = 0;
      
      private var _hurt_flamer:Number = 0;
      
      private var _hurtMul:Number = 0;
      
      private var _hurtMul_rifle:Number = 0;
      
      private var _hurtMul_sniper:Number = 0;
      
      private var _hurtMul_shotgun:Number = 0;
      
      private var _hurtMul_pistol:Number = 0;
      
      private var _hurtMul_rocket:Number = 0;
      
      private var _hurtMul_crossbow:Number = 0;
      
      private var _hurtMul_flamer:Number = 0;
      
      private var _hurtAll:Number = 0;
      
      private var _capacity:Number = 0;
      
      private var _capacity_rifle:Number = 0;
      
      private var _capacity_sniper:Number = 0;
      
      private var _capacity_shotgun:Number = 0;
      
      private var _capacity_pistol:Number = 0;
      
      private var _capacity_rocket:Number = 0;
      
      private var _capacity_crossbow:Number = 0;
      
      private var _capacity_flamer:Number = 0;
      
      private var _capacityMul:Number = 0;
      
      private var _capacityMul_rifle:Number = 0;
      
      private var _capacityMul_sniper:Number = 0;
      
      private var _capacityMul_shotgun:Number = 0;
      
      private var _capacityMul_pistol:Number = 0;
      
      private var _capacityMul_rocket:Number = 0;
      
      private var _capacityMul_crossbow:Number = 0;
      
      private var _capacityMul_flamer:Number = 0;
      
      private var _charger:Number = 0;
      
      private var _charger_rifle:Number = 0;
      
      private var _charger_sniper:Number = 0;
      
      private var _charger_shotgun:Number = 0;
      
      private var _charger_pistol:Number = 0;
      
      private var _charger_rocket:Number = 0;
      
      private var _charger_crossbow:Number = 0;
      
      private var _charger_flamer:Number = 0;
      
      private var _chargerMul:Number = 0;
      
      private var _chargerMul_rifle:Number = 0;
      
      private var _chargerMul_sniper:Number = 0;
      
      private var _chargerMul_shotgun:Number = 0;
      
      private var _chargerMul_pistol:Number = 0;
      
      private var _chargerMul_rocket:Number = 0;
      
      private var _chargerMul_crossbow:Number = 0;
      
      private var _chargerMul_flamer:Number = 0;
      
      private var _reload:Number = 0;
      
      private var _reload_rifle:Number = 0;
      
      private var _reload_sniper:Number = 0;
      
      private var _reload_shotgun:Number = 0;
      
      private var _reload_pistol:Number = 0;
      
      private var _reload_rocket:Number = 0;
      
      private var _reload_crossbow:Number = 0;
      
      private var _reload_flamer:Number = 0;
      
      private var _lifeAll:Number = 0;
      
      private var _life:Number = 0;
      
      private var _lifeMul:Number = 0;
      
      private var _lifeRate:Number = 0;
      
      private var _lifeRateMul:Number = 0;
      
      private var _head:Number = 0;
      
      private var _headMul:Number = 0;
      
      private var _coin:Number = 0;
      
      private var _coinMul:Number = 0;
      
      private var _exp:Number = 0;
      
      private var _expMul:Number = 0;
      
      private var _prestige:Number = 0;
      
      private var _attackGap:Number = 0;
      
      private var _critPro3:Number = 0;
      
      private var _dodge:Number = 0;
      
      private var _fightDedut:Number = 0;
      
      private var _moveMul:Number = 0;
      
      private var _cdMul:Number = 0;
      
      private var _bulletDedut:Number = 0;
      
      private var _skillDedut:Number = 0;
      
      private var _orredArmsDropPro:Number = 0;
      
      private var _orredEquipDropPro:Number = 0;
      
      private var _rareArmsDropPro:Number = 0;
      
      private var _rareEquipDropPro:Number = 0;
      
      private var _arenaStampDropNum:Number = 0;
      
      private var _vehicleCashDropNum:Number = 0;
      
      private var _lifeCatalystDropPro:Number = 0;
      
      private var _godStoneDropPro:Number = 0;
      
      private var _converStoneDropPro:Number = 0;
      
      private var _taxStampDropPro:Number = 0;
      
      private var _weaponDropPro:Number = 0;
      
      private var _deviceDropPro:Number = 0;
      
      private var _bloodStoneDropPro:Number = 0;
      
      private var _blackArmsDropPro:Number = 0;
      
      private var _ranBlackArmsDropPro:Number = 0;
      
      private var _blackEquipDropPro:Number = 0;
      
      private var _gemDropPro:Number = 0;
      
      private var _specialPartsDropPro:Number = 0;
      
      private var _rareGeneDropPro:Number = 0;
      
      private var _petBookDropPro:Number = 0;
      
      private var _damageMul:Number = 0;
      
      private var _moreDpsMul:Number = 0;
      
      private var _moreLifeMul:Number = 0;
      
      private var _vehicleDefMul:Number = 0;
      
      private var _vehicleDpsMul:Number = 0;
      
      private var _zodiacArmsHurtAdd:Number = 0;
      
      private var _dpsAllBlack:Number = 0;
      
      private var _lifeAllBlack:Number = 0;
      
      private var _lifeRateBlack:Number = 0;
      
      private var _capacityMulBlack:Number = 0;
      
      private var _chargerMulBlack:Number = 0;
      
      private var _maxJumpNumAdd:Number = 0;
      
      private var _fgNE:Number = 0;
      
      private var _dayLoveAdd:Number = 0;
      
      public var madheartDropNum:Number = 0;
      
      private var _hurtMul_wavegun:Number = 0;
      
      public function EquipPropertyData()
      {
         super();
         this.lottery = 0;
         this.loveAdd = 0;
         this.sweepingNum = 0;
      }
      
      public function set dpsWhole(v0:Number) : void
      {
         this._dpsWhole = v0 / this.V;
      }
      
      public function get dpsWhole() : Number
      {
         return this._dpsWhole * this.V;
      }
      
      public function set lifeRateMul(v0:Number) : void
      {
         this._lifeRateMul = v0 / this.V;
      }
      
      public function get lifeRateMul() : Number
      {
         return this._lifeRateMul * this.V;
      }
      
      public function set attackGap(v0:Number) : void
      {
         this._attackGap = v0 / this.V;
      }
      
      public function get attackGap() : Number
      {
         return this._attackGap * this.V;
      }
      
      public function set critPro3(v0:Number) : void
      {
         this._critPro3 = v0 / this.V;
      }
      
      public function get critPro3() : Number
      {
         return this._critPro3 * this.V;
      }
      
      public function set dodge(v0:Number) : void
      {
         this._dodge = v0 / this.V;
      }
      
      public function get dodge() : Number
      {
         return this._dodge * this.V;
      }
      
      public function set fightDedut(v0:Number) : void
      {
         this._fightDedut = v0 / this.V;
      }
      
      public function get fightDedut() : Number
      {
         return this._fightDedut * this.V;
      }
      
      public function set moveMul(v0:Number) : void
      {
         this._moveMul = v0 / this.V;
      }
      
      public function get moveMul() : Number
      {
         return this._moveMul * this.V;
      }
      
      public function set cdMul(v0:Number) : void
      {
         this._cdMul = v0 / this.V;
      }
      
      public function get cdMul() : Number
      {
         return this._cdMul * this.V;
      }
      
      public function set bulletDedut(v0:Number) : void
      {
         this._bulletDedut = v0 / this.V;
      }
      
      public function get bulletDedut() : Number
      {
         return this._bulletDedut * this.V;
      }
      
      public function set skillDedut(v0:Number) : void
      {
         this._skillDedut = v0 / this.V;
      }
      
      public function get skillDedut() : Number
      {
         return this._skillDedut * this.V;
      }
      
      public function set orredArmsDropPro(v0:Number) : void
      {
         this._orredArmsDropPro = v0 / this.V;
      }
      
      public function get orredArmsDropPro() : Number
      {
         return this._orredArmsDropPro * this.V;
      }
      
      public function set orredEquipDropPro(v0:Number) : void
      {
         this._orredEquipDropPro = v0 / this.V;
      }
      
      public function get orredEquipDropPro() : Number
      {
         return this._orredEquipDropPro * this.V;
      }
      
      public function set rareArmsDropPro(v0:Number) : void
      {
         this._rareArmsDropPro = v0 / this.V;
      }
      
      public function get rareArmsDropPro() : Number
      {
         return this._rareArmsDropPro * this.V;
      }
      
      public function set rareEquipDropPro(v0:Number) : void
      {
         this._rareEquipDropPro = v0 / this.V;
      }
      
      public function get rareEquipDropPro() : Number
      {
         return this._rareEquipDropPro * this.V;
      }
      
      public function set arenaStampDropNum(v0:Number) : void
      {
         this._arenaStampDropNum = v0 / this.V;
      }
      
      public function get arenaStampDropNum() : Number
      {
         return this._arenaStampDropNum * this.V;
      }
      
      public function set vehicleCashDropNum(v0:Number) : void
      {
         this._vehicleCashDropNum = v0 / this.V;
      }
      
      public function get vehicleCashDropNum() : Number
      {
         return this._vehicleCashDropNum * this.V;
      }
      
      public function set lifeCatalystDropPro(v0:Number) : void
      {
         this._lifeCatalystDropPro = v0 / this.V;
      }
      
      public function get lifeCatalystDropPro() : Number
      {
         return this._lifeCatalystDropPro * this.V;
      }
      
      public function set godStoneDropPro(v0:Number) : void
      {
         this._godStoneDropPro = v0 / this.V;
      }
      
      public function get godStoneDropPro() : Number
      {
         return this._godStoneDropPro * this.V;
      }
      
      public function set converStoneDropPro(v0:Number) : void
      {
         this._converStoneDropPro = v0 / this.V;
      }
      
      public function get converStoneDropPro() : Number
      {
         return this._converStoneDropPro * this.V;
      }
      
      public function set taxStampDropPro(v0:Number) : void
      {
         this._taxStampDropPro = v0 / this.V;
      }
      
      public function get taxStampDropPro() : Number
      {
         return this._taxStampDropPro * this.V;
      }
      
      public function set weaponDropPro(v0:Number) : void
      {
         this._weaponDropPro = v0 / this.V;
      }
      
      public function get weaponDropPro() : Number
      {
         return this._weaponDropPro * this.V;
      }
      
      public function set deviceDropPro(v0:Number) : void
      {
         this._deviceDropPro = v0 / this.V;
      }
      
      public function get deviceDropPro() : Number
      {
         return this._deviceDropPro * this.V;
      }
      
      public function set bloodStoneDropPro(v0:Number) : void
      {
         this._bloodStoneDropPro = v0 / this.V;
      }
      
      public function get bloodStoneDropPro() : Number
      {
         return this._bloodStoneDropPro * this.V;
      }
      
      public function set blackArmsDropPro(v0:Number) : void
      {
         this._blackArmsDropPro = v0 / this.V;
      }
      
      public function get blackArmsDropPro() : Number
      {
         return this._blackArmsDropPro * this.V;
      }
      
      public function set ranBlackArmsDropPro(v0:Number) : void
      {
         this._ranBlackArmsDropPro = v0 / this.V;
      }
      
      public function get ranBlackArmsDropPro() : Number
      {
         return this._ranBlackArmsDropPro * this.V;
      }
      
      public function set blackEquipDropPro(v0:Number) : void
      {
         this._blackEquipDropPro = v0 / this.V;
      }
      
      public function get blackEquipDropPro() : Number
      {
         return this._blackEquipDropPro * this.V;
      }
      
      public function set gemDropPro(v0:Number) : void
      {
         this._gemDropPro = v0 / this.V;
      }
      
      public function get gemDropPro() : Number
      {
         return this._gemDropPro * this.V;
      }
      
      public function set specialPartsDropPro(v0:Number) : void
      {
         this._specialPartsDropPro = v0 / this.V;
      }
      
      public function get specialPartsDropPro() : Number
      {
         return this._specialPartsDropPro * this.V;
      }
      
      public function set rareGeneDropPro(v0:Number) : void
      {
         this._rareGeneDropPro = v0 / this.V;
      }
      
      public function get rareGeneDropPro() : Number
      {
         return this._rareGeneDropPro * this.V;
      }
      
      public function set petBookDropPro(v0:Number) : void
      {
         this._petBookDropPro = v0 / this.V;
      }
      
      public function get petBookDropPro() : Number
      {
         return this._petBookDropPro * this.V;
      }
      
      public function get lottery() : Number
      {
         return this.CF.getAttribute("lottery");
      }
      
      public function set lottery(v0:Number) : void
      {
         this.CF.setAttribute("lottery",v0);
      }
      
      public function set damageMul(v0:Number) : void
      {
         this._damageMul = v0 / this.V;
      }
      
      public function get damageMul() : Number
      {
         return this._damageMul * this.V;
      }
      
      public function get loveAdd() : Number
      {
         return this.CF.getAttribute("loveAdd");
      }
      
      public function set loveAdd(v0:Number) : void
      {
         this.CF.setAttribute("loveAdd",v0);
      }
      
      public function set moreDpsMul(v0:Number) : void
      {
         this._moreDpsMul = v0 / this.V;
      }
      
      public function get moreDpsMul() : Number
      {
         return this._moreDpsMul * this.V;
      }
      
      public function set moreLifeMul(v0:Number) : void
      {
         this._moreLifeMul = v0 / this.V;
      }
      
      public function get moreLifeMul() : Number
      {
         return this._moreLifeMul * this.V;
      }
      
      public function set vehicleDefMul(v0:Number) : void
      {
         this._vehicleDefMul = v0 / this.V;
      }
      
      public function get vehicleDefMul() : Number
      {
         return this._vehicleDefMul * this.V;
      }
      
      public function set vehicleDpsMul(v0:Number) : void
      {
         this._vehicleDpsMul = v0 / this.V;
      }
      
      public function get vehicleDpsMul() : Number
      {
         return this._vehicleDpsMul * this.V;
      }
      
      public function set zodiacArmsHurtAdd(v0:Number) : void
      {
         this._zodiacArmsHurtAdd = v0 / this.V;
      }
      
      public function get zodiacArmsHurtAdd() : Number
      {
         return this._zodiacArmsHurtAdd * this.V;
      }
      
      public function set dpsAllBlack(v0:Number) : void
      {
         this._dpsAllBlack = v0 / this.V;
      }
      
      public function get dpsAllBlack() : Number
      {
         return this._dpsAllBlack * this.V;
      }
      
      public function set lifeAllBlack(v0:Number) : void
      {
         this._lifeAllBlack = v0 / this.V;
      }
      
      public function get lifeAllBlack() : Number
      {
         return this._lifeAllBlack * this.V;
      }
      
      public function set lifeRateBlack(v0:Number) : void
      {
         this._lifeRateBlack = v0 / this.V;
      }
      
      public function get lifeRateBlack() : Number
      {
         return this._lifeRateBlack * this.V;
      }
      
      public function set capacityMulBlack(v0:Number) : void
      {
         this._capacityMulBlack = v0 / this.V;
      }
      
      public function get capacityMulBlack() : Number
      {
         return this._capacityMulBlack * this.V;
      }
      
      public function set chargerMulBlack(v0:Number) : void
      {
         this._chargerMulBlack = v0 / this.V;
      }
      
      public function get chargerMulBlack() : Number
      {
         return this._chargerMulBlack * this.V;
      }
      
      public function set maxJumpNumAdd(v0:Number) : void
      {
         this._maxJumpNumAdd = v0 / this.V;
      }
      
      public function get maxJumpNumAdd() : Number
      {
         return Math.round(this._maxJumpNumAdd * this.V);
      }
      
      public function set fgNE(v0:Number) : void
      {
         this._fgNE = v0 / this.V;
      }
      
      public function get fgNE() : Number
      {
         return this._fgNE * this.V;
      }
      
      public function set dayLoveAdd(v0:Number) : void
      {
         this._dayLoveAdd = v0 / this.V;
      }
      
      public function get dayLoveAdd() : Number
      {
         return Math.round(this._dayLoveAdd * this.V);
      }
      
      public function get sweepingNum() : Number
      {
         return this.CF.getAttribute("sweepingNum");
      }
      
      public function set sweepingNum(v0:Number) : void
      {
         this.CF.setAttribute("sweepingNum",v0);
      }
      
      public function get demStroneDropNum() : Number
      {
         return this.CF.getAttribute("demStroneDropNum");
      }
      
      public function set demStroneDropNum(v0:Number) : void
      {
         this.CF.setAttribute("demStroneDropNum",v0);
      }
      
      public function get demBallDropNum() : Number
      {
         return this.CF.getAttribute("demBallDropNum");
      }
      
      public function set demBallDropNum(v0:Number) : void
      {
         this.CF.setAttribute("demBallDropNum",v0);
      }
      
      public function inData_byObj(obj:Object) : void
      {
         ClassProperty.inData(this,obj,pro_arr);
      }
      
      public function initData() : *
      {
         this.inData_byObj(new EquipPropertyData());
      }
      
      public function addData(obj0:Object) : *
      {
         var n:* = undefined;
         var pro0:String = null;
         if(obj0 == null)
         {
            return;
         }
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            if(this[pro0] is Number)
            {
               if(obj0.hasOwnProperty(pro0))
               {
                  this[pro0] += obj0[pro0];
               }
            }
         }
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this._dpsMul = v0 / this.V;
      }
      
      public function get dpsMul() : Number
      {
         return this._dpsMul * this.V;
      }
      
      public function set dpsAll(v0:Number) : void
      {
         this._dpsAll = v0 / this.V;
      }
      
      public function get dpsAll() : Number
      {
         return this._dpsAll * this.V;
      }
      
      public function set lifeAll(v0:Number) : void
      {
         this._lifeAll = v0 / this.V;
      }
      
      public function get lifeAll() : Number
      {
         return this._lifeAll * this.V;
      }
      
      public function set chargerMul_rifle(v0:Number) : void
      {
         this._chargerMul_rifle = v0 / this.V;
      }
      
      public function get chargerMul_rifle() : Number
      {
         return this._chargerMul_rifle * this.V;
      }
      
      public function set dpsMul_rifle(v0:Number) : void
      {
         this._dpsMul_rifle = v0 / this.V;
      }
      
      public function get dpsMul_rifle() : Number
      {
         return this._dpsMul_rifle * this.V;
      }
      
      public function set chargerMul_sniper(v0:Number) : void
      {
         this._chargerMul_sniper = v0 / this.V;
      }
      
      public function get chargerMul_sniper() : Number
      {
         return this._chargerMul_sniper * this.V;
      }
      
      public function set dpsMul_sniper(v0:Number) : void
      {
         this._dpsMul_sniper = v0 / this.V;
      }
      
      public function get dpsMul_sniper() : Number
      {
         return this._dpsMul_sniper * this.V;
      }
      
      public function set chargerMul_shotgun(v0:Number) : void
      {
         this._chargerMul_shotgun = v0 / this.V;
      }
      
      public function get chargerMul_shotgun() : Number
      {
         return this._chargerMul_shotgun * this.V;
      }
      
      public function set dpsMul_shotgun(v0:Number) : void
      {
         this._dpsMul_shotgun = v0 / this.V;
      }
      
      public function get dpsMul_shotgun() : Number
      {
         return this._dpsMul_shotgun * this.V;
      }
      
      public function set chargerMul_pistol(v0:Number) : void
      {
         this._chargerMul_pistol = v0 / this.V;
      }
      
      public function get chargerMul_pistol() : Number
      {
         return this._chargerMul_pistol * this.V;
      }
      
      public function set dpsMul_pistol(v0:Number) : void
      {
         this._dpsMul_pistol = v0 / this.V;
      }
      
      public function get dpsMul_pistol() : Number
      {
         return this._dpsMul_pistol * this.V;
      }
      
      public function set chargerMul_rocket(v0:Number) : void
      {
         this._chargerMul_rocket = v0 / this.V;
      }
      
      public function get chargerMul_rocket() : Number
      {
         return this._chargerMul_rocket * this.V;
      }
      
      public function set dpsMul_rocket(v0:Number) : void
      {
         this._dpsMul_rocket = v0 / this.V;
      }
      
      public function get dpsMul_rocket() : Number
      {
         return this._dpsMul_rocket * this.V;
      }
      
      public function set reload(v0:Number) : void
      {
         this._reload = v0 / this.V;
      }
      
      public function get reload() : Number
      {
         return this._reload * this.V;
      }
      
      public function set hurt(v0:Number) : void
      {
         this._hurt = v0 / this.V;
      }
      
      public function get hurt() : Number
      {
         return Math.round(this._hurt * this.V);
      }
      
      public function set reload_rifle(v0:Number) : void
      {
         this._reload_rifle = v0 / this.V;
      }
      
      public function get reload_rifle() : Number
      {
         return this._reload_rifle * this.V;
      }
      
      public function set hurt_rifle(v0:Number) : void
      {
         this._hurt_rifle = v0 / this.V;
      }
      
      public function get hurt_rifle() : Number
      {
         return Math.round(this._hurt_rifle * this.V);
      }
      
      public function set reload_sniper(v0:Number) : void
      {
         this._reload_sniper = v0 / this.V;
      }
      
      public function get reload_sniper() : Number
      {
         return this._reload_sniper * this.V;
      }
      
      public function set hurt_sniper(v0:Number) : void
      {
         this._hurt_sniper = v0 / this.V;
      }
      
      public function get hurt_sniper() : Number
      {
         return Math.round(this._hurt_sniper * this.V);
      }
      
      public function set reload_shotgun(v0:Number) : void
      {
         this._reload_shotgun = v0 / this.V;
      }
      
      public function get reload_shotgun() : Number
      {
         return this._reload_shotgun * this.V;
      }
      
      public function set hurt_shotgun(v0:Number) : void
      {
         this._hurt_shotgun = v0 / this.V;
      }
      
      public function get hurt_shotgun() : Number
      {
         return Math.round(this._hurt_shotgun * this.V);
      }
      
      public function set reload_pistol(v0:Number) : void
      {
         this._reload_pistol = v0 / this.V;
      }
      
      public function get reload_pistol() : Number
      {
         return this._reload_pistol * this.V;
      }
      
      public function set hurt_pistol(v0:Number) : void
      {
         this._hurt_pistol = v0 / this.V;
      }
      
      public function get hurt_pistol() : Number
      {
         return Math.round(this._hurt_pistol * this.V);
      }
      
      public function set reload_rocket(v0:Number) : void
      {
         this._reload_rocket = v0 / this.V;
      }
      
      public function get reload_rocket() : Number
      {
         return this._reload_rocket * this.V;
      }
      
      public function set hurt_rocket(v0:Number) : void
      {
         this._hurt_rocket = v0 / this.V;
      }
      
      public function get hurt_rocket() : Number
      {
         return Math.round(this._hurt_rocket * this.V);
      }
      
      public function set life(v0:Number) : void
      {
         this._life = v0 / this.V;
      }
      
      public function get life() : Number
      {
         return Math.round(this._life * this.V);
      }
      
      public function set hurtMul(v0:Number) : void
      {
         this._hurtMul = v0 / this.V;
      }
      
      public function get hurtMul() : Number
      {
         return this._hurtMul * this.V;
      }
      
      public function set hurtAll(v0:Number) : void
      {
         this._hurtAll = v0 / this.V;
      }
      
      public function get hurtAll() : Number
      {
         return this._hurtAll * this.V;
      }
      
      public function set lifeMul(v0:Number) : void
      {
         this._lifeMul = v0 / this.V;
      }
      
      public function get lifeMul() : Number
      {
         return this._lifeMul * this.V;
      }
      
      public function set hurtMul_rifle(v0:Number) : void
      {
         this._hurtMul_rifle = v0 / this.V;
      }
      
      public function get hurtMul_rifle() : Number
      {
         return this._hurtMul_rifle * this.V;
      }
      
      public function set lifeRate(v0:Number) : void
      {
         this._lifeRate = v0 / this.V;
      }
      
      public function get lifeRate() : Number
      {
         return Math.round(this._lifeRate * this.V);
      }
      
      public function set head(v0:Number) : void
      {
         this._head = v0 / this.V;
      }
      
      public function get head() : Number
      {
         return Math.round(this._head * this.V);
      }
      
      public function set exp(v0:Number) : void
      {
         this._exp = v0 / this.V;
      }
      
      public function get exp() : Number
      {
         return Math.round(this._exp * this.V);
      }
      
      public function set hurtMul_sniper(v0:Number) : void
      {
         this._hurtMul_sniper = v0 / this.V;
      }
      
      public function get hurtMul_sniper() : Number
      {
         return this._hurtMul_sniper * this.V;
      }
      
      public function set headMul(v0:Number) : void
      {
         this._headMul = v0 / this.V;
      }
      
      public function get headMul() : Number
      {
         return this._headMul * this.V;
      }
      
      public function set hurtMul_shotgun(v0:Number) : void
      {
         this._hurtMul_shotgun = v0 / this.V;
      }
      
      public function get hurtMul_shotgun() : Number
      {
         return this._hurtMul_shotgun * this.V;
      }
      
      public function set coin(v0:Number) : void
      {
         this._coin = v0 / this.V;
      }
      
      public function get coin() : Number
      {
         return Math.round(this._coin * this.V);
      }
      
      public function set hurtMul_pistol(v0:Number) : void
      {
         this._hurtMul_pistol = v0 / this.V;
      }
      
      public function get hurtMul_pistol() : Number
      {
         return this._hurtMul_pistol * this.V;
      }
      
      public function set coinMul(v0:Number) : void
      {
         this._coinMul = v0 / this.V;
      }
      
      public function get coinMul() : Number
      {
         return this._coinMul * this.V;
      }
      
      public function set hurtMul_rocket(v0:Number) : void
      {
         this._hurtMul_rocket = v0 / this.V;
      }
      
      public function get hurtMul_rocket() : Number
      {
         return this._hurtMul_rocket * this.V;
      }
      
      public function set capacity(v0:Number) : void
      {
         this._capacity = v0 / this.V;
      }
      
      public function get capacity() : Number
      {
         return Math.round(this._capacity * this.V);
      }
      
      public function set expMul(v0:Number) : void
      {
         this._expMul = v0 / this.V;
      }
      
      public function get expMul() : Number
      {
         return this._expMul * this.V;
      }
      
      public function set capacity_rifle(v0:Number) : void
      {
         this._capacity_rifle = v0 / this.V;
      }
      
      public function get capacity_rifle() : Number
      {
         return Math.round(this._capacity_rifle * this.V);
      }
      
      public function set prestige(v0:Number) : void
      {
         this._prestige = v0 / this.V;
      }
      
      public function get prestige() : Number
      {
         return Math.round(this._prestige * this.V);
      }
      
      public function set capacity_sniper(v0:Number) : void
      {
         this._capacity_sniper = v0 / this.V;
      }
      
      public function get capacity_sniper() : Number
      {
         return Math.round(this._capacity_sniper * this.V);
      }
      
      public function set capacity_shotgun(v0:Number) : void
      {
         this._capacity_shotgun = v0 / this.V;
      }
      
      public function get capacity_shotgun() : Number
      {
         return Math.round(this._capacity_shotgun * this.V);
      }
      
      public function set capacity_pistol(v0:Number) : void
      {
         this._capacity_pistol = v0 / this.V;
      }
      
      public function get capacity_pistol() : Number
      {
         return Math.round(this._capacity_pistol * this.V);
      }
      
      public function set capacity_rocket(v0:Number) : void
      {
         this._capacity_rocket = v0 / this.V;
      }
      
      public function get capacity_rocket() : Number
      {
         return Math.round(this._capacity_rocket * this.V);
      }
      
      public function set capacityMul(v0:Number) : void
      {
         this._capacityMul = v0 / this.V;
      }
      
      public function get capacityMul() : Number
      {
         return this._capacityMul * this.V;
      }
      
      public function set capacityMul_rifle(v0:Number) : void
      {
         this._capacityMul_rifle = v0 / this.V;
      }
      
      public function get capacityMul_rifle() : Number
      {
         return this._capacityMul_rifle * this.V;
      }
      
      public function set capacityMul_sniper(v0:Number) : void
      {
         this._capacityMul_sniper = v0 / this.V;
      }
      
      public function get capacityMul_sniper() : Number
      {
         return this._capacityMul_sniper * this.V;
      }
      
      public function set capacityMul_shotgun(v0:Number) : void
      {
         this._capacityMul_shotgun = v0 / this.V;
      }
      
      public function get capacityMul_shotgun() : Number
      {
         return this._capacityMul_shotgun * this.V;
      }
      
      public function set capacityMul_pistol(v0:Number) : void
      {
         this._capacityMul_pistol = v0 / this.V;
      }
      
      public function get capacityMul_pistol() : Number
      {
         return this._capacityMul_pistol * this.V;
      }
      
      public function set capacityMul_rocket(v0:Number) : void
      {
         this._capacityMul_rocket = v0 / this.V;
      }
      
      public function get capacityMul_rocket() : Number
      {
         return this._capacityMul_rocket * this.V;
      }
      
      public function set charger(v0:Number) : void
      {
         this._charger = v0 / this.V;
      }
      
      public function get charger() : Number
      {
         return Math.round(this._charger * this.V);
      }
      
      public function set dps(v0:Number) : void
      {
         this._dps = v0 / this.V;
      }
      
      public function get dps() : Number
      {
         return Math.round(this._dps * this.V);
      }
      
      public function set charger_rifle(v0:Number) : void
      {
         this._charger_rifle = v0 / this.V;
      }
      
      public function get charger_rifle() : Number
      {
         return Math.round(this._charger_rifle * this.V);
      }
      
      public function set dps_rifle(v0:Number) : void
      {
         this._dps_rifle = v0 / this.V;
      }
      
      public function get dps_rifle() : Number
      {
         return Math.round(this._dps_rifle * this.V);
      }
      
      public function set charger_sniper(v0:Number) : void
      {
         this._charger_sniper = v0 / this.V;
      }
      
      public function get charger_sniper() : Number
      {
         return Math.round(this._charger_sniper * this.V);
      }
      
      public function set dps_sniper(v0:Number) : void
      {
         this._dps_sniper = v0 / this.V;
      }
      
      public function get dps_sniper() : Number
      {
         return Math.round(this._dps_sniper * this.V);
      }
      
      public function set charger_shotgun(v0:Number) : void
      {
         this._charger_shotgun = v0 / this.V;
      }
      
      public function get charger_shotgun() : Number
      {
         return Math.round(this._charger_shotgun * this.V);
      }
      
      public function set dps_shotgun(v0:Number) : void
      {
         this._dps_shotgun = v0 / this.V;
      }
      
      public function get dps_shotgun() : Number
      {
         return Math.round(this._dps_shotgun * this.V);
      }
      
      public function set charger_pistol(v0:Number) : void
      {
         this._charger_pistol = v0 / this.V;
      }
      
      public function get charger_pistol() : Number
      {
         return Math.round(this._charger_pistol * this.V);
      }
      
      public function set dps_pistol(v0:Number) : void
      {
         this._dps_pistol = v0 / this.V;
      }
      
      public function get dps_pistol() : Number
      {
         return Math.round(this._dps_pistol * this.V);
      }
      
      public function set charger_rocket(v0:Number) : void
      {
         this._charger_rocket = v0 / this.V;
      }
      
      public function get charger_rocket() : Number
      {
         return Math.round(this._charger_rocket * this.V);
      }
      
      public function set dps_rocket(v0:Number) : void
      {
         this._dps_rocket = v0 / this.V;
      }
      
      public function get dps_rocket() : Number
      {
         return Math.round(this._dps_rocket * this.V);
      }
      
      public function set chargerMul(v0:Number) : void
      {
         this._chargerMul = v0 / this.V;
      }
      
      public function get chargerMul() : Number
      {
         return this._chargerMul * this.V;
      }
      
      public function set dps_crossbow(v0:Number) : void
      {
         this._dps_crossbow = v0 / this.V;
      }
      
      public function get dps_crossbow() : Number
      {
         return Math.round(this._dps_crossbow * this.V);
      }
      
      public function set dps_flamer(v0:Number) : void
      {
         this._dps_flamer = v0 / this.V;
      }
      
      public function get dps_flamer() : Number
      {
         return Math.round(this._dps_flamer * this.V);
      }
      
      public function set dpsMul_crossbow(v0:Number) : void
      {
         this._dpsMul_crossbow = v0 / this.V;
      }
      
      public function get dpsMul_crossbow() : Number
      {
         return this._dpsMul_crossbow * this.V;
      }
      
      public function set dpsMul_flamer(v0:Number) : void
      {
         this._dpsMul_flamer = v0 / this.V;
      }
      
      public function get dpsMul_flamer() : Number
      {
         return this._dpsMul_flamer * this.V;
      }
      
      public function set hurtMul_wavegun(v0:Number) : void
      {
         this._hurtMul_wavegun = v0 / this.V;
      }
      
      public function get hurtMul_wavegun() : Number
      {
         return this._hurtMul_wavegun * this.V;
      }
      
      public function set dpsMul_wavegun(v0:Number) : void
      {
         this._dpsMul_wavegun = v0 / this.V;
      }
      
      public function get dpsMul_wavegun() : Number
      {
         return this._dpsMul_wavegun * this.V;
      }
      
      public function set hurt_crossbow(v0:Number) : void
      {
         this._hurt_crossbow = v0 / this.V;
      }
      
      public function get hurt_crossbow() : Number
      {
         return Math.round(this._hurt_crossbow * this.V);
      }
      
      public function set hurt_flamer(v0:Number) : void
      {
         this._hurt_flamer = v0 / this.V;
      }
      
      public function get hurt_flamer() : Number
      {
         return Math.round(this._hurt_flamer * this.V);
      }
      
      public function set hurtMul_crossbow(v0:Number) : void
      {
         this._hurtMul_crossbow = v0 / this.V;
      }
      
      public function get hurtMul_crossbow() : Number
      {
         return this._hurtMul_crossbow * this.V;
      }
      
      public function set hurtMul_flamer(v0:Number) : void
      {
         this._hurtMul_flamer = v0 / this.V;
      }
      
      public function get hurtMul_flamer() : Number
      {
         return this._hurtMul_flamer * this.V;
      }
      
      public function set capacity_crossbow(v0:Number) : void
      {
         this._capacity_crossbow = v0 / this.V;
      }
      
      public function get capacity_crossbow() : Number
      {
         return Math.round(this._capacity_crossbow * this.V);
      }
      
      public function set capacity_flamer(v0:Number) : void
      {
         this._capacity_flamer = v0 / this.V;
      }
      
      public function get capacity_flamer() : Number
      {
         return Math.round(this._capacity_flamer * this.V);
      }
      
      public function set capacityMul_crossbow(v0:Number) : void
      {
         this._capacityMul_crossbow = v0 / this.V;
      }
      
      public function get capacityMul_crossbow() : Number
      {
         return this._capacityMul_crossbow * this.V;
      }
      
      public function set capacityMul_flamer(v0:Number) : void
      {
         this._capacityMul_flamer = v0 / this.V;
      }
      
      public function get capacityMul_flamer() : Number
      {
         return this._capacityMul_flamer * this.V;
      }
      
      public function set charger_crossbow(v0:Number) : void
      {
         this._charger_crossbow = v0 / this.V;
      }
      
      public function get charger_crossbow() : Number
      {
         return Math.round(this._charger_crossbow * this.V);
      }
      
      public function set charger_flamer(v0:Number) : void
      {
         this._charger_flamer = v0 / this.V;
      }
      
      public function get charger_flamer() : Number
      {
         return Math.round(this._charger_flamer * this.V);
      }
      
      public function set chargerMul_crossbow(v0:Number) : void
      {
         this._chargerMul_crossbow = v0 / this.V;
      }
      
      public function get chargerMul_crossbow() : Number
      {
         return this._chargerMul_crossbow * this.V;
      }
      
      public function set chargerMul_flamer(v0:Number) : void
      {
         this._chargerMul_flamer = v0 / this.V;
      }
      
      public function get chargerMul_flamer() : Number
      {
         return this._chargerMul_flamer * this.V;
      }
      
      public function set reload_crossbow(v0:Number) : void
      {
         this._reload_crossbow = v0 / this.V;
      }
      
      public function get reload_crossbow() : Number
      {
         return this._reload_crossbow * this.V;
      }
      
      public function set reload_flamer(v0:Number) : void
      {
         this._reload_flamer = v0 / this.V;
      }
      
      public function get reload_flamer() : Number
      {
         return this._reload_flamer * this.V;
      }
      
      public function getDpsAll() : Number
      {
         return this.dpsAll + this.dpsAllBlack;
      }
      
      public function getLifeAll() : Number
      {
         return this.lifeAll + this.lifeAllBlack;
      }
      
      public function getLifeRate() : Number
      {
         return this.lifeRate + this.lifeRateBlack;
      }
      
      public function getCapacityMul() : Number
      {
         return this.capacityMul + this.capacityMulBlack;
      }
      
      public function getChargerMul() : Number
      {
         return this.chargerMul + this.chargerMulBlack;
      }
      
      public function getLotteryMul() : Number
      {
         if(this.getUseSummerB())
         {
            return 0;
         }
         return this.lottery / 100;
      }
      
      private function getUseSummerB() : Boolean
      {
         return false;
      }
      
      public function getDropProTip(pd0:NormalPlayerData) : String
      {
         var name0:String = null;
         var v0:Number = NaN;
         var ui0:Number = NaN;
         var sum0:Number = NaN;
         var sumMax0:Number = NaN;
         var moreMul0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var index0:int = 0;
         var str0:String = "";
         var heroPD0:PlayerData = pd0 as PlayerData;
         for each(name0 in EquipPro.dropNameArr)
         {
            if(this.hasOwnProperty(name0))
            {
               v0 = Number(this[name0]);
               ui0 = v0;
               sum0 = 0;
               sumMax0 = EquipPro.getSumMax(name0);
               if(Boolean(heroPD0))
               {
                  sum0 = heroPD0.moreWay.getProSum(name0);
                  if(sum0 == -1)
                  {
                     ui0 = v0;
                  }
                  else
                  {
                     ui0 = sum0;
                  }
               }
               else
               {
                  moreMul0 = EquipPro.getMoreAddMul(name0);
                  v0 *= moreMul0;
                  ui0 = v0;
               }
               if(ui0 > 0 || sumMax0 > 0 && heroPD0)
               {
                  proD0 = EquipPropertyDataCreator.getPropertyArrayDefine(name0);
                  if(Boolean(proD0))
                  {
                     str0 += (index0 == 0 ? "" : "\n") + proD0.cnName + "：<green " + proD0.getValueString(ui0) + "/>";
                     if(Boolean(heroPD0))
                     {
                        if(sumMax0 > 0)
                        {
                           str0 += "<purple (上限" + proD0.getValueString(sumMax0,false,"") + "，背包可生效)/>";
                        }
                     }
                  }
               }
               index0++;
            }
         }
         return str0;
      }
   }
}

