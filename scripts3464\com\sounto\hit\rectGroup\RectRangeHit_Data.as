package com.sounto.hit.rectGroup
{
   import com.sounto.motion.Motion_DOFData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class RectRangeHit_Data
   {
      
      public var haveDataB:Boolean = false;
      
      private var rect:Rectangle = new Rectangle();
      
      private var base:Rectangle = new Rectangle();
      
      public function RectRangeHit_Data()
      {
         super();
      }
      
      public function inData_byRect(rect0:Rectangle) : *
      {
         this.base = rect0.clone();
         this.base.x += 30;
         this.base.width -= 60;
         this.base.y += 50;
         this.base.height -= 100;
         this.rect = this.base.clone();
         this.haveDataB = true;
      }
      
      public function clear() : void
      {
         this.haveDataB = false;
      }
      
      public function getRect() : Rectangle
      {
         return this.rect;
      }
      
      public function getRanPoint() : Point
      {
         return new Point(this.rect.x + this.rect.width * Math.random(),this.rect.y + this.rect.height * Math.random());
      }
      
      public function inSightRect(rect0:Rectangle) : void
      {
         var rect2:Rectangle = rect0.clone();
         rect2.x -= 5;
         rect2.y = -100000;
         rect2.width -= 10;
         rect2.height = 200000;
         this.rect = this.base.intersection(rect2);
      }
      
      public function isInRangeB(rect0:Rectangle, spaceB0:Boolean) : Boolean
      {
         if(rect0.x > this.rect.x && rect0.x + rect0.width < this.rect.x + this.rect.width)
         {
            if(!spaceB0)
            {
               return true;
            }
            if(rect0.y > this.rect.y && rect0.y + rect0.height < this.rect.y + this.rect.height)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getSlopeVerticalRa(x0:Number, y0:Number) : Number
      {
         if(x0 >= this.rect.x + this.rect.width)
         {
            return Math.PI;
         }
         if(x0 <= this.rect.x)
         {
            return 0;
         }
         return -1000;
      }
      
      public function outPan(x0:Number, y0:Number) : Boolean
      {
         if(Boolean(this.rect))
         {
            if(this.rect.contains(x0,y0))
            {
               return false;
            }
         }
         return true;
      }
      
      public function hitRange(rect0:Rectangle, dof0:Motion_DOFData) : *
      {
         var cx2:int = 0;
         var cy2:int = 0;
         var rect1:Rectangle = this.rect;
         var cx1:int = rect0.x - rect1.x;
         if(cx1 < 0)
         {
            dof0.left = 1;
            dof0.left_back = -cx1 - 1;
         }
         else
         {
            cx2 = rect0.x + rect0.width - (rect1.x + rect1.width);
            if(cx2 > 0)
            {
               dof0.right = 1;
               dof0.right_back = cx2 - 1;
            }
         }
         var cy1:int = rect0.y - rect1.y;
         if(cy1 < 0)
         {
            dof0.up = 1;
            dof0.up_back = -cy1 - 1;
         }
         else
         {
            cy2 = rect0.y + rect0.height - (rect1.y + rect1.height);
            if(cy2 > 0)
            {
               dof0.down = 1;
               dof0.down_back = cy2 - 1;
            }
         }
      }
      
      public function limitX(x0:Number) : Number
      {
         var rect1:Rectangle = this.rect;
         if(x0 < rect1.x)
         {
            x0 = rect1.x;
         }
         if(x0 > rect1.x + rect1.width)
         {
            x0 = rect1.x + rect1.width;
         }
         return x0;
      }
      
      public function limitY(x0:Number) : Number
      {
         var rect1:Rectangle = this.rect;
         if(x0 < rect1.y)
         {
            x0 = rect1.y;
         }
         if(x0 > rect1.y + rect1.height)
         {
            x0 = rect1.y + rect1.height;
         }
         return x0;
      }
   }
}

