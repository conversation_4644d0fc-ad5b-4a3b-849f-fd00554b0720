package dataAll._app.parts
{
   import com.sounto.math.Maths;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.body.attack.ElementHurt;
   import dataAll.equip.define.EquipColor;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.ui.text.ProTipType;
   
   public class PartsAddData
   {
      
      public static const negativeArr:Array = ["aiShootRange","gravity","bulletSpeed","shootDistanceAddMul","rareHurtMul"];
      
      public static const overlyingArr:Array = ["dpsMul"];
      
      public static const neVerlyingArr:Array = ["rareHurtMul"];
      
      public static const ELE_ARR:Array = ["fireEleB","frozenEleB","poisonEleB","electricEleB"];
      
      public static var pro_arr:Array = [];
      
      public var skillArr:Array = [];
      
      public var rareSkillArr:Array = [];
      
      public var twoShootPro:Number = 0;
      
      public var penetrationNum:int = 0;
      
      public var penetrationGap:int = 0;
      
      public var dpsMul:Number = 0;
      
      public var dps:Number = 0;
      
      public var attackGap:Number = 0;
      
      public var capacityMul:Number = 0;
      
      public var shakeAngle:Number = 0;
      
      public var reload:Number = 0;
      
      public var shootDistance:Number = 0;
      
      public var shootDistanceAddMul:Number = 0;
      
      public var aiShootRange:Number = 0;
      
      public var bulletWidth:Number = 0;
      
      public var bulletSpeed:Number = 0;
      
      public var bulletLife:Number = 0;
      
      public var gravity:Number = 0;
      
      public var followValue:Number = 0;
      
      public var eleHurtMul:Number = 0;
      
      public var fireEleB:Boolean = false;
      
      public var frozenEleB:Boolean = false;
      
      public var poisonEleB:Boolean = false;
      
      public var electricEleB:Boolean = false;
      
      public var noMagneticB:Boolean = false;
      
      public var shootAngleCtrlB:Boolean = false;
      
      public var redHurtMul:Number = 0;
      
      public var uidpsMul:Number = 0;
      
      public var rareHurtMul:Number = 0;
      
      public var demCapacityMul:Number = 0;
      
      public var color:String = "";
      
      public var colorHurtMul:Number = 0;
      
      public function PartsAddData()
      {
         super();
      }
      
      public static function canUseGather(proD0:PropertyArrayDefine, armsD0:ArmsDefine) : Boolean
      {
         var da0:ArmsData = armsD0 as ArmsData;
         var typeArr0:Array = proD0.typeArr;
         var proName0:String = proD0.name;
         if(Boolean(typeArr0))
         {
            if(typeArr0.indexOf("rect") >= 0)
            {
               if(armsD0.isRect() == false)
               {
                  return false;
               }
            }
         }
         if(proName0 == "bulletWidth")
         {
            if(armsD0.name == "extremeGun")
            {
               return false;
            }
         }
         else if(proName0 == "shootAngleCtrlB")
         {
            if(armsD0.shootAngle <= 0 || armsD0.bulletNum <= 1)
            {
               return false;
            }
         }
         else if(proName0 == "eleHurtMul")
         {
            if(Boolean(da0))
            {
               if(da0.getEleHurtMul() <= 0)
               {
                  return false;
               }
            }
         }
         else if(proName0 == "redHurtMul")
         {
            if(Boolean(da0))
            {
               if(da0.getColor() != EquipColor.RED && da0.getColor() != EquipColor.ORANGE)
               {
                  return false;
               }
            }
         }
         else if(proName0 == "aiShootRange")
         {
            if(Boolean(da0))
            {
               if(da0.getAIShootRange() > 1500)
               {
                  return false;
               }
            }
         }
         if(Boolean(da0))
         {
            if(ELE_ARR.indexOf(proName0) >= 0)
            {
               if(da0.getEleHurtMul() <= 0)
               {
                  return false;
               }
               if(proName0 == da0.getEle() + "EleB")
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      public function getOtherEleNum() : int
      {
         var num0:int = 0;
         if(this.fireEleB)
         {
            num0++;
         }
         if(this.frozenEleB)
         {
            num0++;
         }
         if(this.poisonEleB)
         {
            num0++;
         }
         if(this.electricEleB)
         {
            num0++;
         }
         return num0;
      }
      
      public function getOtherEleArr() : Array
      {
         var arr0:Array = null;
         if(this.fireEleB || this.frozenEleB || this.poisonEleB || this.electricEleB)
         {
            arr0 = [];
            if(this.fireEleB)
            {
               arr0.push(ElementHurt.fire);
            }
            if(this.frozenEleB)
            {
               arr0.push(ElementHurt.frozen);
            }
            if(this.poisonEleB)
            {
               arr0.push(ElementHurt.poison);
            }
            if(this.electricEleB)
            {
               arr0.push(ElementHurt.electric);
            }
            return arr0;
         }
         return null;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inMaxObj(obj0:Object) : void
      {
         var n:* = undefined;
         var name0:String = null;
         var v0:Number = NaN;
         var bb0:Boolean = false;
         var color0:String = null;
         for(n in obj0)
         {
            name0 = n as String;
            if(obj0[n] is Number)
            {
               v0 = Number(obj0[n]);
               if(overlyingArr.indexOf(name0) >= 0)
               {
                  this[n] += v0;
               }
               else if(neVerlyingArr.indexOf(name0) >= 0 && Maths.Pn(v0) != Maths.Pn(this[n]))
               {
                  this[n] += v0;
               }
               else
               {
                  bb0 = true;
                  if(bb0)
                  {
                     if(this[n] < v0)
                     {
                        this[n] = v0;
                     }
                     else if(negativeArr.indexOf(name0) >= 0)
                     {
                        if(this[n] == 0 && v0 < 0)
                        {
                           this[n] = v0;
                        }
                     }
                  }
               }
            }
            else if(obj0[n] is Boolean)
            {
               bb0 = Boolean(obj0[n]);
               if(bb0 && this[n] == false)
               {
                  if(ELE_ARR.indexOf(name0) >= 0)
                  {
                     if(this.getOtherEleNum() <= 0)
                     {
                        this[n] = true;
                     }
                  }
                  else
                  {
                     this[n] = true;
                  }
               }
            }
            else if(obj0[n] is String)
            {
               if(name0 == "color")
               {
                  color0 = obj0[n] as String;
                  if(EquipColor.moreColorPan(color0,this[n]))
                  {
                     this[n] = obj0[n];
                  }
               }
               else
               {
                  this[n] = obj0[n];
               }
            }
         }
      }
      
      public function setPro(name0:String, v0:*) : void
      {
         this[name0] = v0;
      }
      
      public function addPro(name0:String, v0:Number) : void
      {
         this[name0] += v0;
      }
      
      public function addData_byObj(obj0:Object) : void
      {
         var n:* = undefined;
         for(n in obj0)
         {
            if(this[n] is Number || this[n] is int)
            {
               if(this[n] == 0)
               {
                  this[n] = obj0[n];
               }
               else if(n == "dpsMul")
               {
                  this[n] += obj0[n];
               }
            }
            else if(this[n] is String)
            {
               if(this[n] == "")
               {
                  this[n] = obj0[n];
               }
            }
            else if(Boolean(this[n].hasOwnProperty("haveDataB")))
            {
               if(!this[n].haveDataB())
               {
                  this[n].inData_byObj(obj0[n]);
               }
            }
         }
      }
      
      public function addNewSkillArr(arr0:Array) : void
      {
         if(arr0.length > 0)
         {
            this.skillArr = this.skillArr.concat();
            ArrayMethod.addNoRepeatArrInArr(this.skillArr,arr0);
         }
      }
      
      public function addRareSkillArr(arr0:Array) : void
      {
         if(arr0.length > 0)
         {
            if(this.rareSkillArr == null)
            {
               this.rareSkillArr = [];
            }
            ArrayMethod.addNoRepeatArrInArr(this.rareSkillArr,arr0);
         }
      }
      
      public function addNewAndRareSkillArr(arr0:Array) : void
      {
         this.addNewSkillArr(arr0);
         this.addRareSkillArr(arr0);
      }
      
      public function haveRareB() : Boolean
      {
         var d0:PropertyArrayDefine = null;
         var name0:String = null;
         var v0:* = undefined;
         var proDefineArr0:Array = Gaming.defineGroup.partsProperty.rarePro.propertyArr;
         for each(d0 in proDefineArr0)
         {
            name0 = d0.name;
            if(this.hasOwnProperty(name0))
            {
               v0 = this[name0];
               if(v0 is Number)
               {
                  if(v0 != 0)
                  {
                     return true;
                  }
               }
               else if(v0 is Boolean)
               {
                  if(v0)
                  {
                     return true;
                  }
               }
               else if(v0 is String)
               {
                  if(v0 != "")
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function getGatherText() : String
      {
         var str0:String = "";
         if(this.dpsMul != 0)
         {
            str0 += "伤害" + (this.dpsMul > 0 ? "+" : "") + NumberMethod.toPer(this.dpsMul);
         }
         if(str0 != "")
         {
            str0 = "<i1>|<blue 属性：/>\n" + str0 + "\n\n";
         }
         if(this.haveRareB())
         {
            str0 += ProTipType.getTitleMixed("稀有属性") + "\n";
            str0 += PartsCreator.getRareGather(this,"") + "\n\n";
         }
         if(this.skillArr.length > 0)
         {
            str0 += "<i1>|<blue 技能：/>\n";
            str0 += ArmsSpecialAndSkill.getAllSkillTip(this.skillArr) + "\n\n";
         }
         return str0;
      }
   }
}

