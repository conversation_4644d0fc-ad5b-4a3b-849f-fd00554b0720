package dataAll._app.tower
{
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.peak.IO_PointData;
   import dataAll._app.peak.PeakProDefine;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.gift.GiftAddit;
   
   public class UnendData implements IO_EquipAddGetter, IO_PointData
   {
      
      public static const planArr:Array = ["0","1","2"];
      
      public static const planCnArr:Array = ["方案1","方案2","方案3"];
      
      private var save:TowerSave;
      
      private var uiLv:int = 0;
      
      private var winTip:String = "";
      
      private var now:UnendAgent = null;
      
      public function UnendData()
      {
         super();
      }
      
      public static function get LEVEL_POINT() : int
      {
         return 2;
      }
      
      public static function getPointDefine(name0:String) : PeakProDefine
      {
         return Gaming.defineGroup.unend.proG.getDefine(name0);
      }
      
      public function inData_bySave(s0:TowerSave) : void
      {
         this.save = s0;
         this.clearPointNoPro(s0.p0);
         this.clearPointNoPro(s0.p1);
         this.clearPointNoPro(s0.p2);
      }
      
      private function clearPointNoPro(p0:NumberEncodeObj) : void
      {
         var n:* = undefined;
         var obj0:Object = p0.saveObj;
         var newObj0:Object = {};
         for(n in obj0)
         {
            if(Boolean(getPointDefine(n)))
            {
               newObj0[n] = obj0[n];
            }
         }
         p0.saveObj = newObj0;
      }
      
      public function getSave() : TowerSave
      {
         return this.save;
      }
      
      public function isWinB(lv0:int) : Boolean
      {
         if(lv0 <= this.save.unendLv)
         {
            return true;
         }
         return false;
      }
      
      public function isUnlockB(lv0:int) : Boolean
      {
         if(lv0 <= this.save.unendLv + 1)
         {
            return true;
         }
         return false;
      }
      
      public function setUILv(lv0:int) : void
      {
         this.uiLv = lv0;
      }
      
      public function getUILv() : int
      {
         if(this.uiLv > 0)
         {
            return this.uiLv;
         }
         return this.getUnlockMaxLv();
      }
      
      public function getUnlockMaxLv() : int
      {
         var lv0:int = this.save.unendLv + 1;
         return UnendAgent.dealLv(lv0);
      }
      
      public function getWinLv() : int
      {
         var lv0:int = this.save.unendLv;
         return UnendAgent.dealLv(lv0);
      }
      
      public function dealLv(lv0:int) : int
      {
         var max0:int = this.getUnlockMaxLv();
         lv0 = UnendAgent.dealLv(lv0);
         if(lv0 > max0)
         {
            lv0 = max0;
         }
         return lv0;
      }
      
      public function getInfoStr() : String
      {
         var s0:String = "·通关最高层 " + ComMethod.color(this.save.unendLv + "层","#00FFFF");
         s0 += "\n·全体战斗力 " + ComMethod.yellow("+" + NumberMethod.toPer(this.getWholeAdd(),2));
         s0 += "\n" + ComMethod.color("   每一层+" + this.getOneWholeAddTip(),"#666666",12);
         s0 += "\n·随机黑武掉率 " + ComMethod.yellow("+" + NumberMethod.toPer(this.getRanBlackArmsDropPro(),0));
         s0 += "\n" + ComMethod.color("   每一层+" + this.getOneRanBlackArmsDropProTip(),"#666666",12);
         s0 += "\n·虚天点数 " + ComMethod.color(this.getAllPoint() + "点","#00FFFF");
         s0 += "\n" + ComMethod.color("   当前层已获得点数 " + ComMethod.dropColor(this.getNowGetPoint(),LEVEL_POINT),"#666666",12);
         return s0 + ("\n" + ComMethod.color("   每击杀1次未通关层的首领，将获得1点点数，每一层最多获得" + LEVEL_POINT + "点。","#666666",12));
      }
      
      public function getNowGetPoint() : int
      {
         var now0:int = 0;
         var unendLv0:int = this.save.unendLv;
         if(this.uiLv <= unendLv0)
         {
            now0 = LEVEL_POINT;
         }
         else if(this.uiLv == unendLv0 + 1)
         {
            now0 = this.save.uP;
         }
         else
         {
            now0 = 0;
         }
         return now0;
      }
      
      public function getProAddObj() : Object
      {
         var obj0:Object = {};
         obj0["dpsWhole"] = this.getWholeAdd();
         obj0["ranBlackArmsDropPro"] = this.getRanBlackArmsDropPro();
         return obj0;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      public function getRanBlackArmsDropPro() : Number
      {
         return NumberMethod.toFixed(this.save.unendLv * UnendAgent.ONE_RAN_ARMS,2);
      }
      
      private function getOneRanBlackArmsDropProTip() : String
      {
         return NumberMethod.toPer(UnendAgent.ONE_RAN_ARMS,0);
      }
      
      public function getWholeAdd() : Number
      {
         return NumberMethod.toFixed(this.save.unendLv * UnendAgent.ONE_WHOLE_DPS,4);
      }
      
      private function getOneWholeAddTip() : String
      {
         return NumberMethod.toPer(UnendAgent.ONE_WHOLE_DPS,2);
      }
      
      public function getWinTipAndClear() : String
      {
         var s0:String = this.winTip;
         this.winTip = "";
         return s0;
      }
      
      public function setNow(a0:UnendAgent) : void
      {
         this.now = a0;
      }
      
      public function killBossEvent() : Boolean
      {
         var nowLv0:int = 0;
         if(Boolean(this.now))
         {
            nowLv0 = this.now.lv;
            if(this.save.unendLv < nowLv0)
            {
               if(this.save.uP < LEVEL_POINT)
               {
                  ++this.save.uP;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function winEvent() : void
      {
         var nowLv0:int = 0;
         if(Boolean(this.now))
         {
            nowLv0 = this.now.lv;
            if(this.save.unendLv < nowLv0)
            {
               this.save.unendLv = nowLv0;
               this.save.uP = 0;
               GiftAddit.add(this.now.gift);
               this.uiLv = this.dealLv(nowLv0 + 1);
               this.winTip = "您已成功通关虚天塔第<b>" + ComMethod.orange(nowLv0 + "") + "</b>层，获得：";
               this.winTip += "\n" + this.now.gift.getDescription(4);
               this.winTip += "\n全体战斗力+" + ComMethod.yellow(this.getOneWholeAddTip());
            }
         }
      }
      
      public function getPointValueObj(father0:String = "") : Object
      {
         var n:* = undefined;
         var prod0:PeakProDefine = null;
         var lv0:int = 0;
         var obj0:Object = this.save.getNow().saveObj;
         var newObj0:Object = {};
         for(n in obj0)
         {
            prod0 = getPointDefine(n);
            if(father0 == "" || prod0.father == father0)
            {
               lv0 = int(obj0[n]);
               newObj0[n] = prod0.getProAdd(lv0);
            }
         }
         return newObj0;
      }
      
      public function getPointValue(name0:String) : Number
      {
         var lv0:int = this.getOneLv(name0);
         var prod0:PeakProDefine = getPointDefine(name0);
         return prod0.getProAdd(lv0);
      }
      
      public function swapPointPlan(label0:String) : Boolean
      {
         if(planArr.indexOf(label0) >= 0)
         {
            this.save.pNow = label0;
            return true;
         }
         return false;
      }
      
      public function getAllPoint() : int
      {
         var oneMax0:int = LEVEL_POINT;
         var v0:int = this.save.unendLv * oneMax0;
         var add0:int = this.save.uP;
         if(add0 > oneMax0)
         {
            add0 = oneMax0;
         }
         return v0 + add0;
      }
      
      public function usePoint(name0:String) : void
      {
         this.save.getNow().addNum(name0,1);
      }
      
      public function setPoint(name0:String, v0:int) : void
      {
         this.save.getNow().setAttribute(name0,v0);
      }
      
      public function delPoint(name0:String) : void
      {
         var num0:int = this.save.getNow().getAttribute(name0);
         num0 -= 1;
         if(num0 < 0)
         {
            num0 = 0;
         }
         this.save.getNow().setAttribute(name0,num0);
      }
      
      public function resetPoint() : void
      {
         this.save.getNow().clearData();
      }
      
      public function getSurplusPoint() : int
      {
         return this.getAllPoint() - this.getUsePoint();
      }
      
      public function getUsePoint() : int
      {
         var n:* = undefined;
         var sum0:int = 0;
         var obj0:Object = this.save.getNow().getEncodeObj();
         for(n in obj0)
         {
            sum0 += this.getUsePointOne(n);
         }
         return sum0;
      }
      
      public function getUsePointOne(name0:String) : Number
      {
         var d0:PeakProDefine = null;
         var lv0:int = this.save.getNow().getAttribute(name0);
         if(lv0 > 0)
         {
            d0 = getPointDefine(name0);
            if(Boolean(d0))
            {
               return d0.getMustPointSum(lv0);
            }
         }
         return 0;
      }
      
      public function getMustPointOne(name0:String) : Number
      {
         var lv0:int = this.save.getNow().getAttribute(name0);
         var d0:PeakProDefine = getPointDefine(name0);
         return d0.getMustPointByLv(lv0 + 1);
      }
      
      public function getOneLv(name0:String) : int
      {
         return int(this.save.getNow().getAttribute(name0));
      }
   }
}

