package dataAll._app.top.extra
{
   import com.sounto.utils.ClassProperty;
   import dataAll._player.role.RoleName;
   import dataAll.body.define.HeroDefine;
   
   public class DpsTopExtra extends TopExtra
   {
      
      public static var pro_arr:Array = [];
      
      public var player:String = "";
      
      public var role:String = "";
      
      public var lv:int = 0;
      
      public var life:Number = 0;
      
      public var headDef:Number = 0;
      
      public var dps:Number = 0;
      
      public var equip:String = "";
      
      public var coin:Number = 0;
      
      public var veh:String = "";
      
      public var head:String = "";
      
      public var post:String = "";
      
      public var es:String = "0000";
      
      public function DpsTopExtra()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      override public function setToSimulated() : void
      {
         this.player = "迈居";
         this.lv = int(Math.random() * 50);
         this.life = int(Math.random() * 500);
         this.headDef = this.life;
         this.dps = this.life * 3;
         this.equip = Gaming.PG.da.equip.getFourEquipImgNameArr().toString();
         this.coin = int(Math.random() * 500000);
         this.veh = Gaming.PG.da.equip.getVehicleName();
         this.head = Gaming.PG.da.head.save.nowHead;
         this.post = "联邦国务卿 10级";
         this.es = "3333";
      }
      
      public function getRole() : String
      {
         if(this.role == "")
         {
            return RoleName.Striker;
         }
         return this.role;
      }
      
      public function getRoleDefine() : HeroDefine
      {
         return Gaming.defineGroup.body.getHeroDefine(this.getRole());
      }
   }
}

