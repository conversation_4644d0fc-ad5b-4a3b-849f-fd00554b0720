package dataAll._app.union.define
{
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class MilitaryDefineGroup
   {
      
      public var arr:Array = [];
      
      private var obj:Object = {};
      
      public function MilitaryDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var x0:XML = null;
         var d0:MilitaryDefine = null;
         var xml_list0:XMLList = xml0.level;
         var totalMust0:Number = 0;
         var firstGift0:GiftAddDefineGroup = null;
         for(i in xml_list0)
         {
            x0 = xml_list0[i];
            d0 = new MilitaryDefine();
            d0.inData_byXML(x0);
            this.arr.push(d0);
            this.obj[d0.name] = d0;
            if(!firstGift0)
            {
               firstGift0 = d0.gift;
            }
            totalMust0 += d0.must;
            d0.totalMust = totalMust0;
            if(<PERSON><PERSON><PERSON>(firstGift0))
            {
               d0.setGiftByFirst(firstGift0);
            }
         }
      }
      
      public function getDefine(name0:String) : MilitaryDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineByContribution(v0:Number) : MilitaryDefine
      {
         var d0:MilitaryDefine = null;
         for each(d0 in this.arr)
         {
            if(v0 < d0.totalMust)
            {
               return d0;
            }
         }
         return this.arr[this.arr.length - 1];
      }
      
      public function getCnNameByContribution(v0:Number) : String
      {
         var d0:MilitaryDefine = this.getDefineByContribution(v0);
         return d0.cnName;
      }
   }
}

