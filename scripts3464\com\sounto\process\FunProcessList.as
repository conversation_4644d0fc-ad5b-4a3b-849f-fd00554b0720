package com.sounto.process
{
   import flash.utils.getTimer;
   
   public class FunPro<PERSON>List extends BaseFunProcess
   {
      
      private var arr:Array = [];
      
      private var nowProPer:Number = 0;
      
      public function FunProcessList()
      {
         super();
      }
      
      public function pushFun(fun0:FunProcessFun) : void
      {
         var pro0:FunProcess = new FunProcess();
         pro0.setFun(fun0);
         this.pushFunProcess(pro0);
      }
      
      public function pushFunProcess(pro0:IO_FunProcess) : void
      {
         this.arr.push(pro0);
      }
      
      override public function start(_yesFun0:Function = null) : void
      {
         state = "ing";
         index = 0;
         loopNum = this.arr.length;
         all_t = 0;
         first_t = getTimer();
         yesFun = _yesFun0;
      }
      
      override public function getProcessPer() : Number
      {
         var pro0:IO_FunProcess = null;
         if(state == "ing")
         {
            pro0 = this.arr[index];
            if(pro0 is IO_FunProcess)
            {
               return this.nowProPer + pro0.getProPer() * pro0.getProcessPer();
            }
            return 1;
         }
         if(state == "overing" || state == "over")
         {
            return 1;
         }
         return 0;
      }
      
      override public function stopAndClear() : void
      {
         var n:* = undefined;
         var pro0:IO_FunProcess = null;
         super.stopAndClear();
         for(n in this.arr)
         {
            pro0 = this.arr[n];
            pro0.stopAndClear();
         }
         this.arr.length = 0;
         this.nowProPer = 0;
         yesFun = null;
      }
      
      override public function FTimer() : void
      {
         var pro0:IO_FunProcess = null;
         var state0:String = null;
         if(state == "ing")
         {
            if(index > loopNum - 1)
            {
               state = "over";
               return;
            }
            pro0 = this.arr[index];
            state0 = pro0.getState();
            if(state0 == "no")
            {
               pro0.start();
            }
            else if(state0 == "over")
            {
               ++index;
               this.nowProPer += pro0.getProPer();
            }
            pro0.FTimer();
         }
         else if(state == "over")
         {
            state = "overing";
            if(yesFun is Function)
            {
               yesFun();
            }
         }
      }
   }
}

