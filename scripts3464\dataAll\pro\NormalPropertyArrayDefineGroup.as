package dataAll.pro
{
   import dataAll.equip.define.EquipColor;
   import dataAll.level.define.unit.UnitType;
   
   public class NormalPropertyArrayDefineGroup extends PropertyArrayDefineGroup
   {
      
      public function NormalPropertyArrayDefineGroup()
      {
         super();
      }
      
      public function getByName(name0:String, lv0:int) : Number
      {
         name0 = "get" + name0;
         return this[name0](lv0);
      }
      
      public function getArmsDps(lv0:int) : Number
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         if(lv0 < 51)
         {
            return lv0 * lv0 * 4 + 20;
         }
         return lv0 * lv0 * 15 - 28000;
      }
      
      public function getLvInArmsDps(dps0:Number) : int
      {
         var dps2:Number = NaN;
         for(var i:int = 1; i < 200; i++)
         {
            dps2 = this.getArmsDps(i);
            if(dps2 >= dps0)
            {
               return i;
            }
         }
         return 200;
      }
      
      public function getHeroLife(lv0:int) : Number
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         return lv0 * lv0 * 10 + 150;
      }
      
      public function getHeroExp(lv0:int) : Number
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         var v0:Number = Math.round(Math.pow(lv0,3.8) * 3.5 + 1000);
         if(lv0 >= 86)
         {
            v0 = Math.round(Math.pow(lv0,3.8) * 20 - 360000000);
         }
         return v0;
      }
      
      public function traceHeroExp() : void
      {
         var s0:String = "";
         for(var i:int = 1; i <= 100; i++)
         {
            s0 += "\n" + "" + this.getHeroExp(i);
         }
         trace(s0);
      }
      
      public function getDps_Life(lv0:int) : Number
      {
         return getPropertyValue(NormalProperty.dps_life,lv0);
      }
      
      public function getPetLife(lv0:int) : Number
      {
         return Math.ceil(this.getHeroLife(lv0));
      }
      
      public function getPetExp(lv0:int) : Number
      {
         return Math.ceil(this.getHeroExp(lv0) * 0.2);
      }
      
      public function getPetDps(lv0:int) : Number
      {
         return this.getArmsDps(lv0);
      }
      
      public function getEnemyLife(lv0:int) : Number
      {
         return getPropertyValue(NormalProperty.enemyBaseLife,lv0);
      }
      
      public function getEnemyDps(lv0:int) : Number
      {
         var v0:Number = getPropertyValue(NormalProperty.enemyBaseDps,lv0);
         if(lv0 <= 75)
         {
            v0 = Math.ceil(v0 * 0.8);
         }
         return v0;
      }
      
      public function getEnemyExp(lv0:int) : Number
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         var v0:Number = Math.round(lv0 * 15 * 3);
         if(lv0 >= 40)
         {
            v0 = Math.round(v0 * 2);
         }
         return v0;
      }
      
      public function getEnemyCoin(lv0:int) : Number
      {
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         return Math.ceil(lv0 * 1.5);
      }
      
      public function getLevelEnemyExp(lv0:int) : Number
      {
         var one0:Number = this.getEnemyExp(lv0);
         return one0 * (75 + 5 * UnitType.getMul("exp","super") + UnitType.getMul("exp","boss"));
      }
      
      public function getLevelEnemyCoin(lv0:int) : Number
      {
         var one0:Number = this.getEnemyCoin(lv0);
         return one0 * (0.1 * 75 + 5 * UnitType.getMul("coin","super") + UnitType.getMul("coin","boss"));
      }
      
      public function test() : void
      {
         for(var i:int = 1; i <= 100; i++)
         {
            trace(i + ":" + this.getEnemyExp(i));
         }
      }
      
      public function getPriceMul(lv0:Number) : Number
      {
         var v0:Number = 1 - (lv0 - 15) / 25;
         if(v0 < 0.4)
         {
            v0 = 0.4;
         }
         return v0;
      }
      
      public function getPlayerCoinIncome(lv0:Number) : Number
      {
         return getPropertyValue("playerCoinIncome",lv0);
      }
      
      public function getLevelCoinIncome(lv0:Number) : Number
      {
         return getPropertyValue("levelCoinIncome",lv0);
      }
      
      public function getLevelCoinIncomeNoMin(lv0:Number) : Number
      {
         if(lv0 < 10)
         {
            lv0 = 10;
         }
         return getPropertyValue("levelCoinIncome",lv0);
      }
      
      public function getLevelCoinIncomePrice(lv0:Number) : Number
      {
         return Math.ceil(getPropertyValue("levelCoinIncome",lv0));
      }
      
      public function getItemsSellPrice(lv0:int, color0:String, type0:String) : Number
      {
         var mul0:Number = lv0 * 3;
         var color_mul0:Number = EquipColor.getPrice(color0);
         if(type0 == "equip" || type0 == "arms")
         {
            return Math.ceil(mul0 * color_mul0);
         }
         return Math.ceil(mul0 * color_mul0 / 5);
      }
   }
}

