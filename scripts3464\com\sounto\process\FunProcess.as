package com.sounto.process
{
   import flash.utils.getTimer;
   
   public class FunProcess extends BaseFunProcess
   {
      
      private var fun:FunProcessFun = null;
      
      public function FunProcess()
      {
         super();
      }
      
      public function setFun(fun0:FunProcessFun) : void
      {
         this.fun = fun0;
         loopNum = this.fun.getLoopNum();
         proPer = fun0.proPer;
      }
      
      override public function start(_yesFun0:Function = null) : void
      {
         state = "ing";
         index = 0;
         all_t = 0;
         first_t = getTimer();
         yesFun = _yesFun0;
      }
      
      override public function stopAndClear() : void
      {
         super.stopAndClear();
         if(this.fun is FunProcessFun)
         {
            this.fun = null;
         }
      }
      
      override public function FTimer() : void
      {
         var i:int = 0;
         var t0:Number = NaN;
         if(state == "ing")
         {
            if(index > loopNum - 1)
            {
               state = "over";
               return;
            }
            for(i = 0; i < loopNum; i++)
            {
               if(index > loopNum - 1)
               {
                  state = "over";
                  return;
               }
               this.fun.loopFun(index);
               ++index;
               t0 = getTimer() - first_t;
               if(t0 > this.fun.maxTime)
               {
                  first_t = getTimer();
                  all_t += t0;
                  return;
               }
            }
         }
         else if(state == "over")
         {
            state = "overing";
            if(yesFun is Function)
            {
               yesFun();
            }
         }
      }
   }
}

