package dataAll._app.partner
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.edit.TorSave;
   
   public class PartnerSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var ability:NumberEncodeObj = new NumberEncodeObj();
      
      public var aiArr:Array = [];
      
      public var aiIndex:int = 0;
      
      public function PartnerSave()
      {
         super();
      }
      
      public static function get AI_MAX() : int
      {
         return 3;
      }
      
      public function get dayExploit() : Number
      {
         return this.CF.getAttribute("dayExploit");
      }
      
      public function set dayExploit(v0:Number) : void
      {
         this.CF.setAttribute("dayExploit",v0);
      }
      
      public function get exploit() : Number
      {
         return this.CF.getAttribute("exploit");
      }
      
      public function set exploit(v0:Number) : void
      {
         this.CF.setAttribute("exploit",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.aiArr = ClassProperty.copySaveArray(obj0["aiArr"],TorSave);
         this.createAi();
      }
      
      public function initSave() : void
      {
         this.createAi();
      }
      
      private function createAi() : void
      {
         var i:int = 0;
         if(this.aiArr.length == 0)
         {
            for(i = 0; i < AI_MAX; i++)
            {
               this.aiArr.push(new TorSave());
            }
            this.aiIndex = 0;
         }
         if(this.aiIndex < 0)
         {
            this.aiIndex = 0;
         }
         else if(this.aiIndex > this.aiArr.length - 1)
         {
            this.aiIndex = this.aiArr.length - 1;
         }
      }
      
      public function newDayCtrl(nowTimeStr0:String) : void
      {
         this.dayExploit = 0;
      }
      
      public function getPoint(name0:String) : Number
      {
         return this.ability.getAttribute(name0);
      }
      
      public function addPoint(name0:String, v0:Number) : void
      {
         this.ability.addAttribute(name0,v0);
      }
      
      public function setPoint(name0:String, v0:Number) : void
      {
         this.ability.setAttribute(name0,v0);
      }
   }
}

