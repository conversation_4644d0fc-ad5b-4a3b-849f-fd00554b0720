package dataAll.equip.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.level.define.unit.UnitType;
   
   public class GiftEquipDataCreator
   {
      
      public function GiftEquipDataCreator()
      {
         super();
      }
      
      public static function getEquipSave(d0:GiftAddDefine) : EquipSave
      {
         var s0:EquipSave = null;
         var fun0:Function = null;
         if(d0.itemsSave is EquipSave)
         {
            s0 = d0.itemsSave.copyOne() as EquipSave;
         }
         else
         {
            fun0 = GiftEquipDataCreator[d0.name];
            if(fun0 is Function)
            {
               s0 = fun0(d0);
            }
         }
         return s0;
      }
      
      private static function drop(d0:GiftAddDefine) : EquipSave
      {
         var hero_lv0:int = Gaming.PG.da.level;
         return Gaming.defineGroup.equipCreator.getSaveByBody(UnitType.NORMAL,hero_lv0,d0.dropName,d0.childType);
      }
      
      private static function dropBoss(d0:GiftAddDefine) : EquipSave
      {
         var hero_lv0:int = Gaming.PG.da.level;
         return Gaming.defineGroup.equipCreator.getSaveByBody(UnitType.BOSS,hero_lv0,d0.dropName,d0.childType);
      }
      
      private static function superBox(d0:GiftAddDefine) : EquipSave
      {
         var hero_lv0:int = Gaming.PG.da.level;
         var colorPro0:Array = [0,0,0.81,0.12,0.07];
         var colorIndex0:int = ComMethod.getPro_byArrSum(colorPro0);
         var color0:String = EquipColor.TYPE_ARR[colorIndex0];
         var lvPro0:Array = [0,0,0,0,0];
         var minLv0:int = int(lvPro0[colorIndex0]);
         var lv0:int = hero_lv0 - int(Math.random() * (minLv0 + 1));
         if(lv0 > hero_lv0)
         {
            lv0 = hero_lv0;
         }
         var type0:String = d0.childType;
         var typeArr0:Array = EquipType.getTypeArrByHeroLv(hero_lv0);
         if(type0 == "")
         {
            type0 = typeArr0[int(Math.random() * typeArr0.length)];
         }
         return Gaming.defineGroup.equipCreator.getSave(color0,lv0,type0,"superBox");
      }
   }
}

