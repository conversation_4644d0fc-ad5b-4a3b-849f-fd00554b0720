package dataAll._app.task.define
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   
   public class TaskConditionDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var target:String = "";
      
      public var targetId:String = "";
      
      private var _value:String = "";
      
      public var cumulativeType:String = "save";
      
      public var time:Number = 0;
      
      public var timeType:String = "win";
      
      public var timeFirst:String = "";
      
      public function TaskConditionDefine()
      {
         super();
         this.value = 0;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function set value(v0:Number) : void
      {
         this._value = Sounto64.encode(String(v0));
      }
      
      public function get value() : Number
      {
         return Number(Sounto64.decode(this._value));
      }
   }
}

