package dataAll._app.top.define
{
   import dataAll._app.union.battle.UnionBattleDefine;
   import dataAll.ui.label.LabelAddData;
   
   public class TopBarAll
   {
      
      private var obj:Object = {};
      
      private var fatherArrObj:Object = {};
      
      private var gatherDefineObj:Object = {};
      
      private var gatherDefineArr:Array = [];
      
      private var arr:Array = [];
      
      public function TopBarAll()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var gather_xml0:XML = null;
         var gatherName0:String = null;
         var gather_d0:TopBarGatherDefine = null;
         var xml_list0:XMLList = null;
         var i:* = undefined;
         var fatherXML0:XML = null;
         var d0:TopBarDefineGroup = null;
         var gather_list0:XMLList = xml0.gather;
         for(n in gather_list0)
         {
            gather_xml0 = gather_list0[n];
            gatherName0 = gather_xml0.@name;
            gather_d0 = new TopBarGatherDefine();
            gather_d0.inData_byXML(gather_xml0);
            this.gatherDefineObj[gather_d0.name] = gather_d0;
            this.gatherDefineArr.push(gather_d0);
            if(!this.fatherArrObj.hasOwnProperty(gatherName0))
            {
               this.fatherArrObj[gatherName0] = [];
            }
            xml_list0 = gather_xml0.father;
            for(i in xml_list0)
            {
               fatherXML0 = xml_list0[i];
               d0 = new TopBarDefineGroup();
               d0.inData_byXML(fatherXML0,gatherName0);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
               this.fatherArrObj[gatherName0].push(d0);
            }
         }
         this.fleshReplaceLabel();
      }
      
      private function fleshReplaceLabel() : void
      {
         var n:* = undefined;
         var d0:TopBarDefineGroup = null;
         var d2:TopBarDefineGroup = null;
         for(n in this.obj)
         {
            d0 = this.obj[n];
            if(d0.replaceLabel != "")
            {
               d2 = this.getDefine(d0.replaceLabel);
               if(d2 is TopBarDefineGroup)
               {
                  d0.arr = d2.arr;
               }
            }
         }
      }
      
      public function getGatherDefine(name0:String) : TopBarGatherDefine
      {
         return this.gatherDefineObj[name0];
      }
      
      public function getDefine(type0:String) : TopBarDefineGroup
      {
         return this.obj[type0];
      }
      
      public function getDefineById(id0:Number) : TopBarDefineGroup
      {
         var d0:TopBarDefineGroup = null;
         var arr_len0:int = int(this.arr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            d0 = this.arr[i];
            if(d0.id == id0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getArrByGather(gather0:String) : Array
      {
         return this.fatherArrObj[gather0];
      }
      
      public function getArenaDefineByLv(lv0:int, excludeHideB0:Boolean = true) : TopBarDefineGroup
      {
         var n:* = undefined;
         var d0:TopBarDefineGroup = null;
         var arr0:Array = this.fatherArrObj["arena"];
         for(n in arr0)
         {
            d0 = arr0[n];
            if(!(excludeHideB0 && d0.hideB))
            {
               if(lv0 <= d0.maxLevel)
               {
                  return d0;
               }
            }
         }
         return arr0[arr0.length - 1];
      }
      
      public function test_getArenaDefineByLv() : void
      {
         for(var i:int = 0; i < 90; i++)
         {
         }
      }
      
      public function addByUnionBattle(dg0:UnionBattleDefine) : void
      {
      }
      
      public function getNormalTopLabelAddData() : LabelAddData
      {
         var f1:TopBarGatherDefine = null;
         var da1:LabelAddData = null;
         var arr2:Array = null;
         var i:* = undefined;
         var d0:TopBarDefineGroup = null;
         var da2:LabelAddData = null;
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("top","普通排行榜","TopUI/bigLabel",0);
         for each(f1 in this.gatherDefineArr)
         {
            if(f1.normalB)
            {
               da1 = new LabelAddData();
               da1.inDataOne(f1.name,f1.cnName,"TopUI/midLabel",0);
               arr2 = this.fatherArrObj[f1.name];
               for(i in arr2)
               {
                  d0 = arr2[i];
                  da2 = new LabelAddData();
                  da2.inDataOne(d0.name,d0.cnName,"TopUI/midLabel",i);
                  da1.addChildData(da2);
               }
               da0.addChildData(da1);
            }
         }
         return da0;
      }
      
      public function getNameArr(gather0:String) : Array
      {
         var n:* = undefined;
         var d0:TopBarDefineGroup = null;
         var arr0:Array = [];
         var arr2:Array = this.getArrByGather(gather0);
         for(n in arr2)
         {
            d0 = arr2[n];
            if(!d0.hideB)
            {
               arr0.push(d0.name);
            }
         }
         return arr0;
      }
      
      public function getCnNameArr(gather0:String) : Array
      {
         var n:* = undefined;
         var d0:TopBarDefineGroup = null;
         var arr0:Array = [];
         var arr2:Array = this.getArrByGather(gather0);
         for(n in arr2)
         {
            d0 = arr2[n];
            if(!d0.hideB)
            {
               arr0.push(d0.cnName);
            }
         }
         return arr0;
      }
   }
}

