package dataAll._app.space.craft
{
   import com.sounto.utils.TextMethod;
   import dataAll._app.peak.IO_PointData;
   import dataAll._app.peak.PeakProDefine;
   import dataAll._base.OneData;
   import dataAll._base.OneSave;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.vehicle.VehicleBulletDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   
   public class CraftData extends OneData implements IO_PointData
   {
      
      private var save:CraftSave;
      
      private var def:CraftDefine;
      
      private var bodyDef:NormalBodyDefine;
      
      private var body:IO_NormalBody = null;
      
      private var uiUpgradeTip:String = "";
      
      public function CraftData()
      {
         super();
      }
      
      public static function get MAX_LV() : int
      {
         return 40;
      }
      
      override public function inData_bySave(s0:OneSave) : void
      {
         super.inData_bySave(s0);
         this.save = s0 as CraftSave;
         this.def = Gaming.defineGroup.craft.getDefine(this.save.n);
         this.bodyDef = Gaming.defineGroup.body.getDefine(this.save.n);
      }
      
      public function overGamingClear() : void
      {
         this.body = null;
      }
      
      public function newWeek() : void
      {
      }
      
      public function get name() : String
      {
         return this.save.n;
      }
      
      public function getCnName() : String
      {
         if(Boolean(this.bodyDef))
         {
            return this.bodyDef.cnName;
         }
         return "";
      }
      
      public function getBodyDef() : NormalBodyDefine
      {
         return this.bodyDef;
      }
      
      public function getBody() : IO_NormalBody
      {
         return this.body;
      }
      
      public function getPointDefArr() : Array
      {
         return Gaming.defineGroup.peakPro.getArrByFather(this.name);
      }
      
      public function getSkillArr() : Array
      {
         var p0:PeakProDefine = null;
         var name0:String = null;
         var lv0:int = 0;
         var n2:String = null;
         var ar0:Array = [];
         var pdefArr0:Array = this.getPointDefArr();
         for each(p0 in pdefArr0)
         {
            name0 = p0.skill;
            if(name0 != "")
            {
               lv0 = this.save.getSkillIndexLv(name0);
               if(lv0 > 0)
               {
                  n2 = name0 + "_" + lv0;
                  ar0.push(n2);
               }
            }
         }
         return ar0;
      }
      
      public function getLife() : Number
      {
         return 800 + (this.level - 1) * 50;
      }
      
      public function getDps() : Number
      {
         return 1800;
      }
      
      private function getVBulletDef() : VehicleBulletDefine
      {
         if(Boolean(this.bodyDef))
         {
            if(this.bodyDef.vBulletArr.length > 0)
            {
               return this.bodyDef.vBulletArr[0];
            }
         }
         return null;
      }
      
      private function getBulletName() : String
      {
         var lv0:int = 0;
         var vb0:VehicleBulletDefine = this.getVBulletDef();
         if(Boolean(vb0))
         {
            lv0 = this.save.getNow().getAttribute(CraftPro.bullet);
            if(lv0 <= 0)
            {
               return vb0.label;
            }
            return vb0.label + "" + lv0;
         }
         return "";
      }
      
      public function setBody(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         this.body = b0;
         if(Boolean(b0))
         {
            dat0 = b0.getData();
            dat0.inCraftData(this);
            dat0.bodyLevel = this.level;
            dat0.setDpsFactor(this.getDps());
            dat0.setNewMaxLife(this.getLife());
            b0.getSkill().addSkill_byNameArr(this.getSkillArr());
            b0.getBullet().lockDefineName(this.getBulletName());
         }
      }
      
      public function getUITipAndShow() : String
      {
         var s0:String = this.uiUpgradeTip;
         this.uiUpgradeTip = "";
         return s0;
      }
      
      public function get level() : int
      {
         return this.save.lv;
      }
      
      public function setLevel(v0:int) : void
      {
         this.save.lv = v0;
      }
      
      public function get exp() : Number
      {
         return this.save.exp;
      }
      
      public function expIsFillB() : Boolean
      {
         var maxExp0:Number = NaN;
         var now0:Number = NaN;
         var level0:int = this.save.lv;
         if(level0 >= MAX_LV)
         {
            maxExp0 = this.getMaxExp(level0);
            now0 = this.save.exp;
            if(now0 >= maxExp0 - 1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function addExp(v0:Number) : Boolean
      {
         var level0:int = 0;
         var level2:int = 0;
         var maxLevel0:int = 0;
         var max0:Number = NaN;
         var now0:Number = NaN;
         var over0:Number = NaN;
         var upgradeB0:Boolean = false;
         v0 = Math.round(v0);
         if(v0 > 0)
         {
            level0 = this.save.lv;
            if(level0 < 1)
            {
               level0 = 1;
            }
            level2 = level0;
            maxLevel0 = MAX_LV;
            max0 = this.getMaxExp(level0);
            now0 = this.save.exp;
            over0 = 0;
            if(level0 >= maxLevel0)
            {
               now0 += v0;
               if(now0 > max0)
               {
                  over0 = now0 - max0 + 1;
                  now0 = max0 - 1;
               }
               this.save.exp = now0;
            }
            else
            {
               now0 += v0;
               while(now0 >= max0)
               {
                  now0 -= max0;
                  level0++;
                  if(level0 >= maxLevel0)
                  {
                     break;
                  }
                  max0 = this.getMaxExp(level0);
               }
               if(level0 >= maxLevel0)
               {
                  if(now0 > max0)
                  {
                     over0 = now0 - max0 + 1;
                     now0 = max0 - 1;
                  }
               }
               this.save.exp = now0;
               if(level0 > level2)
               {
                  this.save.lv = level0;
                  if(Boolean(this.body))
                  {
                     this.body.getData().bodyLevel = level0;
                  }
                  upgradeB0 = true;
                  this.uiUpgradeTip = "飞船等级升至" + TextMethod.color(level0 + "级","#FFFF00") + "！";
                  this.uiUpgradeTip += "\n获得" + TextMethod.color(level0 - level2 + "","#FFFF00") + "技能点数。";
               }
            }
         }
         return upgradeB0;
      }
      
      public function getNowMaxExp() : int
      {
         var lv0:int = this.save.lv;
         if(lv0 < 0)
         {
            lv0 = 0;
         }
         return this.getMaxExp(lv0);
      }
      
      private function getMaxExp(lv0:int) : int
      {
         return Gaming.defineGroup.dataList.getValue("craftExp",lv0);
      }
      
      public function getAllPoint() : int
      {
         return this.level;
      }
      
      public function usePoint(name0:String) : void
      {
         this.save.getNow().addNum(name0,1);
      }
      
      public function setPoint(name0:String, v0:int) : void
      {
         this.save.getNow().setAttribute(name0,v0);
      }
      
      public function delPoint(name0:String) : void
      {
         var num0:int = this.save.getNow().getAttribute(name0);
         num0 -= 1;
         if(num0 < 0)
         {
            num0 = 0;
         }
         this.save.getNow().setAttribute(name0,num0);
      }
      
      public function resetPoint() : void
      {
         this.save.getNow().clearData();
      }
      
      public function getSurplusPoint() : int
      {
         return this.getAllPoint() - this.getUsePoint();
      }
      
      public function getUsePoint() : int
      {
         var n:* = undefined;
         var sum0:int = 0;
         var obj0:Object = this.save.getNow().getEncodeObj();
         for(n in obj0)
         {
            sum0 += this.getUsePointOne(n);
         }
         return sum0;
      }
      
      public function getUsePointOne(name0:String) : Number
      {
         var d0:PeakProDefine = null;
         var lv0:int = this.save.getNow().getAttribute(name0);
         if(lv0 > 0)
         {
            d0 = Gaming.defineGroup.peakPro.getDefine(name0);
            return d0.getMustPointSum(lv0);
         }
         return 0;
      }
      
      public function getMustPointOne(name0:String) : Number
      {
         var lv0:int = this.save.getNow().getAttribute(name0);
         var d0:PeakProDefine = Gaming.defineGroup.peakPro.getDefine(name0);
         return d0.getMustPointByLv(lv0 + 1);
      }
      
      public function getOneLv(name0:String) : int
      {
         return int(this.save.getNow().getAttribute(name0));
      }
   }
}

