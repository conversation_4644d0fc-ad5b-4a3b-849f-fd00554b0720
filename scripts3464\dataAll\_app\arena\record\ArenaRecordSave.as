package dataAll._app.arena.record
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.arena.ArenaCountData;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarSave;
   
   public class ArenaRecordSave
   {
      
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public var sortIndex:Number = 0;
      
      public function ArenaRecordSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],OneArenaRecordSave);
      }
      
      public function addTopBarData(da0:TopBarData, c0:ArenaCountData, time0:String, playerName0:String, normalB0:Boolean) : void
      {
         var id0:String = da0.getPCGId();
         var one0:OneArenaRecordSave = this.getOne(id0);
         var s0:TopBarSave = da0.getTopBarSave();
         if(!one0)
         {
            one0 = new OneArenaRecordSave();
            this.obj[id0] = one0;
         }
         ++this.sortIndex;
         one0.inData(s0,playerName0);
         one0.addNum(c0.winB,time0,this.sortIndex,c0.score,normalB0);
         this.delOneMore30(time0);
      }
      
      public function getOne(id0:String) : OneArenaRecordSave
      {
         return this.obj[id0];
      }
      
      public function delOneMore30(time0:String) : void
      {
         var n:* = undefined;
         var minId0:String = this.getCanDel(time0);
         var minIndex0:Number = 999999999;
         var num0:int = 0;
         for(n in this.obj)
         {
            num0++;
         }
         if(num0 > 30)
         {
            if(minId0 != "")
            {
               this.delOne(minId0);
            }
         }
      }
      
      public function delOne(id0:String) : void
      {
         var n:* = undefined;
         var obj2:Object = {};
         for(n in this.obj)
         {
            if(n != id0)
            {
               obj2[n] = this.obj[n];
            }
         }
         this.obj = obj2;
      }
      
      private function getCanDel(time0:String) : String
      {
         var n:* = undefined;
         var s0:OneArenaRecordSave = null;
         var timeB0:Boolean = false;
         var index0:Number = NaN;
         var minId0:String = "";
         var minIndex0:int = 9999999;
         for(n in this.obj)
         {
            s0 = this.obj[n];
            timeB0 = !(s0.nowIsLastTimeB(time0) && s0.score > 0);
            index0 = s0.getMaxSortIndex();
            if(index0 < minIndex0 && timeB0)
            {
               minIndex0 = index0;
               minId0 = n;
            }
         }
         return minId0;
      }
      
      public function getDataArr() : Array
      {
         var n:* = undefined;
         var s0:OneArenaRecordSave = null;
         var da1:ArenaRecordBarData = null;
         var da2:ArenaRecordBarData = null;
         var arr0:Array = [];
         for(n in this.obj)
         {
            s0 = this.obj[n];
            if(s0.killSortIndex != 0)
            {
               da1 = new ArenaRecordBarData();
               da1.inData(s0,true);
               arr0.push(da1);
            }
            if(s0.dieSortIndex != 0)
            {
               da2 = new ArenaRecordBarData();
               da2.inData(s0,false);
               arr0.push(da2);
            }
         }
         arr0.sort(this.getData_sortFun);
         return arr0;
      }
      
      private function getData_sortFun(da1:ArenaRecordBarData, da2:ArenaRecordBarData) : int
      {
         if(da1.sortIndex > da2.sortIndex)
         {
            return -1;
         }
         if(da1.sortIndex == da2.sortIndex)
         {
            return 0;
         }
         return 1;
      }
      
      public function zuobiPan() : String
      {
         var s0:OneArenaRecordSave = null;
         var str0:String = null;
         var dateStr0:String = null;
         var num0:int = 0;
         var dateObj0:Object = {};
         var lastDate0:StringDate = new StringDate("2015-9-20");
         for each(s0 in this.obj)
         {
            if(s0.score > 0)
            {
               this.addDateObj(dateObj0,s0.killTime != "" ? s0.killTime : s0.dieTime,lastDate0);
            }
         }
         str0 = "";
         for(dateStr0 in dateObj0)
         {
            num0 = int(dateObj0[dateStr0]);
            if(num0 > 5)
            {
               str0 += "【" + dateStr0 + "竞技场次数" + num0 + "》5】";
            }
         }
         return str0;
      }
      
      private function addDateObj(obj0:Object, timeStr0:String, lastDate0:StringDate) : void
      {
         var dateStr0:String = null;
         var now0:StringDate = new StringDate(timeStr0);
         if(now0.reductionOne(lastDate0) >= 0)
         {
            dateStr0 = now0.getDateStr();
            if(!obj0.hasOwnProperty(dateStr0))
            {
               obj0[dateStr0] = 0;
            }
            ++obj0[dateStr0];
         }
      }
   }
}

