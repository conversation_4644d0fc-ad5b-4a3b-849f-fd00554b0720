package com.sounto.oldUtils
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   
   public class OldObjectCF
   {
      
      private var varObj:Object = {};
      
      public function OldObjectCF()
      {
         super();
      }
      
      public function setAttributeString(varName:String, str0:String) : *
      {
         var obj0:Object = JSON2.decode(str0);
         this.setAttribute(varName,obj0);
      }
      
      public function setAttribute(varName:String, varValue:Object) : *
      {
         var i:* = undefined;
         var tmpObj:Object = new Object();
         tmpObj = {"value":Base64.encodeObject(varValue)};
         var tmpObj2:Object = new Object();
         for(i in this.varObj)
         {
            tmpObj2[i] = this.varObj[i];
         }
         tmpObj2[varName] = tmpObj.value;
         tmpObj = null;
         this.varObj = null;
         this.varObj = tmpObj2;
      }
      
      public function getAttribute(varName:String) : Object
      {
         if(this.varObj == null || !this.varObj.hasOwnProperty(varName))
         {
            return null;
         }
         return Base64.decodeObject(this.varObj[varName]);
      }
   }
}

