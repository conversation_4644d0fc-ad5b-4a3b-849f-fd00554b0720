package dataAll._app.edit.boss
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.task.TaskState;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class BossTaskDefine
   {
      
      public static const SIMPLE_LIFE:Array = [3,9,27,81,200,1000];
      
      public static const SIMPLE_LIFE2:Array = [100,1000];
      
      public static const SIMPLE_LIFE1:Array = [1000];
      
      public static const LIFE_1000:Array = [0.1,1,3,9,27,81,200,1000];
      
      public static const DPS_ARR:Array = [0.005,0.02,0.05,0.12,0.3,0.5,0.7,1];
      
      public static const HIGH_LIFE_1000:Array = [0.002,0.05,0.1,0.2,0.5,1,2,1000];
      
      public static const HIGH_DPS_ARR:Array = [0.0005,0.002,0.005,0.012,0.03,0.05,0.1,1];
      
      public static const HIGH_LIFE_MAX:Array = [0.0005,0.002,0.005,0.01,0.02,0.05,0.1,1000];
      
      public var name:String = "";
      
      public var o:String = "";
      
      public var giftCnArr:Array = [];
      
      public var dpsArr:Array = null;
      
      public var lifeArr:Array = null;
      
      public var noSkillArr:Array = null;
      
      public var start:String = "";
      
      public var end:String = "";
      
      public var authorMul:Number = 4;
      
      public function BossTaskDefine()
      {
         super();
      }
      
      public function getDiffLen() : int
      {
         return this.giftCnArr.length;
      }
      
      public function getGiftCn(diff0:int) : String
      {
         return ArrayMethod.getElementLimit(this.giftCnArr,diff0) as String;
      }
      
      public function getGift(diff0:int, authorB0:Boolean) : GiftAddDefineGroup
      {
         var cn0:String = null;
         var cnStr0:String = this.getGiftCn(diff0);
         var cnArr0:Array = cnStr0.split("、");
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(cn0 in cnArr0)
         {
            g0.addGiftByCnStr(cn0);
         }
         if(authorB0)
         {
         }
         return g0;
      }
      
      public function getTmMul(diff0:int) : Number
      {
         var max0:int = this.getDiffLen() - 1;
         var c0:int = max0 - diff0;
         return 1 - c0 / 10;
      }
      
      public function getLifeMul(diff0:int) : Number
      {
         var arr0:Array = LIFE_1000;
         if(Boolean(this.lifeArr))
         {
            arr0 = this.lifeArr;
         }
         var v0:Number = ArrayMethod.getElementLimit(arr0,diff0) as Number;
         return v0 / 1000;
      }
      
      public function getDpsMul(diff0:int) : Number
      {
         var arr0:Array = DPS_ARR;
         if(Boolean(this.dpsArr))
         {
            arr0 = this.dpsArr;
         }
         return ArrayMethod.getElementLimit(arr0,diff0) as Number;
      }
      
      public function getState(now0:StringDate) : String
      {
         var v0:int = 0;
         if(this.start != "" || this.end != "")
         {
            v0 = now0.betweenIn(this.start,this.end,true);
         }
         if(v0 == -1)
         {
            return TaskState.noStart;
         }
         if(v0 == 1)
         {
            return TaskState.isEnd;
         }
         return "";
      }
      
      public function getTip(g0:GiftAddDefineGroup, diff0:int) : String
      {
         var lastGiftCn0:String = g0.getDescription(99,true);
         var s0:String = "";
         s0 += "<blue 难度" + (diff0 + 1) + "奖励：/><b>" + lastGiftCn0 + "</b>";
         if(this.start != "")
         {
            s0 += "\n<blue 开放时间：/>" + this.start;
         }
         if(this.end != "")
         {
            s0 += "\n<blue 结束时间：/>" + this.end;
         }
         return s0;
      }
   }
}

