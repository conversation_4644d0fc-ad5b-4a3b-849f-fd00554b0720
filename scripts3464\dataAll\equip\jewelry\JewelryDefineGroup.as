package dataAll.equip.jewelry
{
   import dataAll.equip.define.EquipDefine;
   
   public class JewelryDefineGroup
   {
      
      private var nowIndex:int = 0;
      
      public var obj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var arr:Array = [];
      
      private var oneNameArr:Array = [];
      
      public var baseNameArr:Array = [];
      
      public function JewelryDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var father0:String = null;
         var xl2:XMLList = null;
         var x2:XML = null;
         var baseLabel0:String = null;
         var d0:JewelryDefine = null;
         var xl0:XMLList = xml0.father;
         for each(x0 in xl0)
         {
            father0 = x0.@name;
            xl2 = x0.equip;
            for each(x2 in xl2)
            {
               baseLabel0 = x2.@baseLabel;
               d0 = this.getDefine(baseLabel0 + "_1");
               if(d0 is JewelryDefine)
               {
                  d0 = d0.cloneBase();
               }
               else
               {
                  d0 = new JewelryDefine();
                  d0.name = baseLabel0;
                  this.oneNameArr.push(baseLabel0 + "_1");
                  this.baseNameArr.push(baseLabel0);
               }
               d0.inData_byXML(x2,father0);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
               this.addInFatherArr(d0,father0);
            }
         }
         this.addNew();
      }
      
      private function addNew() : void
      {
         var d0:JewelryDefine = null;
         var max0:int = 0;
         var i:int = 0;
         var d2:JewelryDefine = null;
         var newskillArr0:Array = null;
         var skillName0:String = null;
         var arr0:Array = this.arr.concat([]);
         for each(d0 in arr0)
         {
            max0 = d0.maxLv;
            for(i = 1; i <= max0; i++)
            {
               d2 = d0.clone();
               d2.name += "_" + i;
               d2.lv = i;
               d2.baseLabel = d0.name;
               newskillArr0 = [];
               for each(skillName0 in d0.skillArr)
               {
                  newskillArr0.push(skillName0 + "_" + i);
               }
               d2.skillArr = newskillArr0;
               this.obj[d2.name] = d2;
            }
         }
      }
      
      private function addInFatherArr(d0:EquipDefine, father0:String) : void
      {
         d0.index = this.nowIndex;
         ++this.nowIndex;
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function getDefine(name0:String) : JewelryDefine
      {
         return this.obj[name0];
      }
      
      public function getNormalBaseNameArr() : Array
      {
         return this.baseNameArr;
      }
      
      public function getJewelryDefineByCn(cn0:String) : JewelryDefine
      {
         var d0:JewelryDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getIconNum() : int
      {
         var d0:EquipDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconLabel))
            {
               obj0[d0.iconLabel] = 0;
               num0++;
            }
         }
         return num0;
      }
   }
}

