package dataAll.equip.creator
{
   import com.common.data.Base64;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.arms.creator.ArmsRemakeCtrl;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.creator.OneProData;
   import dataAll.must.define.MustDefine;
   
   public class EquipRemakeCtrl
   {
      
      public function EquipRemakeCtrl()
      {
         super();
      }
      
      public static function getRemake(s0:EquipSave, proDataArr0:Array) : EquipSave
      {
         var newS0:EquipSave = null;
         for(var i:int = 0; i < 30; i++)
         {
            newS0 = getRemakeOne(s0,proDataArr0);
            if(!samePan(s0,newS0))
            {
               return newS0;
            }
         }
         return null;
      }
      
      private static function getRemakeOne(s0:EquipSave, proDataArr0:Array) : EquipSave
      {
         var newS0:EquipSave = s0.clone();
         newS0.skillArr = [];
         remakeSkill(s0,proDataArr0,newS0);
         return newS0;
      }
      
      private static function samePan(s0:EquipSave, s1:EquipSave) : Boolean
      {
         var skillArr0:Array = s0.getSkillArr().concat([]);
         skillArr0.sort();
         var skillArr1:Array = s1.getSkillArr().concat([]);
         skillArr1.sort();
         return Base64.encodeObject(skillArr0) == Base64.encodeObject(skillArr1);
      }
      
      private static function remakeSkill(s0:EquipSave, proDataArr0:Array, newS0:EquipSave) : void
      {
         var da0:OneProData = null;
         var num0:int = 0;
         var haveArr0:Array = [];
         for each(da0 in proDataArr0)
         {
            if(da0.lockB && da0.type == "skill")
            {
               haveArr0.push(da0.name);
            }
         }
         num0 = s0.getSkillArr().length - haveArr0.length;
         if(num0 == 0)
         {
            num0 = 1;
         }
         var allArr0:Array = EquipSkillCreator.getSkillNameArr(s0.partType);
         var canArr0:Array = ComMethod.deductArr(allArr0,haveArr0);
         var newArr0:Array = ComMethod.getRandomArray(canArr0,num0);
         newS0.skillArr = sortSkillArrByOther(haveArr0.concat(newArr0),s0.skillArr);
      }
      
      private static function sortSkillArrByOther(arr0:Array, arr1:Array) : Array
      {
         var s0:String = null;
         var f0:int = 0;
         var newArr0:Array = [];
         for each(s0 in arr1)
         {
            f0 = int(arr0.indexOf(s0));
            if(f0 >= 0)
            {
               newArr0.push(s0);
               arr0.splice(f0,1);
            }
         }
         return newArr0.concat(arr0);
      }
      
      public static function setAllByOther(s0:EquipSave, otherS0:EquipSave) : void
      {
         s0.obj = ClassProperty.copyObj(otherS0.obj);
         s0.skillArr = otherS0.getSkillArr().concat([]);
         s0.lockB = true;
      }
      
      public static function getMust(s0:EquipSave, proDataArr0:Array) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var lv0:int = s0.getTrueLevel();
         d0.lv = lv0;
         if(lv0 < EquipSkillCreator.maxLevel)
         {
            d0.lv = EquipSkillCreator.maxLevel;
         }
         d0.coin = Gaming.defineGroup.normal.getLevelCoinIncome(lv0);
         var b0:Number = getMustBaseValue(lv0);
         var p0:Number = getMustByLockNum(proDataArr0);
         var n0:Number = getMustByProNum(proDataArr0.length);
         var c0:Number = getMustByColor(s0.color);
         var skill0:Number = getMustBySkillNum(s0.getSkillArr().length);
         var type0:Number = 1;
         var num0:int = Math.ceil((b0 * p0 + b0 * n0) * c0 * skill0 * type0);
         d0.inThingsDataByArr(["godStone;" + num0]);
         return d0;
      }
      
      private static function getMustByColor(color0:String) : Number
      {
         if(color0 == EquipColor.RED || color0 == EquipColor.BLACK)
         {
            return 2;
         }
         if(EquipColor.moreBlackB(color0))
         {
            return 3;
         }
         return 1;
      }
      
      private static function getMustByLockNum(proDataArr0:Array) : Number
      {
         var da0:OneProData = null;
         var lockNum0:int = 0;
         for each(da0 in proDataArr0)
         {
            if(da0.lockB)
            {
               lockNum0++;
            }
         }
         return lockNum0 * 0.5;
      }
      
      private static function getMustBaseValue(lv0:int) : Number
      {
         return ArmsRemakeCtrl.getMustBaseValue(lv0);
      }
      
      private static function getMustByProNum(num0:int) : Number
      {
         return 3;
      }
      
      private static function getMustBySkillNum(num0:int) : Number
      {
         if(num0 == 0)
         {
            return 1.3;
         }
         return 1;
      }
   }
}

