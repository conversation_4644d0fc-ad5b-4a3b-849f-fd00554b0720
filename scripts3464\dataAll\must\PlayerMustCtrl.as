package dataAll.must
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.api.shop.ShopBuyObject;
   import UI.base.alert.AlertBox;
   import UI.pay.PayCtrl;
   import dataAll.must.define.MustDefine;
   
   public class PlayerMustCtrl
   {
      
      private static var noFun:Function;
      
      private static var yesFun:Function;
      
      private static var nowDefine:MustDefine;
      
      private static var numBuy_yesFun:Function;
      
      private static var numBuy_mustDefine:MustDefine;
      
      private static var simpleBuy_yesFun:Function;
      
      private static var simpleBuy_mustDefine:MustDefine;
      
      private static var deductMustAlert_yesFun:Function;
      
      public function PlayerMustCtrl()
      {
         super();
      }
      
      private static function get alertBox() : AlertBox
      {
         return Gaming.uiGroup.alertBox;
      }
      
      public static function deductMustAlert(d0:MustDefine, infoStr0:String, _yesFun0:Function = null) : void
      {
         var bb0:Boolean = d0.panCondition();
         simpleBuy_yesFun = _yesFun0;
         simpleBuy_mustDefine = d0;
         alertBox.showChoose(infoStr0 + "\n" + d0.getText(),yes_deductMustAlert);
         alertBox.setYesActived(bb0);
      }
      
      private static function yes_deductMustAlert() : void
      {
         deductMustAndPan(simpleBuy_mustDefine,simpleBuy_yesFun);
      }
      
      public static function deductMustAndPan(d0:MustDefine, _yesFun0:Function = null, _noFun0:Function = null) : void
      {
         var error0:String = d0.panConditionStr();
         if(error0 == "")
         {
            deductMust(d0,_yesFun0,_noFun0);
         }
         else
         {
            UIOrder.alertError(error0);
         }
      }
      
      public static function deductMust(d0:MustDefine, _yesFun0:Function = null, _noFun0:Function = null) : void
      {
         var shopObj0:ShopBuyObject = null;
         yesFun = _yesFun0;
         noFun = _noFun0;
         nowDefine = d0;
         if(d0.money > 0)
         {
            shopObj0 = d0.getShopObj();
            Gaming.uiGroup.connectUI.show();
            Gaming.api.shop.buyPropNd(shopObj0,doYesFun,doNoFun);
         }
         else
         {
            doYesFun();
         }
      }
      
      private static function doYesFun() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var d0:MustDefine = nowDefine;
         Gaming.PG.da.useThingsByMustDefine(d0);
         Gaming.PG.da.main.useCoin(d0.coin);
         if(nowDefine.money > 0)
         {
            Gaming.api.useMoneyMust(d0);
         }
         nowDefine = null;
         UIShow.flesh_coinChange();
         if(yesFun is Function)
         {
            yesFun();
         }
      }
      
      private static function doNoFun(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(noFun is Function)
         {
            noFun(str0);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("购买失败！\n" + str0);
         }
      }
      
      public static function numBuy(must_d0:MustDefine, name0:String, canNum0:int, yesFun0:Function, oneNum0:int = 1) : void
      {
         if(canNum0 <= 0 && canNum0 != -1)
         {
            Gaming.uiGroup.alertBox.showError("今天已经无法再增加" + name0 + "了。");
            initNumBuy();
            return;
         }
         var mustB0:Boolean = must_d0.panCondition();
         if(!mustB0)
         {
            Gaming.uiGroup.alertBox.showPay();
            initNumBuy();
            return;
         }
         numBuy_yesFun = yesFun0;
         numBuy_mustDefine = must_d0;
         var str0:String = must_d0.getNumBuyText(name0,canNum0,oneNum0);
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",showAddNum);
      }
      
      private static function showAddNum() : void
      {
         deductMust(numBuy_mustDefine,numBuy_yesFun);
      }
      
      private static function initNumBuy() : void
      {
         numBuy_yesFun = null;
         numBuy_mustDefine = null;
      }
      
      public static function simpleBuy(must_d0:MustDefine, infoStr0:String, yesFun0:Function) : void
      {
         var str0:String = infoStr0 + "\n" + must_d0.getText();
         var enoughB0:Boolean = must_d0.panCondition();
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",enoughB0 ? yes_simpleBuy : PayCtrl.gotoPay);
         if(!enoughB0)
         {
            Gaming.uiGroup.alertBox.yesBtnToPay();
         }
         else
         {
            simpleBuy_mustDefine = must_d0;
            simpleBuy_yesFun = yesFun0;
         }
      }
      
      private static function yes_simpleBuy() : void
      {
         deductMust(simpleBuy_mustDefine,simpleBuy_yesFun);
      }
   }
}

