package UI.api.top
{
   import UI.test.SaveTestBox;
   import com.adobe.serialization.json.JSON2;
   import dataAll._app.login.SaveData4399;
   import dataAll._app.top.simulated.SimulatedTopBarDataGroup;
   import unit4399.events.RankListEvent;
   
   public class TopAPI
   {
      
      public var clearScoreB:Boolean = false;
      
      public var ID1:int = 142;
      
      private var getOneRankInfo_saveIndex:int = 0;
      
      private var submitScore_yesFun:Function = null;
      
      private var submitScore_noFun:Function = null;
      
      private var getRankLists_yesFun:Function = null;
      
      private var getRankLists_noFun:Function = null;
      
      private var getRankListByOwn_yesFun:Function = null;
      
      private var getRankListByOwn_noFun:Function = null;
      
      private var getOneRankInfo_yesFun:Function = null;
      
      private var getOneRankInfo_noFun:Function = null;
      
      private var getUserData_yesFun:Function = null;
      
      private var getUserData_noFun:Function = null;
      
      public function TopAPI()
      {
         super();
      }
      
      public function High_API() : *
      {
      }
      
      public function addListener(stage0:*) : *
      {
         stage0.addEventListener(RankListEvent.RANKLIST_ERROR,this.onRankListErrorHandler);
         stage0.addEventListener(RankListEvent.RANKLIST_SUCCESS,this.onRankListSuccessHandler);
      }
      
      public function outLoginEvent() : void
      {
      }
      
      public function getOneRankInfo(rankListId:uint, uname:String, saveIndex0:int, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.getOneRankInfo_yesFun = _yesFun;
         this.getOneRankInfo_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            this.getOneRankInfo_saveIndex = saveIndex0;
            Gaming.serviceHold.getOneRankInfo(rankListId,uname);
         }
         else if(this.getOneRankInfo_yesFun is Function)
         {
            this.getOneRankInfo_yesFun(SimulatedTopBarDataGroup.getOneRankInfo(rankListId, uname));
         }
      }
      
      private function decodeOneRankInfo(dataArr0:Array) : void
      {
         var n:* = undefined;
         var obj0:Object = null;
         SaveTestBox.addText(JSON2.encode(dataArr0));
         if(Boolean(dataArr0))
         {
            if(dataArr0.length > 0)
            {
               if(this.getOneRankInfo_yesFun is Function)
               {
                  for(n in dataArr0)
                  {
                     obj0 = dataArr0[n];
                     if(obj0.index == this.getOneRankInfo_saveIndex)
                     {
                        this.getOneRankInfo_yesFun(obj0);
                        return;
                     }
                  }
               }
            }
         }
         if(this.getOneRankInfo_noFun is Function)
         {
            this.getOneRankInfo_noFun("没有排名数据。");
         }
      }
      
      public function getRankListByOwn(rankListId:uint, idx:uint, rankNum:uint, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.getRankListByOwn_yesFun = _yesFun;
         this.getRankListByOwn_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            Gaming.serviceHold.getRankListByOwn(rankListId,idx,rankNum);
         }
         else if(_noFun is Function)
         {
            if(this.getRankListByOwn_yesFun is Function)
            {
               this.getRankListByOwn_yesFun(SimulatedTopBarDataGroup.getRankListsData(rankListId,12,1));
            }
         }
      }
      
      private function decodeRankListByOwnInfo(dataAry:Array) : void
      {
         if(dataAry == null)
         {
            dataAry = [];
         }
         if(this.getRankListByOwn_yesFun is Function)
         {
            this.getRankListByOwn_yesFun(dataAry);
         }
      }
      
      public function submitScore(obj0:Object, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var rankInfoAry:Array = null;
         SaveTestBox.addText("submitScore：" + JSON2.encode(obj0));
         this.submitScore_yesFun = _yesFun;
         this.submitScore_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            rankInfoAry = [obj0];
            Gaming.serviceHold.submitScoreToRankLists(Gaming.PG.getSaveIndex(),rankInfoAry);
         }
         else if(this.submitScore_yesFun is Function)
         {
            this.submitScore_yesFun(SimulatedTopBarDataGroup.submitScore(obj0));
         }
      }
      
      public function submitScoreArray(objArr0:Array, _yesFun:Function = null, _noFun:Function = null) : *
      {
         var rankInfoAry:Array = null;
         this.submitScore_yesFun = _yesFun;
         this.submitScore_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            rankInfoAry = objArr0;
            Gaming.serviceHold.submitScoreToRankLists(Gaming.PG.getSaveIndex(),rankInfoAry);
         }
         else if(this.submitScore_yesFun is Function)
         {
            this.submitScore_yesFun(SimulatedTopBarDataGroup.submitScore(objArr0[0]));
         }
      }
      
      private function decodeSumitScoreInfo(dataAry:Array) : void
      {
         if(dataAry == null || dataAry.length == 0)
         {
            if(this.submitScore_noFun is Function)
            {
               this.submitScore_noFun("返回数据有问题");
            }
            return;
         }
         var tmpObj:Object = dataAry[0];
         if(tmpObj.code == "10000")
         {
            if(this.submitScore_yesFun is Function)
            {
               this.submitScore_yesFun(tmpObj);
            }
         }
         else if(this.submitScore_noFun is Function)
         {
            SaveTestBox.addText("decodeSumitScoreInfo信息：" + tmpObj.message);
            this.submitScore_noFun("信息：" + tmpObj.message);
         }
      }
      
      public function getRankListsData(rankListId:uint, pageSize:uint, pageNum:uint, _yesFun:Function = null, _noFun:Function = null) : *
      {
         this.getRankLists_yesFun = _yesFun;
         this.getRankLists_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            Gaming.serviceHold.getRankListsData(rankListId,pageSize,pageNum);
         }
         else if(this.getRankLists_yesFun is Function)
         {
            this.getRankLists_yesFun(SimulatedTopBarDataGroup.getRankListsData(rankListId,pageSize,pageNum));
         }
      }
      
      private function decodeRankListInfo(dataAry:Array) : void
      {
         if(dataAry == null)
         {
            dataAry = [];
         }
         if(this.getRankLists_yesFun is Function)
         {
            this.getRankLists_yesFun(dataAry);
         }
      }
      
      public function getUserData(uid:String, idx:uint, _yesFun:Function = null, _noFun:Function = null) : void
      {
         var obj0:Object = null;
         var beforeStr0:String = null;
         this.getUserData_yesFun = _yesFun;
         this.getUserData_noFun = _noFun;
         if(Gaming.serviceHold)
         {
            Gaming.serviceHold.getUserData(uid,idx);
         }
         else
         {
            obj0 = null;
            beforeStr0 = Gaming.uiGroup.testUI.save4399.getBeforeTextStr();
            if(beforeStr0 != "")
            {
               obj0 = SimulatedTopBarDataGroup.getUserDataByObj(JSON2.decode(beforeStr0));
            }
            if(obj0 == null)
            {
               obj0 = SimulatedTopBarDataGroup.getUserData(uid,idx);
            }
            this.decodeUserData(obj0);
         }
      }
      
      private function decodeUserData(dataObj:Object) : void
      {
         var ud0:SaveData4399 = null;
         if(dataObj == null || dataObj == "")
         {
            if(this.getUserData_noFun is Function)
            {
               this.getUserData_noFun("没找到指定存档。");
            }
         }
         else if(this.getUserData_yesFun is Function)
         {
            ud0 = new SaveData4399();
            ud0.inData_byObj(dataObj);
            this.getUserData_yesFun(ud0);
         }
      }
      
      private function onRankListErrorHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var str:String = "apiFlag:" + obj.apiName + "   errorCode:" + obj.code + "   message:" + obj.message;
         switch(obj.apiName)
         {
            case "1":
               if(this.getOneRankInfo_noFun is Function)
               {
                  this.getOneRankInfo_noFun(str);
               }
            case "2":
               if(this.getRankListByOwn_noFun is Function)
               {
                  this.getRankListByOwn_noFun(str);
               }
               break;
            case "4":
               if(this.getRankLists_noFun is Function)
               {
                  this.getRankLists_noFun(str);
               }
               break;
            case "3":
               if(this.submitScore_noFun is Function)
               {
                  this.submitScore_noFun(str);
               }
         }
      }
      
      private function onRankListSuccessHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var data:* = obj.data;
         switch(obj.apiName)
         {
            case "1":
               this.decodeOneRankInfo(data);
            case "2":
               this.decodeRankListByOwnInfo(data);
               break;
            case "4":
               this.decodeRankListInfo(data);
               break;
            case "3":
               this.decodeSumitScoreInfo(data);
               break;
            case "5":
               this.decodeUserData(data);
         }
      }
   }
}

