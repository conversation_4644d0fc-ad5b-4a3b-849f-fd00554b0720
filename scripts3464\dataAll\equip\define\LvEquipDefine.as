package dataAll.equip.define
{
   import dataAll.things.define.ThingsDefine;
   
   public class LvEquipDefine extends EquipDefine
   {
      
      public var baseLabel:String = "";
      
      public function LvEquipDefine()
      {
         super();
      }
      
      public function get lv() : Number
      {
         return CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         CF.setAttribute("lv",v0);
      }
      
      override public function getComposeThingsDefine() : ThingsDefine
      {
         return Gaming.defineGroup.things.getDefine(this.baseLabel + "_1");
      }
   }
}

