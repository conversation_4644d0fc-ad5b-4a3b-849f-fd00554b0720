package dataAll.ui.text
{
   import dataAll.ui.color.UIColor;
   
   public class ProTipType
   {
      
      public function ProTipType()
      {
         super();
      }
      
      public static function getBookTitleMixed(str0:String) : String
      {
         return ProFirstIcon.getM(ProFirstIcon.bookPoint) + UIColor.mixed(str0,UIColor.greeness);
      }
      
      public static function getBookProMixed(cn0:String, value0:String) : String
      {
         return UIColor.mixed(cn0 + "",UIColor.blove) + value0;
      }
      
      public static function getTitleMixed(s0:String, colorName0:String = "") : String
      {
         if(colorName0 == "")
         {
            colorName0 = "blue";
         }
         return "<i1>|<" + colorName0 + " <b>" + s0 + "</b>/>";
      }
   }
}

