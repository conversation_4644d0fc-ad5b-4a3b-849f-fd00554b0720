package dataAll._app.union.building.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.things.define.ThingsDefine;
   
   public class UnionGeologyThingsDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var type:String = "";
      
      public function UnionGeologyThingsDefine()
      {
         super();
         this.max = 0;
         this.treaPro = 0;
         this.orePro = 0;
      }
      
      public function get max() : Number
      {
         return this.CF.getAttribute("max");
      }
      
      public function set max(v0:Number) : void
      {
         this.CF.setAttribute("max",v0);
      }
      
      public function get treaPro() : Number
      {
         return this.CF.getAttribute("treaPro");
      }
      
      public function set treaPro(v0:Number) : void
      {
         this.CF.setAttribute("treaPro",v0);
      }
      
      public function get orePro() : Number
      {
         return this.CF.getAttribute("orePro");
      }
      
      public function set orePro(v0:Number) : void
      {
         this.CF.setAttribute("orePro",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         var thingsD0:ThingsDefine = Gaming.defineGroup.things.getDefineByCnName(this.cnName);
         if(Boolean(thingsD0))
         {
            this.name = thingsD0.name;
         }
         else
         {
            this.name = this.cnName;
         }
      }
      
      public function isCountB() : Boolean
      {
         return this.type == "";
      }
   }
}

