package dataAll._app.edit.card
{
   import UI.test.SaveTestBox;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._base.OneData;
   import dataAll._base.OneSave;
   import dataAll._player.PlayerData;
   import dataAll._player.define.MainPlayerType;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import dataAll.ui.text.ProTipType;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   
   public class BossCardData extends OneData implements IO_GiftDefine
   {
      
      protected var fatherData:BossCardDataGroup = null;
      
      protected var cardSave:BossCardSave;
      
      protected var bodyDef:NormalBodyDefine = null;
      
      public var newB:Boolean = false;
      
      protected var fightBody:IO_NormalBody;
      
      private var secI:int = 0;
      
      public var pkB:Boolean = false;
      
      protected var maxProArr:Array = null;
      
      public function BossCardData()
      {
         super();
      }
      
      public static function getEvoChildNext(star0:int) : int
      {
         if(star0 == 5)
         {
            return 4;
         }
         return 0;
      }
      
      public static function getEvoChildNextStar(star0:int) : int
      {
         if(star0 == 5)
         {
            return star0 + 1;
         }
         return 0;
      }
      
      public static function getEvoChildStar(star0:int) : int
      {
         var now0:int = star0;
         if(now0 == 4)
         {
            return 4;
         }
         if(now0 == 6)
         {
            return 5;
         }
         if(now0 == 7)
         {
            return 7;
         }
         return 7;
      }
      
      public static function panEvoChildB(star0:int, childStar0:int) : Boolean
      {
         var must0:int = getEvoChildStar(star0);
         var next0:int = getEvoChildNextStar(must0);
         if(childStar0 == must0 || childStar0 == next0)
         {
            return true;
         }
         return false;
      }
      
      public static function panEvoChildData(da0:BossCardData, childDa0:BossCardData) : Boolean
      {
         if(da0 != childDa0)
         {
            if(panEvoChildB(da0.getStar(),childDa0.getStar()))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function countEvoChildAdd(star0:int, childStar0:int) : int
      {
         var must0:int = getEvoChildStar(star0);
         var next0:int = getEvoChildNextStar(must0);
         if(childStar0 == must0)
         {
            return 1;
         }
         if(childStar0 == next0)
         {
            return getEvoChildNext(must0);
         }
         return 0;
      }
      
      override public function inData_bySave(s0:OneSave) : void
      {
         super.inData_bySave(s0);
         this.cardSave = s0 as BossCardSave;
         this.bodyDef = Gaming.defineGroup.body.getDefine(this.cardSave.n);
      }
      
      public function setFatherData(dg0:BossCardDataGroup) : void
      {
         this.fatherData = dg0;
      }
      
      public function getCardSave() : BossCardSave
      {
         return this.cardSave;
      }
      
      public function get id() : String
      {
         return this.cardSave.id;
      }
      
      public function toGiftID() : void
      {
         if(this.isGiftID() == false)
         {
            this.cardSave.id = "g" + this.cardSave.id;
         }
      }
      
      public function isGiftID() : Boolean
      {
         if(this.id.substr(0,1) == "g")
         {
            return true;
         }
         return false;
      }
      
      public function get cnName() : String
      {
         return this.bodyDef.cnName;
      }
      
      public function getTipTitle() : String
      {
         return this.getStar() + "星·魂卡";
      }
      
      public function getIconUrl() : String
      {
         if(Boolean(this.bodyDef))
         {
            return this.bodyDef.headIconUrl;
         }
         return "";
      }
      
      public function getStar() : int
      {
         return this.cardSave.s;
      }
      
      public function getUISecMc() : String
      {
         if(Boolean(this.maxProArr))
         {
            return "max";
         }
         return "";
      }
      
      public function getBodyName() : String
      {
         return this.cardSave.n;
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return this.bodyDef;
      }
      
      public function getSmallFrame(fightB0:Boolean, delB0:Boolean) : int
      {
         if(fightB0)
         {
            return 2;
         }
         if(delB0 && this.lockB == false)
         {
            return 4;
         }
         if(this.newB)
         {
            return 3;
         }
         return 1;
      }
      
      public function getIconNewLabel() : String
      {
         if(this.lockB)
         {
            if(this.cardSave.ev > 0)
            {
               return "lockGift";
            }
            if(this.isGiftID())
            {
               return "evo";
            }
            return "lock";
         }
         return "";
      }
      
      public function getAddObj() : Object
      {
         return this.cardSave.o;
      }
      
      public function getDpsAllBlack() : Number
      {
         return ObjectMethod.getEleIfHave(this.cardSave.o,"dpsAllBlack",0);
      }
      
      public function get lockB() : Boolean
      {
         return this.cardSave.lk == 1;
      }
      
      public function set lockB(bb0:Boolean) : void
      {
         if(bb0)
         {
            this.cardSave.lk = 1;
         }
         else
         {
            this.cardSave.lk = 0;
         }
      }
      
      public function getVipGiftMust() : int
      {
         return this.cardSave.getVipMust();
      }
      
      public function getAchieveGiftMust() : int
      {
         return int(this.id.replace("achieveGift",""));
      }
      
      public function getAchieveGiftSaveId() : String
      {
         var str0:String = this.id.replace("achieveGift","");
         return str0.replace("00","");
      }
      
      public function getSkillNameArr() : Array
      {
         return BossCardCreator.getSkillNameArrByIdArr(this.cardSave.sr);
      }
      
      public function getSumSkillArr() : Array
      {
         var bossArr0:Array = null;
         var arr0:Array = this.getSkillNameArr();
         if(Boolean(this.bodyDef))
         {
            bossArr0 = this.bodyDef.getBossSkillArr(false,false);
            ArrayMethod.addNoRepeatArrInArr(arr0,bossArr0);
         }
         arr0.push("sumBossAtten");
         arr0.push("alloyShell_8");
         return arr0;
      }
      
      public function getHaveSkillIdArr() : Array
      {
         var baseSkillArr0:Array = this.getBodyDefine().getEditSkillArr();
         var baseSkillIDArr0:Array = BossCardCreator.getIdArrBySkillNameArr(baseSkillArr0);
         return baseSkillIDArr0.concat(this.cardSave.sr);
      }
      
      public function getLv() : int
      {
         return 99;
      }
      
      private function getSumLife() : Number
      {
         var mul0:Number = this.cardSave.li;
         var baseLife0:Number = 5 * Gaming.defineGroup.normal.getEnemyDps(this.getLv());
         return Math.ceil(baseLife0 * mul0);
      }
      
      private function getSumDps() : Number
      {
         var mul0:Number = this.cardSave.dp;
         var base0:Number = 0.2 * Gaming.defineGroup.normal.getEnemyLife(this.getLv());
         return Math.ceil(base0 * mul0);
      }
      
      public function getUnitOrderDefine(heroId0:String, camp0:String, pd0:PlayerData = null) : OneUnitOrderDefine
      {
         var d0:OneUnitOrderDefine = null;
         var bodyD0:NormalBodyDefine = this.getBodyDefine();
         d0 = new OneUnitOrderDefine();
         d0.aiOrder = "followBodyAttack:" + heroId0;
         d0.warningRange = 3000;
         d0.camp = camp0;
         d0.name = bodyD0.name;
         d0.fleshBodyName();
         d0.id = "bossCard_" + bodyD0.cnName;
         if(Boolean(pd0) && pd0.mainPlayerType != MainPlayerType.ME)
         {
            d0.id = heroId0 + "_" + d0.id;
         }
         return d0;
      }
      
      public function getFightBody() : IO_NormalBody
      {
         return this.fightBody;
      }
      
      public function overGamingClear() : void
      {
         this.fightBody = null;
         this.secI = 0;
      }
      
      public function setFightBody(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         this.fightBody = b0;
         if(Boolean(b0))
         {
            dat0 = b0.getData();
            dat0.inBossCardData(this);
            dat0.bodyLevel = this.getLv();
            dat0.setDpsFactor(this.getSumDps());
            dat0.setNewMaxLife(this.getSumLife());
            b0.getSkill().addSkill_byNameArr(this.getSumSkillArr());
            SaveTestBox.addText("魂卡：" + dat0.define.cnName + "：life=" + dat0.maxLife + "  dps=" + dat0.dpsFactor);
            SaveTestBox.addText("技能：" + Gaming.defineGroup.skill.getCnArrByNameArr(this.getSumSkillArr()));
         }
      }
      
      public function isFightB() : Boolean
      {
         if(Boolean(this.fatherData))
         {
            this.fatherData.fightPan(this);
         }
         return false;
      }
      
      public function getFatherData() : BossCardDataGroup
      {
         return this.fatherData;
      }
      
      public function bodyTimerEvent() : void
      {
         if(this.fatherData == null)
         {
            return;
         }
         if(Gaming.LG.isCardPKB())
         {
            return;
         }
         this.secI += 1;
         if(this.secI >= 30)
         {
            this.secI -= 30;
            this.fatherData.bodyTimerSecond(this);
         }
      }
      
      public function getPKUnitOrderDefine(id0:String, camp0:String, followId0:String) : OneUnitOrderDefine
      {
         var bodyD0:NormalBodyDefine = this.getBodyDefine();
         var d0:OneUnitOrderDefine = new OneUnitOrderDefine();
         d0.aiOrder = "followBodyAttack:" + followId0;
         d0.warningRange = 3000;
         d0.camp = camp0;
         d0.name = bodyD0.name;
         d0.fleshBodyName();
         d0.id = id0;
         return d0;
      }
      
      public function dealPKBody(b0:IO_NormalBody) : void
      {
         var dat0:NormalBodyData = null;
         if(Boolean(b0))
         {
            dat0 = b0.getData();
            dat0.inBossCardData(this);
            dat0.bodyLevel = this.getLv();
            dat0.setDpsFactor(this.getSumDps() * 0.02);
            dat0.setNewMaxLife(this.getSumLife());
            b0.getSkill().addSkill_byNameArr(this.getSumSkillArr());
            SaveTestBox.addText("PK魂卡：" + dat0.define.cnName + "：life=" + dat0.maxLife + "  dps=" + dat0.dpsFactor);
            SaveTestBox.addText("PK技能：" + Gaming.defineGroup.skill.getCnArrByNameArr(this.getSumSkillArr()));
         }
      }
      
      public function canEvoB() : Boolean
      {
         var star0:int = this.getStar();
         if(star0 == 6 || star0 == 7)
         {
            return true;
         }
         return false;
      }
      
      public function getEvoMustStar() : int
      {
         return getEvoChildStar(this.getStar());
      }
      
      public function getEvoMustNum() : int
      {
         var now0:int = this.getStar();
         if(now0 == 4)
         {
            return 20;
         }
         if(now0 == 6)
         {
            return 10;
         }
         if(now0 == 7)
         {
            return 2;
         }
         return 999;
      }
      
      public function evoEvent() : void
      {
         ++this.cardSave.ev;
         if(Boolean(this.fatherData))
         {
            this.fatherData.evoEvent(this);
         }
      }
      
      public function getEvoChildAlert() : String
      {
         var tip0:String = "";
         if(this.isFightB())
         {
            tip0 = "目标处于出战状态";
         }
         else if(this.isGiftID())
         {
            tip0 = "目标为系统赠送的魂卡";
         }
         if(tip0 != "")
         {
            tip0 += "，是否要作为消耗品？";
         }
         return tip0;
      }
      
      public function getEvoChildError() : String
      {
         if(this.isGiftID())
         {
            if(BcardEvoAgent.noChildArr.indexOf(this.getBodyName()) >= 0)
            {
               return "该魂卡很宝贵，不能作为材料。";
            }
         }
         return "";
      }
      
      public function getEvoChildArr() : Array
      {
         var mustStar0:int = 0;
         var moreNum0:int = 0;
         var arr0:Array = null;
         var da0:BossCardData = null;
         var newArr0:Array = [];
         if(Boolean(this.fatherData))
         {
            mustStar0 = this.getEvoMustStar();
            moreNum0 = getEvoChildNext(mustStar0);
            arr0 = this.fatherData.getDataArr();
            for each(da0 in arr0)
            {
               if(panEvoChildData(this,da0))
               {
                  newArr0.push(da0);
               }
            }
         }
         return newArr0;
      }
      
      public function canRemakeB() : Boolean
      {
         return this.getStar() >= 8;
      }
      
      public function addMaxPro(pro0:String) : void
      {
         if(this.maxProArr == null)
         {
            this.maxProArr = [];
         }
         if(this.maxProArr.indexOf(pro0) == -1)
         {
            this.maxProArr.push(pro0);
         }
      }
      
      public function clearMaxPro() : void
      {
         this.maxProArr = null;
      }
      
      public function getGatherTip(delB0:Boolean = false, haveFirstStrB0:Boolean = true) : String
      {
         var skillName0:String = null;
         var baseSkillArr0:Array = null;
         var fightB0:Boolean = false;
         var fightStr0:String = null;
         var skillD0:SkillDefine = null;
         var skillCn0:String = null;
         var s0:String = "";
         if(Gaming.testCtrl.canCheatingB())
         {
            s0 += "id:" + this.id + "\n";
         }
         if(!this.bodyDef)
         {
            return s0;
         }
         if(haveFirstStrB0)
         {
            fightB0 = this.isFightB();
            fightStr0 = "";
            if(delB0)
            {
               if(fightB0 == false)
               {
                  fightStr0 = "<red ·点击删除·/>\n\n";
               }
            }
            s0 += fightStr0;
         }
         s0 += ProTipType.getTitleMixed(this.bodyDef.cnName,"yellow");
         s0 += "\n生命系数|" + this.cardSave.li;
         s0 += "\n伤害系数|" + this.cardSave.dp;
         var haveObjB0:Boolean = ObjectMethod.getObjElementNum(this.cardSave.o) > 0;
         if(haveObjB0)
         {
            s0 += "\n\n" + ProTipType.getTitleMixed("提升人物");
            s0 += "\n" + EquipPropertyDataCreator.getText_byObj(this.cardSave.o,null,true,this.gatherProLastFun);
         }
         var skillArr0:Array = this.getSkillNameArr();
         if(skillArr0.length > 0)
         {
            if(haveObjB0 == false)
            {
               s0 += "\n";
            }
            s0 += "\n" + ProTipType.getTitleMixed("后天技能");
         }
         for each(skillName0 in skillArr0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(skillName0);
            skillCn0 = skillD0.cnName;
            if(BossCardCreator.isRareSkill(skillName0))
            {
               skillCn0 = "<orange " + skillCn0 + "/>";
            }
            else if(BossCardCreator.isMagicSkill(skillName0))
            {
               skillCn0 = "<redness2 " + skillCn0 + "/>";
            }
            s0 += "\n" + skillCn0;
         }
         baseSkillArr0 = this.bodyDef.getEditSkillArr();
         if(baseSkillArr0.length > 0)
         {
            s0 += "\n\n<gray2 原始技能：/>";
            s0 += "\n<graydark " + StringMethod.concatStringArr(Gaming.defineGroup.skill.getCnArrByNameArr(baseSkillArr0),3) + "/>";
         }
         if(skillArr0.length > 0)
         {
            s0 += "\n\n<b><orange 按下F键查看技能详情/></b>";
         }
         return s0;
      }
      
      private function gatherProLastFun(d0:PropertyArrayDefine, s0:String, v0:*) : String
      {
         if(Boolean(this.maxProArr))
         {
            if(this.maxProArr.indexOf(d0.name) >= 0)
            {
               s0 += " <b><red max/></b>";
            }
         }
         return s0;
      }
      
      public function getFTip() : String
      {
         var s0:String = "";
         var skillArr0:Array = this.getSkillNameArr();
         s0 += ProTipType.getTitleMixed("后天技能");
         return s0 + ("\n\n" + SkillDescrip.getSkillArrGather(skillArr0,GatherColor.blueColor,false,false,true));
      }
      
      public function getGiftCn() : String
      {
         return this.getTipTitle();
      }
      
      public function getGiftTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGiftIconUrl() : String
      {
         return this.getIconUrl();
      }
   }
}

