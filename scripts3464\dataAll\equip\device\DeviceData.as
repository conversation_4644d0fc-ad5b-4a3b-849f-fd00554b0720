package dataAll.equip.device
{
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   
   public class DeviceData extends EquipData
   {
      
      public var deviceDefine:DeviceDefine;
      
      public var useNum:Number = 0;
      
      public function DeviceData()
      {
         super();
      }
      
      public function getDeviceSave() : DeviceSave
      {
         return save as DeviceSave;
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         super.inData_bySave(s0,pd0,dg0);
         this.deviceDefine = this.getDeviceSave().getDeviceDefine();
         save.obj = this.deviceDefine.getAddObj();
      }
      
      override public function clone() : EquipData
      {
         var da0:DeviceData = super.clone() as DeviceData;
         da0.deviceDefine = this.deviceDefine;
         return da0;
      }
      
      override public function startLevel(pg0:IO_PlayerLevelGetter) : void
      {
         this.useNum = 0;
      }
      
      public function isUseUpB() : Boolean
      {
         if(this.deviceDefine.canNum > 0)
         {
            if(this.useNum >= this.deviceDefine.canNum)
            {
               return true;
            }
            return false;
         }
         return false;
      }
      
      override public function isCanNumSwapB() : Boolean
      {
         return true;
      }
      
      override public function isCanOverlayB() : Boolean
      {
         return true;
      }
      
      override public function getNowNum() : int
      {
         return save.nowNum;
      }
      
      override public function setNowNum(num0:int) : void
      {
         save.nowNum = num0;
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         addNowTrueNum(num0,otherDa0);
      }
      
      override public function getSellPrice() : Number
      {
         return save.nowNum * 2000 * save.itemsLevel;
      }
      
      public function getUpradeData() : DeviceData
      {
         var upgradeName0:String = this.deviceDefine.getUpgradeName();
         var s0:DeviceSave = DeviceDataCreator.getSave(upgradeName0,1);
         var da0:DeviceData = new DeviceData();
         da0.inData_bySave(s0,normalPlayerData);
         return da0;
      }
      
      public function getUpradeMust() : MustDefine
      {
         return DeviceDataCreator.getUpgradeMust(this,save.itemsLevel + 1);
      }
      
      public function changeToOneData(da0:DeviceData) : void
      {
         var name0:String = null;
         var proArr0:Array = ["skillArr","imgName","nowNum","itemsLevel","name","cnName","newB","lockB"];
         for each(name0 in proArr0)
         {
            save[name0] = da0.save[name0];
         }
         this.deviceDefine = this.getDeviceSave().getDeviceDefine();
         save.obj = this.deviceDefine.getAddObj();
      }
      
      override public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         var str0:String = this.deviceDefine.getGatherTip();
         return str0 + getSurplusDayGatherTip(tipType0);
      }
   }
}

