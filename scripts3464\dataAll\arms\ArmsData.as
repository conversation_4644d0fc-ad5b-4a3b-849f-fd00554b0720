package dataAll.arms
{
   import UI.base.tip.TextGatherAnalyze;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.parts.PartsAddData;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsRare;
   import dataAll._app.parts.define.PartsType;
   import dataAll._player.PlayerData;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.creator.ArmsPiano;
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.creator.ArmsStrengthenCtrl;
   import dataAll.arms.creator.ArmsUpgradeCtrl;
   import dataAll.arms.define.ArmsColor;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.arms.save.ArmsSave;
   import dataAll.arms.skin.IO_HaveSkinData;
   import dataAll.body.attack.ElementHurt;
   import dataAll.body.attack.ElementHurtDefine;
   import dataAll.body.attack.HurtData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.IO_StrengthenData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.creator.ItemsStrengthenCtrl;
   import dataAll.items.save.ItemsSave;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.things.ThingsData;
   import dataAll.ui.GatherColor;
   import gameAll.arms.GameArmsCtrl;
   
   public class ArmsData extends ArmsDefine implements IO_HaveSkinData, IO_StrengthenData, IO_ItemsData
   {
      
      private static var copyDArr:Array = ["critD3","speedD","followD"];
      
      public var fatherData:ItemsDataGroup = null;
      
      public var placeType:String = "";
      
      public var def:ArmsDefine;
      
      public var save:ArmsSave;
      
      public var partsData:PartsDataGroup = new PartsDataGroup();
      
      private var eleHurtMul:Number = 0;
      
      private var uidpsAddMul:Number = 0;
      
      public var eleHurtAddState:Number = 0;
      
      private var otherEleArr:Array = null;
      
      private var _nowCapacityNum:Number = 0;
      
      public var now_t:Number = 0;
      
      public var playerData:PlayerData;
      
      public var normalPlayerData:NormalPlayerData;
      
      public var tempSortId:String = "";
      
      public var hidingB:Boolean = false;
      
      public var pianoArr:Array = null;
      
      public var pianoIndex:int = 0;
      
      private var _isChosen:Boolean = false;
      
      private var _newB:Boolean = false;
      
      public var shootAngleCtrlB:Boolean = false;
      
      public var moveGap:Number = 0;
      
      private var baseShootRange:Number = -1;
      
      private var baseSpecialNum:Number = -1;
      
      private var baseHurt:Number = -1;
      
      protected var partsAddData:PartsAddData = null;
      
      private var tempSkillArr:Array = null;
      
      public function ArmsData()
      {
         super();
      }
      
      public function set nowCapacityNum(v0:Number) : void
      {
         this._nowCapacityNum = v0 / V;
      }
      
      public function get nowCapacityNum() : Number
      {
         return Math.round(this._nowCapacityNum * V);
      }
      
      public function set isChosen(bb0:Boolean) : void
      {
         this._isChosen = bb0;
      }
      
      public function get isChosen() : Boolean
      {
         return this._isChosen;
      }
      
      public function set newB(bb0:Boolean) : void
      {
         this._newB = bb0;
      }
      
      public function get newB() : Boolean
      {
         return this._newB;
      }
      
      public function initState() : void
      {
         this.eleHurtAddState = 0;
      }
      
      public function setFatherData(dg0:ItemsDataGroup) : void
      {
         this.fatherData = dg0;
      }
      
      public function inData_bySave(s0:ArmsSave, pd0:NormalPlayerData, fleshOriginalB:Boolean = true, dg0:ArmsDataGroup = null) : void
      {
         this.setPlayerData(pd0);
         this.setFatherData(dg0);
         var d0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(s0.name);
         if(!d0)
         {
            INIT.showError("找不到定义ArmsRangeDefine：" + s0.name);
         }
         this.def = d0.def;
         this.save = s0;
         if(Boolean(pd0))
         {
            if(ArmsColor.madbossArmsArr.indexOf(d0.name) >= 0)
            {
               d0 = ArmsColor.dealMadbossArms(this);
               this.def = d0.def;
            }
         }
         var pianoStr0:String = "";
         if(this.def.isPianoB())
         {
            if(!(s0.o is String))
            {
               s0.o = this.def.recordD.piano;
            }
            this.pianoArr = ArmsPiano.strToArr(s0.o as String);
         }
         this.partsData.placeType = ItemsDataGroup.PLACE_WEAR;
         this.partsData.inData_bySaveGroup(this.save.partsSave);
         this.partsData.nowArmsData = this;
         this.partsData.saveGroup.gripMaxNum = PartsType.getWearLen();
         this.partsData.saveGroup.unlockTo(PartsType.getWearUnlockLen() - 1);
         if(d0.name == "barrenAwn")
         {
            if(ArmsSpecialAndSkill.haveSpecialNum(s0) > 3)
            {
               ArmsSpecialAndSkill.gotoFirstSpecial(this);
            }
         }
         if(fleshOriginalB)
         {
            this.fleshOriginalData();
         }
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.normalPlayerData = pd0;
         this.playerData = this.normalPlayerData as PlayerData;
         this.partsData.setAllPlayerData(pd0);
      }
      
      public function getPlayerData() : NormalPlayerData
      {
         return this.normalPlayerData;
      }
      
      public function checkNormalPlayerData(pd0:NormalPlayerData) : void
      {
         if(pd0 != this.normalPlayerData)
         {
            INIT.showErrorMust("父类normalPlayerData不匹配");
         }
         this.partsData.checkNormalPlayerData(pd0);
      }
      
      public function shallowCopy(partB0:Boolean = true) : ArmsData
      {
         var da0:ArmsData = new ArmsData();
         da0.placeType = this.placeType;
         da0.def = this.def;
         da0.save = this.save;
         if(partB0)
         {
            da0.partsData = this.partsData;
         }
         return da0;
      }
      
      public function normalClone() : IO_ItemsData
      {
         return null;
      }
      
      public function getPartsUIIconUrlArr() : Array
      {
         return this.partsData.getPartsUIIconUrlArr();
      }
      
      public function havePartsB() : Boolean
      {
         return this.partsData.dataArr.length > 0;
      }
      
      public function getPartsUIIconLabel() : String
      {
         return this.partsData.getPartsUIIconLabel();
      }
      
      public function haveColorPartsB() : Boolean
      {
         if(Boolean(this.partsAddData))
         {
            if(this.partsAddData.color != "")
            {
               return true;
            }
         }
         return false;
      }
      
      public function fleshOriginalData() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var pro_arr2:Array = null;
         var i:* = undefined;
         var name1:String = null;
         for(n in pro_arr)
         {
            name0 = pro_arr[n];
            if(copyDArr.indexOf(name0) >= 0)
            {
               this[name0] = this.def[name0].copy();
            }
            else
            {
               this[name0] = this.def[name0];
            }
         }
         if(Boolean(this.save))
         {
            pro_arr2 = ArmsSave.pro_arr;
            for(i in pro_arr2)
            {
               name1 = pro_arr2[i];
               if(this.hasOwnProperty(name1))
               {
                  if(name1 == "armsImgLabel")
                  {
                     if(this.save[name1] == "")
                     {
                        continue;
                     }
                  }
                  this[name1] = this.save[name1];
               }
            }
         }
         if(skillArr.length == 0)
         {
            skillArr = this.def.skillArr.concat([]);
         }
      }
      
      public function fillAllData(capacityMul0:Number = 1) : void
      {
         var mul0:Number = 1;
         var diyD0:ModeDiyDefine = Gaming.LG.getModelDiyDefineNull();
         if(Boolean(diyD0))
         {
            mul0 = diyD0.getCapacityMul(this);
         }
         this.nowCapacityNum = Math.ceil(capacity * capacityMul0 * mul0);
         this.pianoIndex = 0;
      }
      
      public function setSaveAllValueFromDef(d0:ArmsDefine = null) : void
      {
         var pro_arr2:Array = null;
         if(d0 == null)
         {
            d0 = this.def;
         }
         if(Boolean(this.save))
         {
            pro_arr2 = ArmsSave.pro_arr;
            ClassProperty.inData_bySaveObj(this.save,d0,ArmsSave.pro_arr);
         }
      }
      
      public function fleshData_byMeEquip() : void
      {
         if(this.normalPlayerData is NormalPlayerData)
         {
            this.fleshData_byEquip(this.normalPlayerData.getHeroMerge());
         }
      }
      
      public function fleshData_byEquip(ea0:EquipPropertyData, fleshOriginalB:Boolean = true, fillB:Boolean = false) : void
      {
         var partsDa0:ThingsData = null;
         this.tempSkillArr = null;
         if(fleshOriginalB)
         {
            this.fleshOriginalData();
         }
         var parts0:PartsAddData = this.partsData.getPartsAddData();
         this.partsAddData = parts0;
         var ea_dpsMul0:Number = ObjectMethod.getNumberEleIfHave(ea0,"dpsMul_" + armsType);
         var ea_dps0:Number = ObjectMethod.getNumberEleIfHave(ea0,"dps_" + armsType);
         var ea_hurtMul0:Number = ObjectMethod.getNumberEleIfHave(ea0,"hurtMul_" + armsType);
         var ea_hurt0:Number = ObjectMethod.getNumberEleIfHave(ea0,"hurt_" + armsType);
         var ea_capacityMul0:Number = ObjectMethod.getNumberEleIfHave(ea0,"capacityMul_" + armsType);
         var ea_capacity0:Number = ObjectMethod.getNumberEleIfHave(ea0,"capacity_" + armsType);
         var ea_reload0:Number = ObjectMethod.getNumberEleIfHave(ea0,"reload_" + armsType);
         this.baseHurt = -1;
         var dps0:Number = this.getDps();
         var dps2:Number = dps0 * (1 + parts0.dpsMul) * (1 + ea0.dpsMul + ea_dpsMul0) + (ea0.dps + ea_dps0 + parts0.dps) * getDpsMul();
         dps2 *= 1 + ea0.vipDef.dpsMul;
         dps2 *= 1 + this.getMoreDpsAdd();
         dps2 *= 1 + this.getDpsWhole(ea0);
         var hurt_add2:Number = 0;
         if(Math.abs(dps0 - dps2) > 1)
         {
            hurt_add2 = ArmsDataCreator.getHurt(this,dps2) - hurtRatio;
         }
         var hurt0:Number = hurtRatio;
         var hurt2:Number = hurt0 * (1 + ea0.hurtMul + ArmsStrengthenCtrl.getHurtMul(this.save.strengthenLv) + ea_hurtMul0) + ArmsType.getHurtAdd(armsType) * (ea0.hurt + ea_hurt0) + hurt_add2;
         hurt2 *= 1 + ea0.getDpsAll() + ea0.hurtAll;
         hurt2 *= ArmsEvoCtrl.getHurtMul(this.save.evoLv,this.def);
         if(parts0.colorHurtMul > 0)
         {
            hurt2 *= 1 + parts0.colorHurtMul;
         }
         if(parts0.redHurtMul > 0)
         {
            if(this.getSaveColor() == EquipColor.RED || this.getSaveColor() == EquipColor.ORANGE)
            {
               hurt2 *= 1 + parts0.redHurtMul;
            }
         }
         if(this.def.isZodiacB())
         {
            hurt2 *= 1 + ea0.zodiacArmsHurtAdd;
         }
         var capacity0:Number = capacity;
         var capacity2:Number = capacity0 * (1 + ea0.capacityMul + ea_capacityMul0 + parts0.capacityMul) + ea0.capacity * ArmsType.getCapacityMul(armsType) + ea_capacity0;
         var beforeAttackGap0:Number = attackGap;
         attackGap = attackGap * (1 + ArmsType.getAttackGapAdd(armsType) * parts0.attackGap) / (1 + ea0.attackGap);
         if(attackGap < 0.05)
         {
            attackGap = 0.05;
         }
         if(beforeAttackGap0 == 0)
         {
            attackDelay = 0;
         }
         else
         {
            attackDelay = attackGap / beforeAttackGap0 * attackDelay;
         }
         var reloadMul0:Number = ArmsType.getReloadGapMul(armsType);
         var reload0:Number = reloadGap;
         var reload2:Number = reload0 / (1 + (ea0.reload + ea_reload0) * reloadMul0) * (1 + parts0.reload * reloadMul0);
         if(reload2 < attackGap * 0.7)
         {
            reload2 = attackGap * 0.7;
         }
         if(hurt2 < 1)
         {
            hurt2 = 1;
         }
         if(parts0.rareHurtMul != 0 || extraMul != 1)
         {
            this.baseHurt = hurt2;
            hurt2 *= 1 + parts0.rareHurtMul;
            hurt2 *= extraMul;
         }
         hurtRatio = hurt2;
         capacity = Math.ceil(capacity2);
         reloadGap = reload2;
         shakeAngle *= 1 + ArmsType.getAngleAdd(armsType) * parts0.shakeAngle;
         shootAngle *= 1 + ArmsType.getAngleAdd(armsType) * parts0.shakeAngle / 2;
         setShootRange((this.getShootRange() + parts0.shootDistance) * (parts0.shootDistanceAddMul + 1));
         this.baseSpecialNum = ArmsSpecialAndSkill.haveSpecialNum(this);
         this.baseShootRange = this.getShootRange();
         if(isRect())
         {
            if(parts0.bulletWidth > 0)
            {
               bulletWidth *= 1 + parts0.bulletWidth;
            }
            if(parts0.bulletSpeed != 0)
            {
               bulletSpeed *= 1 + parts0.bulletSpeed;
               speedD.max *= 1 + parts0.bulletSpeed;
               speedD.min *= 1 + parts0.bulletSpeed;
            }
            if(parts0.bulletLife > 0)
            {
               bulletLife *= 1 + parts0.bulletLife;
            }
            if(parts0.gravity != 0)
            {
               gravity += parts0.gravity;
            }
         }
         twoShootPro += parts0.twoShootPro;
         penetrationNum += parts0.penetrationNum;
         penetrationGap += parts0.penetrationGap;
         if(parts0.noMagneticB)
         {
            noMagneticB = true;
         }
         if(parts0.shootAngleCtrlB)
         {
            this.shootAngleCtrlB = true;
         }
         if(parts0.followValue > 0)
         {
            followD.value += parts0.followValue;
         }
         uiDpsMul *= 1 + parts0.uidpsMul;
         for each(partsDa0 in this.partsData.dataArr)
         {
            PartsRare.specialDeal(partsDa0,this);
         }
         if(this.save.ele != "")
         {
            this.eleHurtMul = this.save.getEleHurtMul() + parts0.eleHurtMul;
            this.otherEleArr = parts0.getOtherEleArr();
         }
         else
         {
            this.eleHurtMul = 0;
            this.otherEleArr = null;
         }
         if(ea0.critPro3 > 0)
         {
            critD3.pro = ea0.critPro3;
         }
         if(fillB)
         {
            this.fillAllData();
         }
      }
      
      private function partsDeal() : void
      {
      }
      
      override public function getShootRange() : int
      {
         if(hitType == LONG_LINE)
         {
            return super.getShootRange();
         }
         if(this.baseShootRange < 0)
         {
            return super.getShootRange();
         }
         return this.baseShootRange;
      }
      
      override public function getAIShootRange(precisionB0:Boolean = true) : int
      {
         var v0:Number = super.getAIShootRange(precisionB0);
         if(v0 <= 1500)
         {
            if(Boolean(this.partsAddData))
            {
               v0 += this.partsAddData.aiShootRange;
            }
         }
         return v0;
      }
      
      private function getMoreDpsAdd() : Number
      {
         var md0:PlayerData = null;
         var pd0:MorePlayerData = this.normalPlayerData as MorePlayerData;
         if(Boolean(pd0))
         {
            md0 = pd0.getMainPlayerData();
            if(Boolean(md0))
            {
               return md0.getHeroMerge().moreDpsMul;
            }
         }
         return 0;
      }
      
      private function getDpsWhole(ea0:EquipPropertyData) : Number
      {
         if(Boolean(this.normalPlayerData))
         {
            return this.normalPlayerData.getDpsWholeAdd();
         }
         return 0;
      }
      
      public function getDps() : Number
      {
         return Math.ceil(ArmsDataCreator.getDps(this,this.baseHurt));
      }
      
      public function getShowDps() : Number
      {
         return Math.ceil(ArmsDataCreator.getDps(this,this.baseHurt) / getDpsMul());
      }
      
      public function getUIShowDps() : Number
      {
         var dps0:Number = this.getShowDps();
         var sp0:Number = this.getBaseSpecialNum();
         var s0:Number = skillArr.length;
         var g0:Number = godSkillArr.length;
         dps0 *= 1 + (sp0 + s0) * 0.15 + g0 * 0.2;
         dps0 *= uiDpsMul;
         dps0 *= ArmsType.getUIDpsMul(armsType,this.getSaveColor(),this.save.evoLv,this.def);
         dps0 *= 1 + this.save.getEleAddDps();
         if(Boolean(this.partsAddData))
         {
            if(this.partsAddData.colorHurtMul > 0)
            {
               dps0 /= 1 + this.partsAddData.colorHurtMul * 0.13;
            }
         }
         return Math.ceil(dps0);
      }
      
      private function getBaseSpecialNum() : int
      {
         if(this.baseSpecialNum == -1)
         {
            return ArmsSpecialAndSkill.haveSpecialNum(this);
         }
         return this.baseSpecialNum;
      }
      
      public function getTestBaseShowDps() : Number
      {
         var v0:Number = ArmsDataCreator.countDps(this.save.hurtRatio,this.save.bulletNum,this.def.shootNum,this.save.attackGap,this.save.reloadGap,this.save.capacity,this.save.shakeAngle,this.save.shootAngle,this.save.bulletWidth,this.def.bulletSpeed,this.def.hitType);
         return Math.ceil(v0 / this.def.getDpsMul());
      }
      
      public function setTestBaseShowDps(v0:Number) : Number
      {
         v0 *= this.def.getDpsMul();
         var hurt0:Number = ArmsDataCreator.countHurt(v0,this.save.bulletNum,this.def.shootNum,this.save.attackGap,this.save.reloadGap,this.save.capacity,this.save.shakeAngle,this.save.shootAngle,this.save.bulletWidth,this.def.bulletSpeed,this.def.hitType);
         this.save.hurtRatio = hurt0;
         return hurt0;
      }
      
      public function getTestBaseDps() : Number
      {
         var v0:Number = ArmsDataCreator.countDps(this.save.hurtRatio,this.save.bulletNum,this.def.shootNum,this.save.attackGap,this.save.reloadGap,this.save.capacity,this.save.shakeAngle,this.save.shootAngle,this.save.bulletWidth,this.def.bulletSpeed,this.def.hitType);
         return Math.ceil(v0);
      }
      
      override public function getAllSkillArr() : Array
      {
         var arr0:Array = null;
         if(this.tempSkillArr == null)
         {
            arr0 = super.getAllSkillArr();
            if(Boolean(this.partsAddData))
            {
               if(Boolean(this.partsAddData.skillArr))
               {
                  arr0 = arr0.concat(this.partsAddData.skillArr);
               }
            }
            ArrayMethod.clearRepeat(arr0);
            this.tempSkillArr = arr0;
         }
         return this.tempSkillArr;
      }
      
      public function getEleHurtMul() : Number
      {
         return this.eleHurtMul;
      }
      
      override public function getHurtData() : HurtData
      {
         var h0:HurtData = super.getHurtData();
         if(this.save.ele != "")
         {
            h0.ele = this.save.ele;
            h0.eleMul = this.eleHurtMul + this.eleHurtAddState;
            h0.eleArr = this.otherEleArr;
         }
         return h0;
      }
      
      public function swapCharger(chargerData0:ArmsChargerData, noReduceB:Boolean = false) : void
      {
         if(noReduceB)
         {
            this.nowCapacityNum = capacity;
            return;
         }
         var maxNum0:int = capacity - this.nowCapacityNum;
         var nowNum0:int = 0;
         if(chargerData0.now > maxNum0)
         {
            nowNum0 = maxNum0;
         }
         else
         {
            nowNum0 = chargerData0.now;
         }
         this.nowCapacityNum += nowNum0;
         chargerData0.now -= nowNum0;
      }
      
      public function addCapacity(v0:int) : void
      {
         var now0:int = this.nowCapacityNum;
         var max0:int = capacity;
         now0 += v0;
         if(now0 > max0)
         {
            now0 = max0;
         }
         this.nowCapacityNum = now0;
      }
      
      public function getDemonCapacityAddMul() : Number
      {
         if(Boolean(this.partsAddData))
         {
            return this.partsAddData.demCapacityMul;
         }
         return 0;
      }
      
      public function getShootSoundUrl() : String
      {
         return this.save.shootSoundUrl;
      }
      
      public function getLaunchLabel() : String
      {
         if(this.nowCapacityNum > 1)
         {
            return ArmsType.getLaunchLabel(armsType);
         }
         return "";
      }
      
      public function getColorCnName() : String
      {
         return this.save.getColorCnName(this.getColor());
      }
      
      public function getColor() : String
      {
         if(Boolean(this.partsAddData))
         {
            if(this.partsAddData.color != "")
            {
               return this.partsAddData.color;
            }
         }
         return this.save.color;
      }
      
      public function getSaveColor() : String
      {
         return this.save.color;
      }
      
      public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "武器";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         if(maxWidth0 < 100 && maxWidth0 > 0)
         {
            if(this.def.iconUrl == "")
            {
               return "IconGather/" + armsType;
            }
            return this.def.iconUrl;
         }
         return this.save.getImgLabel();
      }
      
      override public function getCnName() : String
      {
         return this.save.getCnName();
      }
      
      public function getSellPrice() : Number
      {
         return this.save.getSellPrice("arms");
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function isCanNumSwapB() : Boolean
      {
         return false;
      }
      
      public function isCanOverlayB() : Boolean
      {
         return false;
      }
      
      public function getNowNum() : int
      {
         return 1;
      }
      
      public function setNowNum(num0:int) : void
      {
      }
      
      public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
      }
      
      public function getTypeId() : String
      {
         return "01";
      }
      
      public function getDataType() : String
      {
         return ItemsDataGroup.TYPE_ARMS;
      }
      
      public function getChildTypeId() : String
      {
         return ArmsType.getID_byType(armsType);
      }
      
      public function setPlaceType(type0:String) : void
      {
         this.placeType = type0;
      }
      
      public function getPlaceType() : String
      {
         return this.placeType;
      }
      
      public function getTempSortId() : String
      {
         return this.tempSortId;
      }
      
      public function setTempSortId(str0:String) : void
      {
         this.tempSortId = str0;
      }
      
      public function toOneSortId(str0:String) : void
      {
         this.tempSortId = "";
      }
      
      public function setHidingB(bb0:Boolean) : void
      {
         this.hidingB = bb0;
      }
      
      public function getHidingB() : Boolean
      {
         return this.hidingB;
      }
      
      public function dealBtnListCn(label0:String) : String
      {
         return "";
      }
      
      override public function getGatherTip(cDa0:ArmsDefine = null, compactB0:Boolean = false, showArmsSkillB:Boolean = true, tipType0:String = "no") : String
      {
         var da0:ArmsData = cDa0 as ArmsData;
         var str0:String = "";
         if(tipType0 == "no" && this.getPartsUIIconUrlArr().length > 0)
         {
            str0 += "稀有零件| \n";
         }
         if(!compactB0)
         {
            str0 += "基础等级|<yellow " + this.save.itemsLevel + "级/>\n";
         }
         str0 += this.getTestBaseShowDpsGather(cDa0,compactB0) + "\n";
         str0 += super.getGatherTip(cDa0,compactB0,showArmsSkillB,tipType0);
         this.getCountTaskLv();
         return str0;
      }
      
      private function getTestBaseShowDpsGather(cDa0:ArmsDefine = null, compactB0:Boolean = false) : String
      {
         var dps2:Number = NaN;
         var da0:ArmsData = cDa0 as ArmsData;
         var dpsShowTarget_da0:ArmsData = this;
         var dpsShowTarget_da2:ArmsData = da0;
         var dpsStr0:String = "战斗力";
         if(!compactB0)
         {
            dpsShowTarget_da0 = this.shallowCopy();
            dpsShowTarget_da0.fleshOriginalData();
            if(da0 is ArmsData)
            {
               dpsShowTarget_da2 = da0.shallowCopy();
               dpsShowTarget_da2.fleshOriginalData();
            }
            dpsStr0 = "基础战斗力";
         }
         var dpsC0:String = "";
         var dps0:Number = dpsShowTarget_da0.getTestBaseShowDps();
         if(!compactB0)
         {
            dps0 = this.getTestBaseShowDps();
         }
         if(Boolean(da0))
         {
            dps2 = dpsShowTarget_da2.getTestBaseShowDps();
            if(!compactB0)
            {
               dps2 = da0.getTestBaseShowDps();
            }
            if(dps0 > dps2)
            {
               dpsC0 = "|<i6>";
            }
            else if(dps0 < dps2)
            {
               dpsC0 = "|<i7>";
            }
         }
         return dpsStr0 + "|<yellow " + dps0 + "/>" + dpsC0;
      }
      
      public function getElementGather() : String
      {
         var d0:ElementHurtDefine = null;
         var s0:String = null;
         if(this.save.ele != "")
         {
            d0 = ElementHurt.getDefine(this.save.ele);
            s0 = "\n" + TextGatherAnalyze.color(d0.getHurtCn(),d0.gatherColor);
            return s0 + ("|" + TextGatherAnalyze.color(TextWay.numberToPer(this.eleHurtMul),d0.gatherColor));
         }
         return "";
      }
      
      public function getElementTip() : String
      {
         var d0:ElementHurtDefine = null;
         var s0:String = null;
         if(this.save.ele != "")
         {
            d0 = ElementHurt.getDefine(this.save.ele);
            s0 = ComMethod.color(d0.getHurtCn(),d0.getColor());
            return s0 + (" " + TextWay.numberToPer(this.eleHurtMul));
         }
         return "";
      }
      
      public function haveElementB() : Boolean
      {
         return this.save.ele != "";
      }
      
      public function getEle() : String
      {
         return this.save.ele;
      }
      
      override public function getPartsAddData() : PartsAddData
      {
         return this.partsData.getPartsAddData();
      }
      
      public function canUpgradeB() : Boolean
      {
         return ArmsUpgradeCtrl.canUpgradeB(this);
      }
      
      public function canStrengthenB() : Boolean
      {
         return ArmsStrengthenCtrl.canStrengthenB(this);
      }
      
      public function canStrengthenMoveB() : Boolean
      {
         return ArmsStrengthenCtrl.canStrengthenMoveB(this);
      }
      
      public function getStrengthenLv() : int
      {
         return this.save.strengthenLv;
      }
      
      public function getStrengthenTitle() : String
      {
         var s0:String = ComMethod.color("强化" + this.getStrengthenLv() + "级",GatherColor.yellowColor);
         return s0 + ("\n战力:" + NumberMethod.toBigWan(this.getUIShowDps()));
      }
      
      public function getSMaxLv() : int
      {
         return this.save.sMaxLv;
      }
      
      public function getStrengthenMustMul() : Number
      {
         return 1;
      }
      
      public function strengthenNumAdd() : void
      {
         ++this.save.strengthenNum;
      }
      
      public function isArenaGiftB() : Boolean
      {
         var goodDefine0:GoodsDefine = null;
         var skillLen0:int = int(this.save.skillArr.length);
         var godLen0:int = int(this.save.godSkillArr.length);
         if((skillLen0 == 3 || skillLen0 == 2 && godLen0 == 2) && this.save.itemsLevel >= 55)
         {
            goodDefine0 = Gaming.defineGroup.goods.getDefine(this.save.name);
            if(goodDefine0 is GoodsDefine)
            {
               if(goodDefine0.father == "arena")
               {
                  return true;
               }
            }
            return true;
         }
         return false;
      }
      
      public function getAllUpgradeConverStoneNum() : int
      {
         if(this.isArenaGiftB() || this.save.addLevel <= 0)
         {
            return 0;
         }
         return int(this.save.getAllUpgradeConverStoneNum() / 3);
      }
      
      public function canEchelonB() : Boolean
      {
         return ItemsStrengthenCtrl.canEchelonB(this);
      }
      
      public function canEvoB() : Boolean
      {
         return ArmsEvoCtrl.canEvoB(this.def,this.save.evoLv);
      }
      
      public function getEvoLv() : int
      {
         return this.save.evoLv;
      }
      
      public function canRefiningB() : Boolean
      {
         if(this.save.isOnceGetB())
         {
            return false;
         }
         if(isCanEvoB())
         {
            return false;
         }
         if(this.canResolveB())
         {
            return false;
         }
         if(ArmsColor.canRefiningArr.indexOf(this.def.color) >= 0)
         {
            if(this.save.getTrueLevel() >= 80)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getResolveGift() : GiftAddDefineGroup
      {
         var num0:int = 0;
         var thingsName0:String = null;
         var g0:GiftAddDefineGroup = null;
         var pda0:ThingsData = null;
         if(this.canResolveB())
         {
            num0 = this.def.chipNum;
            thingsName0 = this.def.getChipName();
            g0 = new GiftAddDefineGroup();
            g0.addGiftByStr("things;" + thingsName0 + ";" + num0);
            for each(pda0 in this.partsData.dataArr)
            {
               g0.addGiftByStr("parts;" + pda0.save.name + ";" + pda0.getNowNum());
            }
            return g0;
         }
         return null;
      }
      
      override public function canResolveB() : Boolean
      {
         if(Boolean(this.playerData) && this.placeType == "wear")
         {
            if(this.playerData.arms.dataArr.length <= 1)
            {
               return false;
            }
         }
         if(this.def.canResolveB())
         {
            return this.save.evoLv == 1;
         }
         return false;
      }
      
      override public function getPathPoint2(i0:int) : Array
      {
         if(Boolean(this.save.l))
         {
            return ArrayMethod.getElement(this.save.l,i0,null,null) as Array;
         }
         return null;
      }
      
      public function getSkinDefArr() : Array
      {
         return Gaming.defineGroup.armsCharger.getSkinArr(this.def.name);
      }
      
      public function setSkin(name0:String) : void
      {
         if(name0 != this.save.getBaseSkin())
         {
            this.save.s = name0;
         }
         else
         {
            this.save.s = "";
         }
         GameArmsCtrl.addArmsSaveResoure(this.save);
      }
      
      public function haveMoreSkinB() : Boolean
      {
         return this.getSkinDefArr();
      }
      
      public function getNowSkin() : String
      {
         return this.save.getNowSkin();
      }
      
      public function getBaseSkin() : String
      {
         return this.save.getBaseSkin();
      }
      
      public function sameForm(mda0:ArmsData, partsB0:Boolean, lvB0:Boolean, strenB0:Boolean, eleB0:Boolean) : void
      {
         var parr0:Array = null;
         var p0:ThingsData = null;
         var pda0:ThingsData = null;
         var clv0:int = 0;
         if(partsB0)
         {
            this.partsData.clearData();
            parr0 = mda0.partsData.getSiteDataArray();
            for each(p0 in parr0)
            {
               pda0 = this.partsData.addDataByNameNew(p0.save.getDefine().name);
               pda0.save.site = p0.save.site;
            }
         }
         if(lvB0)
         {
            clv0 = mda0.save.getTrueLevel() - this.save.itemsLevel;
            if(clv0 > 0)
            {
               this.save.addLevel = clv0;
            }
         }
         if(strenB0)
         {
            this.save.strengthenLv = mda0.save.strengthenLv;
         }
         if(eleB0)
         {
            this.save.ele = mda0.save.ele;
            this.save.eleLv = mda0.save.eleLv;
         }
      }
      
      public function getCountTaskLv() : int
      {
         var lv0:int = this.save.getTrueLevel();
         var color0:int = 0;
         var stren0:int = this.save.getStarNum() / 2;
         var evo0:int = (this.save.evoLv - 1) / 2;
         return lv0 + color0 + stren0 + evo0;
      }
      
      public function canEditPathB() : Boolean
      {
         return godSkillArr.indexOf("editBulletPath") >= 0;
      }
      
      public function panCanUseOne(firstB0:Boolean, typeLimitArr0:Array = null, nameLimitArr0:Array = null, chargerG0:ArmsChargerDataGroup = null) : Boolean
      {
         var charger0:int = 0;
         var addB0:Boolean = true;
         if(firstB0 && !this.save.firstChoiceB)
         {
            addB0 = false;
         }
         if(Boolean(typeLimitArr0))
         {
            if(typeLimitArr0.indexOf(armsType) == -1)
            {
               addB0 = false;
            }
         }
         if(Boolean(nameLimitArr0))
         {
            if(nameLimitArr0.indexOf(name) == -1)
            {
               addB0 = false;
            }
         }
         if(Boolean(chargerG0))
         {
            if(this.nowCapacityNum == 0)
            {
               charger0 = chargerG0.getCharger(armsType);
               if(charger0 == 0)
               {
                  addB0 = false;
               }
            }
         }
         return addB0;
      }
      
      public function getNowCapacityAndCharger(chargerG0:ArmsChargerDataGroup = null) : Number
      {
         var charger0:int = chargerG0.getCharger(armsType);
         return charger0 + this.nowCapacityNum;
      }
      
      public function zuobiPan() : String
      {
         return "";
      }
   }
}

