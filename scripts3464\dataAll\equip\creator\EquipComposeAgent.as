package dataAll.equip.creator
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipComposeAgent
   {
      
      public static const MUST_FATHER_ARR:Array = ["aintSuit","thunderSuit"];
      
      private static const COMPOSE_ARR:Array = ["madamor_head","madamor_pants","madamor_coat","madamor_belt"];
      
      public var def:EquipDefine;
      
      private var mustDefArr:Array = [];
      
      public function EquipComposeAgent()
      {
         super();
      }
      
      private static function getMustEvoLv() : int
      {
         return 4;
      }
      
      public static function getMustThingsByType(type0:String) : String
      {
         if(type0 == EquipType.HEAD)
         {
            return "wisdomGem";
         }
         if(type0 == EquipType.PANTS)
         {
            return "agileGem";
         }
         if(type0 == EquipType.COAT)
         {
            return "defenceGem";
         }
         if(type0 == EquipType.BELT)
         {
            return "alertGem";
         }
         return "wisdomGem";
      }
      
      public static function getComposeDefineArr() : Array
      {
         var name0:String = null;
         var d0:EquipDefine = null;
         var arr0:Array = [];
         for each(name0 in COMPOSE_ARR)
         {
            d0 = Gaming.defineGroup.equip.getDefine(name0);
            arr0.push(d0);
         }
         return arr0;
      }
      
      public function inDefine(equipDefine0:EquipDefine) : void
      {
         var father0:String = null;
         var name0:String = null;
         var d0:EquipDefine = null;
         this.def = equipDefine0;
         for each(father0 in MUST_FATHER_ARR)
         {
            name0 = father0 + "_" + this.def.type;
            d0 = Gaming.defineGroup.equip.getDefine(name0);
            this.mustDefArr.push(d0);
         }
      }
      
      public function getMustDefArr() : Array
      {
         return this.mustDefArr;
      }
      
      public function getIconText(index0:int, da0:EquipData) : String
      {
         var d0:EquipDefine = null;
         var evoLv0:int = 0;
         var cn0:String = null;
         if(index0 >= 0 && index0 <= this.mustDefArr.length - 1)
         {
            d0 = this.mustDefArr[index0];
            evoLv0 = getMustEvoLv();
            cn0 = EquipEvoCtrl.getCnName(d0,evoLv0);
            if(Boolean(da0))
            {
               cn0 = da0.getCnName();
               return "\n" + TextMethod.color(cn0,EquipColor.blackHtmlColor);
            }
            return "请拖入装备\n" + TextMethod.color(cn0,EquipColor.blackHtmlColor);
         }
         return "";
      }
      
      public function getComposeIconText(da0:EquipData) : String
      {
         var cn0:String = this.def.cnName;
         if(Boolean(da0))
         {
            cn0 = da0.getCnName();
         }
         var colorCn0:String = TextMethod.color(cn0,EquipColor.darkgoldHtmlColor);
         return "合成暗金装备\n" + colorCn0;
      }
      
      public function panMustDataStr(da0:EquipData, index0:int) : String
      {
         var mustEvoLv0:int = 0;
         var mustDef0:EquipDefine = this.mustDefArr[index0];
         if(Boolean(mustDef0))
         {
            if(da0.save.getDefine() == mustDef0)
            {
               mustEvoLv0 = getMustEvoLv();
               if(da0.save.evoLv >= mustEvoLv0)
               {
                  return "";
               }
               return "当前装备进化等级不够。";
            }
            return "该装备不是" + TextMethod.color(mustDef0.cnName,EquipColor.blackHtmlColor) + "。";
         }
         return "未找到所需装备数据。";
      }
      
      public function getMust() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var thingsName0:String = getMustThingsByType(this.def.type);
         d0.lv = this.def.itemsLevel;
         d0.coin = 200000;
         d0.inThingsDataByArr([thingsName0 + ";300","armsTitanium;150","armsRadium;150"]);
         return d0;
      }
      
      public function getComposeData(daArr0:Array) : EquipData
      {
         var s0:EquipSave = this.getComposeSave(daArr0);
         var da0:EquipData = new EquipData();
         da0.inData_bySave(s0,Gaming.PG.da,null);
         return da0;
      }
      
      public function getComposeSave(daArr0:Array) : EquipSave
      {
         var s0:EquipSave = null;
         var n:* = undefined;
         var strenLv0:int = 0;
         var evoLv0:int = 0;
         var valueList0:Array = null;
         var da0:EquipData = null;
         var baseObj0:Object = null;
         var v0:Number = NaN;
         var lastValue0:Number = NaN;
         if(daArr0.length < this.mustDefArr.length)
         {
            return null;
         }
         s0 = Gaming.defineGroup.equipCreator.getDarkgoldSave(this.def.name);
         var otherObj0:Object = this.def.getOtherObj();
         var proArrObj0:Object = {};
         var saveObj0:Object = s0.obj;
         for(n in otherObj0)
         {
            valueList0 = [];
            proArrObj0[n] = valueList0;
            for each(da0 in daArr0)
            {
               baseObj0 = da0.save.obj;
               if(baseObj0.hasOwnProperty(n))
               {
                  v0 = Number(baseObj0[n]);
                  if(n == "dpsAllBlack")
                  {
                     v0 = v0 * 2 + 0.05;
                  }
                  valueList0.push(v0);
               }
            }
            if(valueList0.length > 0)
            {
               ArrayMethod.sortNumberArray(valueList0);
               lastValue0 = Number(valueList0[valueList0.length - 1]);
               if(valueList0.length == 1)
               {
                  lastValue0 *= 0.7;
               }
               saveObj0[n] = lastValue0;
            }
         }
         s0.obj = saveObj0;
         var chooseDa0:EquipData = this.getMaxStrenLvInArr(daArr0);
         s0.skillArr = chooseDa0.save.skillArr.concat();
         s0.heroSkillAddObj = chooseDa0.save.heroSkillAddObj;
         strenLv0 = this.getStrenLvAvg(daArr0);
         s0.setStrengthenLvAndMax(strenLv0);
         evoLv0 = this.getEvoLvSum(daArr0) - 7;
         s0.evoLv = evoLv0;
         return s0;
      }
      
      private function getEvoLvSum(daArr0:Array) : Number
      {
         var e1:EquipData = daArr0[0];
         var e2:EquipData = daArr0[1];
         return e1.save.evoLv + e2.save.evoLv;
      }
      
      private function getStrenLvAvg(daArr0:Array) : int
      {
         var da0:EquipData = null;
         var avgExp0:Number = NaN;
         var findLv0:int = 0;
         var lv0:int = 0;
         var numExp0:Number = NaN;
         if(daArr0.length == 0)
         {
            return 0;
         }
         var sumExp0:Number = 0;
         var maxLv0:int = 0;
         var pd0:PropertyArrayDefine = Gaming.defineGroup.equip.strengthen.getDefine("oneNum");
         for each(da0 in daArr0)
         {
            lv0 = da0.getStrengthenLv();
            numExp0 = 0;
            if(lv0 > 0)
            {
               numExp0 = pd0.getSum(lv0);
            }
            sumExp0 += numExp0;
            if(lv0 > maxLv0)
            {
               maxLv0 = lv0;
            }
         }
         avgExp0 = Math.round(sumExp0 / daArr0.length);
         findLv0 = pd0.findLvBySum(avgExp0);
         if(findLv0 > maxLv0)
         {
            findLv0 = maxLv0;
         }
         return findLv0;
      }
      
      private function getMaxStrenLvInArr(daArr0:Array) : EquipData
      {
         var arr0:Array = daArr0.concat();
         arr0.sort(this.sortMaxStrenFun);
         return arr0[0];
      }
      
      private function sortMaxStrenFun(a:EquipData, b:EquipData) : int
      {
         var alv0:int = a.save.getStrengthenLv();
         var blv0:int = b.save.getStrengthenLv();
         return ArrayMethod.sortNumberFun(blv0,alv0);
      }
   }
}

