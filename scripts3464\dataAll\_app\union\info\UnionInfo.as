package dataAll._app.union.info
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.union.extra.MemberExtra;
   import dataAll._app.union.extra.UnionExtra;
   import dataAll._app.union.extra.UnionExtraPer;
   import dataAll._player.PlayerData;
   import dataAll.test.ZuoBiMax;
   import w_test.drop.LevelDropCount;
   
   public class UnionInfo
   {
      
      public static var pro_arr:Array = [];
      
      public static var LIST_ARR:Array = ["rank","title","getKingName","level","getCountString","getDpsString","getBattleScoreString","experience"];
      
      public static var LIST_CN:Array = ["序号","名称","拥有者","等级","人数","总战斗力","争霸积分","贡献值"];
      
      public var rank:int = 1;
      
      public var id:int = 0;
      
      public var unionId:int = 0;
      
      public var title:String = "";
      
      public var username:String = "";
      
      public var level:int = 0;
      
      public var count:int = 0;
      
      public var extra:String = "";
      
      public var experience:Number = 0;
      
      public var userName:String = "";
      
      public var nickName:String = "";
      
      public var uId:Number = 0;
      
      public var index:int = 0;
      
      public var extra2:String = "";
      
      public var dissolveDate:String = "";
      
      public var transfer:String = "";
      
      public var extraObj:UnionExtra = new UnionExtra();
      
      public var extraPerObj:UnionExtraPer = new UnionExtraPer();
      
      public function UnionInfo()
      {
         super();
      }
      
      public static function getSimulated(index0:int) : UnionInfo
      {
         var i0:UnionInfo = new UnionInfo();
         i0.rank = index0 + 1;
         i0.id = index0 + 1;
         i0.title = "沃龙队" + i0.unionId;
         i0.username = "sounto" + String(index0 + 1);
         i0.level = int(Math.random() * 50) + 40;
         i0.count = 30;
         var eobj0:UnionExtra = new UnionExtra();
         eobj0.setUrl("https://my.4399.com/forums/thread-58103202");
         i0.extra = MemberExtra.toExtra(eobj0);
         i0.experience = int(Math.random() * 400 + 10);
         return i0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(this.id == 0)
         {
            this.id = this.unionId;
         }
         if(this.unionId == 0)
         {
            this.unionId = this.id;
         }
         if(this.extra.indexOf("{") == -1 && this.extra != "")
         {
            this.extraObj.inData_byObj(MemberExtra.toObj(this.extra));
         }
         if(this.extra2.indexOf("{") == -1 && this.extra2 != "")
         {
            this.extraPerObj.inData_byObj(MemberExtra.toObj(this.extra2));
         }
      }
      
      public function refreshToExtra() : void
      {
         this.extra = MemberExtra.toExtra(this.extraObj);
         this.extra2 = MemberExtra.toExtra(this.extraPerObj);
      }
      
      public function getStringArr() : Array
      {
         var name0:String = null;
         var str0:String = null;
         var arr0:Array = [];
         for each(name0 in LIST_ARR)
         {
            str0 = String(this.getProByUrl(name0));
            arr0.push(str0);
         }
         return arr0;
      }
      
      private function getProByUrl(url0:String) : *
      {
         var f0:String = null;
         var n0:String = null;
         var index0:int = int(url0.indexOf("."));
         if(index0 == -1)
         {
            f0 = url0;
            if(this[f0] is Function)
            {
               return this[f0]();
            }
            return this[f0];
         }
         f0 = url0.substring(0,index0);
         n0 = url0.substring(index0 + 1);
         return this[f0][n0];
      }
      
      public function inNoticeString(str0:String) : void
      {
         this.extraObj.setNotice(str0);
         this.refreshToExtra();
      }
      
      public function inUrlString(str0:String) : void
      {
         this.extraObj.setUrl(str0);
         this.refreshToExtra();
      }
      
      public function getKingName() : String
      {
         if(this.extraObj.kingName == "")
         {
            return this.username;
         }
         return this.extraObj.kingName;
      }
      
      public function getExperienceString() : String
      {
         var max0:Number = Gaming.defineGroup.union.dataPro.getPropertyValue("exp",this.level + 1);
         return this.experience + ComMethod.color("/" + max0);
      }
      
      public function getCountString() : String
      {
         var max0:Number = Gaming.defineGroup.union.dataPro.getPropertyValue("count",this.level);
         if(this.count >= max0)
         {
            return ComMethod.color(this.count + "/" + max0,"#FF9900");
         }
         return this.count + ComMethod.color("/" + max0);
      }
      
      public function getDpsString() : String
      {
         return ComMethod.numberToSmall(this.extraObj.dps,"#00FFFF");
      }
      
      public function getLifeString() : String
      {
         return ComMethod.numberToSmall(this.extraObj.life,"#00FFFF");
      }
      
      public function getBattleScoreString() : String
      {
         return this.extraObj.bs + "";
      }
      
      public function isFillB() : Boolean
      {
         var max0:Number = Gaming.defineGroup.union.dataPro.getPropertyValue("count",this.level);
         return this.count >= max0;
      }
      
      public function getDpsMulString() : String
      {
         var max0:Number = this.getDpsMul();
         return TextWay.numberToPer(max0);
      }
      
      public function getDpsMul() : Number
      {
         return Gaming.defineGroup.union.dataPro.getPropertyValue("unionDpsMul",this.level);
      }
      
      public function getApplyTip(pd0:PlayerData) : Object
      {
         var tip0:String = "要加入军队" + ComMethod.color(this.title,"#FF6600");
         var canB0:Boolean = false;
         var conditionStr0:String = "";
         var dpsB0:Boolean = pd0.getDps() >= this.extraObj.dpsLimit;
         if(this.extraObj.dpsLimit > 0)
         {
            conditionStr0 += "\n战斗力到达：" + this.extraObj.dpsLimit + this.getApplyConditionStr(dpsB0);
         }
         var vipB0:Boolean = pd0.vip.def.getTrueLevel() >= this.extraObj.vipLimit;
         if(this.extraObj.vipLimit > 0)
         {
            conditionStr0 += "\nVIP等级到达：" + this.extraObj.vipLimit + this.getApplyConditionStr(vipB0);
         }
         if(conditionStr0 != "")
         {
            tip0 += "，你需要符合以下条件：" + conditionStr0;
            canB0 = dpsB0 && vipB0;
         }
         else
         {
            tip0 += "吗？";
            canB0 = true;
         }
         if(this.extraObj.getUrl() != "")
         {
            tip0 += "\n" + ComMethod.color("申请之前最好查看该军队的官帖，以了解详细申请规则。","#00FFFF");
         }
         var obj0:Object = {};
         obj0.tip = tip0;
         obj0.bb = canB0;
         return obj0;
      }
      
      public function getApplyConditionTip() : String
      {
         var tip0:String = "战斗力到达：" + this.extraObj.dpsLimit;
         return tip0 + ("\nVIP等级到达：" + this.extraObj.vipLimit);
      }
      
      public function panApplyConditionInfo(info0:MemberInfo) : Boolean
      {
         var e0:MemberExtra = info0.extraObj;
         return this.panApplyCondition(e0.dps,e0.getVipNumber());
      }
      
      private function panApplyCondition(dps0:Number, vip0:int) : Boolean
      {
         if(dps0 < this.extraObj.dpsLimit)
         {
            return false;
         }
         if(vip0 < this.extraObj.vipLimit)
         {
            return false;
         }
         return true;
      }
      
      private function getApplyConditionStr(bb0:Boolean) : String
      {
         if(bb0)
         {
            return " " + ComMethod.color("√","#00FF00");
         }
         return " " + ComMethod.color("×","#FF0000");
      }
      
      public function getTopUnionExtra() : String
      {
         return (LevelDropCount.testB ? "作弊了 " : "") + "id" + this.id + "，" + this.title + "，lv" + this.level + "，人数" + this.count;
      }
      
      public function zuobiPan() : String
      {
         if(this.count > 0)
         {
            if(this.extraObj.dps > this.count * ZuoBiMax.dps)
            {
               return "军队总战斗力过高，存在作弊成员。";
            }
            if(this.extraObj.life > 40000000000)
            {
               return "军队总生命值过高，存在作弊成员。";
            }
         }
         return "";
      }
   }
}

