package dataAll.scene
{
   import com.sounto.hit.spiderWeb.SpiderWeb;
   import com.sounto.hit.spiderWeb.SpiderWebNode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._data.ConstantDefine;
   import flash.geom.Rectangle;
   
   public class SceneDefineGroup
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function SceneDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var father0:String = null;
         var sceneXML0:* = undefined;
         var n:* = undefined;
         var d0:SceneDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            father0 = fatherXML0[i].@name;
            sceneXML0 = fatherXML0[i].scene;
            for(n in sceneXML0)
            {
               d0 = new SceneDefine();
               d0.inData_byXML(sceneXML0[n],father0,ConstantDefine.WIDTH,ConstantDefine.HEIGHT);
               this.arr.push(d0);
               this.obj[d0.name] = d0;
            }
         }
      }
      
      public function getDefine(name0:String) : SceneDefine
      {
         return this.obj[name0];
      }
      
      public function testMaxSniper() : void
      {
         var d0:SceneDefine = null;
         var num0:int = 0;
         var spiderWeb0:SpiderWeb = new SpiderWeb();
         var max0:Number = 0;
         var maxD0:SceneDefine = null;
         for each(d0 in this.arr)
         {
            spiderWeb0.clear();
            spiderWeb0.inData_byStr(d0.spiderData);
            num0 = int(spiderWeb0.arr.length);
            if(num0 > max0)
            {
               max0 = num0;
               maxD0 = d0;
            }
         }
      }
      
      public function findeSniperOut() : void
      {
         var d0:SceneDefine = null;
         var r0:Rectangle = null;
         var outArr0:Array = null;
         var nd0:SpiderWebNode = null;
         var spiderWeb0:SpiderWeb = new SpiderWeb();
         for each(d0 in this.arr)
         {
            r0 = d0.viewRangeRect.clone();
            r0.x += 30;
            r0.width -= 60;
            r0.y += 100;
            r0.height -= 150;
            spiderWeb0.clear();
            spiderWeb0.inData_byStr(d0.spiderData);
            outArr0 = [];
            for each(nd0 in spiderWeb0.obj)
            {
               if(!r0.contains(nd0.x,nd0.y))
               {
                  outArr0.push(int(nd0.x) + ":" + int(nd0.y));
               }
            }
            if(outArr0.length > 0)
            {
               trace(d0.cnName + "   " + outArr0);
            }
         }
      }
      
      public function testAllMap() : void
      {
         var d0:SceneDefine = null;
         var map0:WorldMapDefine = null;
         for each(d0 in this.arr)
         {
            map0 = Gaming.defineGroup.worldMap.getDefine(d0.name);
            if(!map0)
            {
               INIT.TRACE("SceneDefine找不到WorldMapDefine：" + d0.name);
            }
         }
      }
      
      public function findMaxWidth() : void
      {
         var d0:SceneDefine = null;
         var xx0:int = 0;
         var max0:int = 0;
         var maxD0:SceneDefine = null;
         for each(d0 in this.arr)
         {
            if(d0.sceneWidth > max0)
            {
               max0 = d0.sceneWidth;
               maxD0 = d0;
               INIT.tempTrace(d0.cnName + "  " + d0.sceneWidth + "   " + d0.sceneHeight);
            }
         }
         xx0 = 0;
      }
   }
}

