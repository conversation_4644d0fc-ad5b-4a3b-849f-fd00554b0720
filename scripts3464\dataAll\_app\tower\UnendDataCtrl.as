package dataAll._app.tower
{
   import dataAll._app.peak.PeakProDefineGroup;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.unit.UnitType;
   import dataAll.pro.dataList.DataListDefine;
   
   public class UnendDataCtrl
   {
      
      private static var enemyListNameArr:Array = ["unendEnemy","unendEnemyFar","unendEnemySuper","unendBoss","unendWilder"];
      
      private var GIFT1:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private var GIFT2:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      private var lvObj:Object = {};
      
      private var enemyListObj:Object = {};
      
      private var enemyObj:Object = {};
      
      public var proG:PeakProDefineGroup = new PeakProDefineGroup();
      
      public function UnendDataCtrl()
      {
         super();
      }
      
      public function afterDeal() : void
      {
         this.GIFT1.addGiftByCnStr("十年币*10");
         this.GIFT1.addGiftByCnStr("无双水晶*6");
         this.GIFT1.addGiftByCnStr("万能球*8");
         this.GIFT2.addGiftByCnStr("十年币*10");
         this.GIFT2.addGiftByCnStr("零件券*8");
         this.inEnemyList();
      }
      
      public function getBodyLife(cn0:String) : Number
      {
         return this.getEnemyObjValue(cn0,"life");
      }
      
      public function getBodyDps(cn0:String) : Number
      {
         return this.getEnemyObjValue(cn0,"dps");
      }
      
      private function getEnemyObjValue(cn0:String, pro0:String) : Number
      {
         var obj0:Object = null;
         if(this.enemyObj.hasOwnProperty(cn0))
         {
            obj0 = this.enemyObj[cn0];
            if(obj0.hasOwnProperty(pro0))
            {
               return obj0[pro0];
            }
         }
         return 1;
      }
      
      private function inEnemyList() : void
      {
         var name0:String = null;
         var d0:DataListDefine = null;
         var dataArr0:Array = null;
         var newArr0:Array = null;
         var str0:String = null;
         var strArr0:Array = null;
         var cn0:String = null;
         var life0:Number = NaN;
         var dps0:Number = NaN;
         var oneObj0:Object = null;
         var nameArr0:Array = enemyListNameArr;
         for each(name0 in nameArr0)
         {
            d0 = Gaming.defineGroup.dataList.getDefine(name0);
            dataArr0 = d0.getTrueValueArr();
            newArr0 = [];
            for each(str0 in dataArr0)
            {
               strArr0 = str0.split(":");
               cn0 = strArr0[0];
               life0 = 1;
               dps0 = 1;
               if(strArr0.length >= 2)
               {
                  life0 = Number(strArr0[1]);
               }
               if(strArr0.length >= 3)
               {
                  dps0 = Number(strArr0[2]);
               }
               oneObj0 = {};
               oneObj0.life = life0;
               oneObj0.dps = dps0;
               this.enemyObj[cn0] = oneObj0;
               newArr0.push(cn0);
            }
            this.enemyListObj[name0] = newArr0;
         }
      }
      
      private function getListEnemy() : Array
      {
         return this.enemyListObj["unendEnemy"];
      }
      
      private function getListEnemyFar() : Array
      {
         return this.enemyListObj["unendEnemyFar"];
      }
      
      private function getListEnemySuper() : Array
      {
         return this.enemyListObj["unendEnemySuper"];
      }
      
      private function getListEnemyAll() : Array
      {
         return this.getListEnemy().concat(this.getListEnemyFar()).concat(this.getListEnemySuper());
      }
      
      private function getListBoss() : Array
      {
         return this.enemyListObj["unendBoss"];
      }
      
      private function getListWilder() : Array
      {
         return this.enemyListObj["unendWilder"];
      }
      
      public function getAgent(lv0:int) : UnendAgent
      {
         if(this.lvObj.hasOwnProperty(lv0) == false)
         {
            this.lvObj[lv0] = this.createAgent(lv0);
         }
         return this.lvObj[lv0];
      }
      
      private function createAgent(lv0:int) : UnendAgent
      {
         var a0:UnendAgent = new UnendAgent();
         a0.lv = lv0;
         a0.map = this.getMap(lv0);
         a0.bossCnArr = this.getBossCnArr3(lv0);
         a0.bossLife = UnendAgent.GET_LIFE(lv0,UnitType.BOSS);
         a0.bossDps = UnendAgent.GET_DPS(lv0,UnitType.BOSS);
         a0.enemyCnArr = this.getEnemyCnArr3(lv0);
         a0.enemyLife = UnendAgent.GET_LIFE(lv0,UnitType.NORMAL);
         a0.enemyDps = UnendAgent.GET_DPS(lv0,UnitType.NORMAL);
         if(lv0 <= 50)
         {
            a0.gift = this.GIFT1;
         }
         else
         {
            a0.gift = this.GIFT1;
         }
         return a0;
      }
      
      private function getEnemyCnArr3(lv0:int) : Array
      {
         var index0:int = lv0 - 1;
         var arr1:Array = this.getListEnemy();
         var arr2:Array = this.getListEnemyFar();
         var arr3:Array = this.getListEnemySuper();
         var cn1:String = arr1[index0 % arr1.length];
         var cn2:String = arr2[index0 % arr2.length];
         var cn3:String = arr3[index0 % arr3.length];
         return [cn1,cn2,cn3];
      }
      
      private function getBossCnArr3(lv0:int) : Array
      {
         var index0:int = lv0 - 1;
         var arr1:Array = this.getListEnemyAll();
         var arr2:Array = this.getListBoss();
         var arr3:Array = this.getListWilder();
         var cn1:String = arr1[(index0 + int(arr1.length / 2)) % arr1.length];
         var cn2:String = arr2[index0 % arr2.length];
         var cn3:String = arr3[index0 % arr3.length];
         return [cn1,cn2,cn3];
      }
      
      private function getMap(lv0:int) : String
      {
         var d0:DataListDefine = Gaming.defineGroup.dataList.getDefine("unendMap");
         var arr0:Array = d0.getTrueValueArr();
         var index0:int = int(lv0 / 5) % arr0.length;
         return arr0[index0];
      }
   }
}

