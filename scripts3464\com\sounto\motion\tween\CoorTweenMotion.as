package com.sounto.motion.tween
{
   public class CoorTweenMotion
   {
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var mx:Number = 0;
      
      public var my:Number = 0;
      
      public var max:Number = 30;
      
      public var maxPer:Number = 1;
      
      public var tweenPer:Number = 1;
      
      public var initB:Boolean = false;
      
      public function CoorTweenMotion()
      {
         super();
      }
      
      public function init(x0:Number, y0:Number) : void
      {
         this.x = x0;
         this.y = y0;
         this.mx = this.x;
         this.my = this.y;
         this.initB = true;
      }
      
      public function inData(mx0:Number, my0:Number) : void
      {
         var max0:Number = NaN;
         var cx:Number = NaN;
         var cy:Number = NaN;
         this.mx = mx0;
         this.my = my0;
         var clong:Number = Math.sqrt((this.mx - this.x) * (this.mx - this.x) + (this.my - this.y) * (this.my - this.y));
         var v0:Number = clong * this.tweenPer / 5;
         if(v0 < 0.1 && v0 > -0.1)
         {
            v0 = 0;
         }
         else
         {
            max0 = this.max * this.maxPer;
            if(v0 > max0)
            {
               v0 = max0;
            }
            if(v0 < -max0)
            {
               v0 = -max0;
            }
            cx = v0 / clong * (this.mx - this.x);
            cy = v0 / clong * (this.my - this.y);
            this.x += cx;
            this.y += cy;
         }
      }
      
      public function toEndBreak() : void
      {
         this.x = this.mx;
         this.y = this.my;
      }
   }
}

