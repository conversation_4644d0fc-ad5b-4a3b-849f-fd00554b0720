package dataAll.equip.add
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.define.PlayerDataName;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.pro.PropertyArrayDefine;
   
   public class EquipAddAgentGroup
   {
      
      private var pd:NormalPlayerData;
      
      private var arr:Array = [];
      
      private var obj:Object = {};
      
      public function EquipAddAgentGroup()
      {
         super();
      }
      
      private static function getAddObjObj(dataNameArr0:Array, pd0:NormalPlayerData) : Object
      {
         var dataName0:String = null;
         var getter0:IO_EquipAddGetter = null;
         var obj0:Object = {};
         for each(dataName0 in dataNameArr0)
         {
            getter0 = pd0.getEquipAddGetter(dataName0);
            if(Boolean(getter0))
            {
               obj0[dataName0] = getter0.getProAddObj();
            }
         }
         return obj0;
      }
      
      private static function getChilTipObjObj(dataNameArr0:Array, pd0:NormalPlayerData) : Object
      {
         var dataName0:String = null;
         var getter0:IO_EquipAddTipGetter = null;
         var obj0:Object = {};
         for each(dataName0 in dataNameArr0)
         {
            getter0 = pd0.getEquipAddGetter(dataName0) as IO_EquipAddTipGetter;
            if(Boolean(getter0))
            {
               obj0[dataName0] = getter0.getProAddTipObj();
            }
         }
         return obj0;
      }
      
      public function inData(pd0:NormalPlayerData, proArr0:Array) : void
      {
         var pro0:String = null;
         var proD0:PropertyArrayDefine = null;
         var a0:EquipAddAgent = null;
         this.pd = pd0;
         var dataNameArr0:Array = pd0.getEquipAddDataNameArr();
         if(pd0 is PlayerData)
         {
            dataNameArr0 = dataNameArr0.concat(PlayerDataName.show_proAddArr);
         }
         var addOO:Object = getAddObjObj(dataNameArr0,pd0);
         var tipOO:Object = getChilTipObjObj(dataNameArr0,pd0);
         for each(pro0 in proArr0)
         {
            proD0 = Gaming.defineGroup.getPropertyArrayDefine(pro0);
            a0 = new EquipAddAgent();
            a0.inData(proD0,dataNameArr0,addOO,tipOO,pd0);
            if(a0.canShowB(pd0))
            {
               this.arr.push(a0);
               this.obj[pro0] = a0;
            }
         }
      }
      
      public function getAgent(pro0:String) : EquipAddAgent
      {
         return this.obj[pro0];
      }
      
      public function getChild(pro0:String, dataName0:String) : EquipAddChild
      {
         var a0:EquipAddAgent = this.getAgent(pro0);
         if(Boolean(a0))
         {
            return a0.getChild(dataName0);
         }
         return null;
      }
      
      public function getChildByLink(link0:String) : EquipAddChild
      {
         var index0:int = int(link0.indexOf(":"));
         var pro0:String = link0.substring(0,index0);
         var dataName0:String = link0.substring(index0 + 1);
         return this.getChild(pro0,dataName0);
      }
      
      public function getPD() : NormalPlayerData
      {
         return this.pd;
      }
      
      public function isCpRoleB() : Boolean
      {
         var mainPd0:PlayerData = Gaming.PG.da;
         if(mainPd0 != this.pd)
         {
            return mainPd0.isCpB(this.pd);
         }
         return false;
      }
      
      public function getTextObj(dropB0:Boolean) : Object
      {
         var a0:EquipAddAgent = null;
         var titleArr0:Array = [];
         var valueArr0:Array = [];
         for each(a0 in this.arr)
         {
            titleArr0.push(a0.getTitleText());
            valueArr0.push(a0.getText());
         }
         if(dropB0)
         {
            if(this.isCpRoleB())
            {
               titleArr0.unshift("");
               valueArr0.unshift(ComMethod.purple("在" + Gaming.PG.da.getSayCn() + "P1下，该队友的掉率不衰减。"));
            }
         }
         var obj0:Object = {};
         obj0.title = titleArr0;
         obj0.value = valueArr0;
         return obj0;
      }
   }
}

