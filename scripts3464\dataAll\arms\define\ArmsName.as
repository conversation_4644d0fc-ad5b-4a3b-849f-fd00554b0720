package dataAll.arms.define
{
   public class ArmsName
   {
      
      public static const yearTiger:String = "yearTiger";
      
      public static const yearSheep:String = "yearSheep";
      
      public static const lightCone:String = "lightCone";
      
      public static const yearDog:String = "yearDog";
      
      public static const beadCrossbow:String = "beadCrossbow";
      
      public static const christmasGun:String = "christmasGun";
      
      public static const bangerGun:String = "bangerGun";
      
      public static const bangerGunSkill:String = "bangerGunSkill";
      
      public static const rocketCate:String = "rocketCate";
      
      public static const scorpionCrossbow:String = "scorpionCrossbow";
      
      public static const noPenNumDecayArr:Array = [bangerGunSkill,"yearChicken","yearTiger","sniperCicadaYa","Daybreak_sub","consVirgo"];
      
      public static const consLeo:String = "consLeo";
      
      public static const ultiArr:Array = [consLeo];
      
      public function ArmsName()
      {
         super();
      }
   }
}

