package com.common.net
{
   import com.common.image.JPGEncoder;
   import com.common.image.PNGEncoder;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import flash.net.FileReference;
   import flash.utils.ByteArray;
   
   public class SaveImage
   {
      
      public function SaveImage()
      {
         super();
      }
      
      public static function SaveByMC(sp:DisplayObject, rect:Rectangle, str:String = "未命名", pngB:Boolean = false) : *
      {
         var ba:ByteArray = null;
         var encoder:* = undefined;
         var bmp1:BitmapData = new BitmapData(rect.width,rect.height,true,0);
         var mat0:Matrix = new Matrix();
         mat0.tx -= rect.x;
         mat0.ty -= rect.y;
         bmp1.draw(sp,mat0,null,null,null);
         var fileRef:FileReference = new FileReference();
         var backname0:String = ".jpg";
         if(pngB)
         {
            backname0 = ".png";
            ba = PNGEncoder.encode(bmp1);
         }
         else
         {
            encoder = new JPGEncoder(80);
            ba = encoder.encode(bmp1);
         }
         fileRef.save(ba,str + backname0);
         ba.clear();
      }
      
      public static function SaveBmp(bmp0:BitmapData, str:String = "未命名", pngB:Boolean = false) : *
      {
         var ba:ByteArray = null;
         var encoder:* = undefined;
         var fileRef:FileReference = new FileReference();
         var backname0:String = ".jpg";
         if(pngB)
         {
            backname0 = ".png";
            ba = PNGEncoder.encode(bmp0);
         }
         else
         {
            encoder = new JPGEncoder(100);
            ba = encoder.encode(bmp0);
         }
         fileRef.save(ba,str + backname0);
         ba.clear();
      }
   }
}

