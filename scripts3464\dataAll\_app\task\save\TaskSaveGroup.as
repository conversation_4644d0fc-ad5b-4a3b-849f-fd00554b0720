package dataAll._app.task.save
{
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.TextMethod;
   import dataAll._app.task.define.TaskDefine;
   
   public class TaskSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var dataArr:Array = [];
      
      public var onlyCompleteB:Boolean = false;
      
      public var todayCompleteNumObj:Object = {};
      
      public var todayBuyNumObj:Object = {};
      
      private var _weekDropDateStr:String = "";
      
      public function TaskSaveGroup()
      {
         super();
         this.weekMulIndex = 0;
         this.weekDropDateStr = "";
      }
      
      private static function getSwapMax() : int
      {
         return 5;
      }
      
      public function get weekMulIndex() : Number
      {
         return this.CF.getAttribute("weekMulIndex");
      }
      
      public function set weekMulIndex(v0:Number) : void
      {
         this.CF.setAttribute("weekMulIndex",v0);
      }
      
      public function set weekDropDateStr(str0:String) : void
      {
         this._weekDropDateStr = TextWay.toCode32(str0);
      }
      
      public function get weekDropDateStr() : String
      {
         return TextWay.getText32(this._weekDropDateStr);
      }
      
      public function get swapNum() : Number
      {
         return this.CF.getAttribute("swapNum");
      }
      
      public function set swapNum(v0:Number) : void
      {
         this.CF.setAttribute("swapNum",v0);
      }
      
      public function get madN() : Number
      {
         return this.CF.getAttribute("madN");
      }
      
      public function set madN(v0:Number) : void
      {
         this.CF.setAttribute("madN",v0);
      }
      
      public function get madG() : Number
      {
         return this.CF.getAttribute("madG");
      }
      
      public function set madG(v0:Number) : void
      {
         this.CF.setAttribute("madG",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.dataArr = ClassProperty.copySaveArray(obj0["dataArr"],TaskSave);
         this.todayCompleteNumObj = ClassProperty.copyObj(obj0["todayCompleteNumObj"]);
         this.todayBuyNumObj = ClassProperty.copyObj(obj0["todayBuyNumObj"]);
      }
      
      public function getTrueTodayCompleteNum(type0:String) : Number
      {
         return this.getTodayCompleteNum(type0) + this.getTodayBuyNum(type0);
      }
      
      public function dealSaveObj(obj0:Object) : void
      {
         var s0:TaskSave = null;
         var newArr0:Array = [];
         for each(s0 in this.dataArr)
         {
            newArr0.push(s0.getSaveObj());
         }
         obj0["dataArr"] = newArr0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.swapNum = 0;
         this.clearDayNumData();
      }
      
      public function newWeek() : void
      {
         this.clearWeekNumData();
         this.weekMulIndex = 0;
      }
      
      public function addTodayCompleteNum(type0:String, num0:int) : void
      {
         var nowNum0:int = this.getTodayCompleteNum(type0);
         var finalNum0:int = nowNum0 + num0;
         this.setTodayCompleteNum(type0,finalNum0);
      }
      
      public function setTodayCompleteNum(type0:String, num0:int) : void
      {
         this.todayCompleteNumObj = ClassProperty.copyObj(this.todayCompleteNumObj);
         this.todayCompleteNumObj[type0] = Base64.encodeString(String(num0));
      }
      
      public function getTodayCompleteNum(type0:String) : Number
      {
         if(this.todayCompleteNumObj.hasOwnProperty(type0))
         {
            return Number(Base64.decode(this.todayCompleteNumObj[type0]));
         }
         return 0;
      }
      
      public function addTodayBuyNum(type0:String, num0:int) : void
      {
         var nowNum0:int = this.getTodayBuyNum(type0);
         this.setTodayBuyNum(type0,nowNum0 + num0);
      }
      
      public function setTodayBuyNum(type0:String, num0:int) : void
      {
         this.todayBuyNumObj = ClassProperty.copyObj(this.todayBuyNumObj);
         this.todayBuyNumObj[type0] = Base64.encodeString(String(num0));
      }
      
      public function getTodayBuyNum(type0:String) : Number
      {
         if(this.todayBuyNumObj.hasOwnProperty(type0))
         {
            return Number(Base64.decode(this.todayBuyNumObj[type0]));
         }
         return 0;
      }
      
      public function getSwapBtnTip() : String
      {
         var s0:String = "每天玩家可以换" + getSwapMax() + "次地图。";
         return s0 + ("\n今天还可以换" + TextMethod.redZeroOrGreen(getSwapMax() - this.swapNum) + "次。");
      }
      
      public function getSwapCanB() : Boolean
      {
         return this.swapNum < getSwapMax();
      }
      
      public function clearSaveNoDefine() : void
      {
         var n:* = undefined;
         var s0:TaskSave = null;
         var d0:TaskDefine = null;
         var arr0:Array = [];
         for(n in this.dataArr)
         {
            s0 = this.dataArr[n];
            d0 = Gaming.defineGroup.task.getOneDefine(s0.name);
            if(d0 is TaskDefine)
            {
               arr0.push(s0);
            }
         }
         this.dataArr = arr0;
      }
      
      public function clearDayNumData() : void
      {
         var obj0:Object = {};
         if(this.todayCompleteNumObj.hasOwnProperty("week"))
         {
            obj0["week"] = this.todayCompleteNumObj["week"];
         }
         this.todayCompleteNumObj = obj0;
         var obj2:Object = {};
         if(this.todayBuyNumObj.hasOwnProperty("week"))
         {
            obj2["week"] = this.todayBuyNumObj["week"];
         }
         this.todayBuyNumObj = obj2;
      }
      
      private function clearWeekNumData() : void
      {
         this.setTodayCompleteNum("week",0);
         this.setTodayBuyNum("week",0);
      }
   }
}

