package dataAll._app.post
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.post.define.PostDefine;
   import dataAll._app.post.define.PostDefineGroup;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.pro.PropertyArrayDefine;
   
   public class PostData implements IO_EquipAddGetter
   {
      
      public var playerData:PlayerData;
      
      public var save:PostSave;
      
      public function PostData()
      {
         super();
      }
      
      public function inData_bySave(s0:PostSave) : void
      {
         this.save = s0;
      }
      
      public function getGoodDataArr() : Array
      {
         var d0:GoodsDefine = null;
         var da0:GoodsData = null;
         var dArr0:Array = Gaming.defineGroup.goods.monthCardArr.concat();
         var daArr0:Array = [];
         for each(d0 in dArr0)
         {
            da0 = this.playerData.goods.getData(d0.name);
            da0.numDisFun = PostDefineGroup.numDisFun;
            daArr0.push(da0);
         }
         return daArr0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.openUI(timeStr0);
      }
      
      public function openUI(timeStr0:String) : void
      {
         this.save.openUI(timeStr0,this.playerData.level);
      }
      
      public function havePostB() : Boolean
      {
         return this.save.nowPost != "";
      }
      
      public function buyPan(name0:String, timeStr0:String) : String
      {
         var day0:int = 0;
         var dayStr0:String = null;
         var str0:String = null;
         var defName0:String = PostDefine.getPostName(name0);
         if(this.save.nowPost != defName0 && this.save.nowPost != "")
         {
            day0 = this.save.converNewPostDay(timeStr0,defName0);
            dayStr0 = day0 > 0 ? "同时补偿" + ComMethod.color(day0 + "天","#00FF00") + "，" : "";
            str0 = "";
            str0 = "你购买的职务与当前职务不同，";
            str0 += "\n职务持续时间将会被覆盖，" + dayStr0;
            return str0 + "\n是否继续购买？";
         }
         return "";
      }
      
      public function useCard(name0:String, num0:int, timeStr0:String) : void
      {
         var defName0:String = PostDefine.getPostName(name0);
         var onlyDay0:int = PostDefine.getOnlyDay(name0);
         var d0:PostDefine = Gaming.defineGroup.post.getDefine(defName0);
         this.save.useCardDefine(d0,num0,timeStr0,this.playerData.level,onlyDay0);
      }
      
      public function getNowPostDefine() : PostDefine
      {
         return Gaming.defineGroup.post.getDefine(this.save.nowPost);
      }
      
      public function isDropFollowB() : Boolean
      {
         if(this.save.dropB)
         {
            if(this.haveDropRightB())
            {
               return true;
            }
         }
         return false;
      }
      
      public function haveDropRightB() : Boolean
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.dropFollowB;
         }
         return false;
      }
      
      public function haveAutoDesB() : Boolean
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.autoDesB;
         }
         return false;
      }
      
      public function getTopStr() : String
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.cnName + " " + this.save.postLv + "级";
         }
         return "";
      }
      
      public function getSweepingNum() : int
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.sweepingNum;
         }
         return 0;
      }
      
      public function getLotteryNum() : int
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.lotteryNum;
         }
         return 0;
      }
      
      public function getProAddObj() : Object
      {
         var obj0:Object = this.getOnlyProAddObj();
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            obj0["loveAdd"] = d0.loveAdd;
         }
         return obj0;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      private function getOnlyProAddObj() : Object
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.getProAddObj(this.save.postLv);
         }
         return {};
      }
      
      public function getProAddStr() : String
      {
         var n:* = undefined;
         var proD0:PropertyArrayDefine = null;
         var str0:String = "";
         var obj0:Object = this.getOnlyProAddObj();
         var index0:int = 0;
         for(n in obj0)
         {
            index0++;
            proD0 = EquipPropertyDataCreator.getPropertyArrayDefine(n);
            str0 += (index0 == 1 ? "" : "\n") + proD0.cnName + ComMethod.color(" +" + proD0.getValueString(obj0[n]),"#00FF00");
         }
         return str0;
      }
      
      public function isNoDeductLoveB() : Boolean
      {
         var d0:PostDefine = this.getNowPostDefine();
         if(Boolean(d0))
         {
            return d0.noDeductLoveB;
         }
         return false;
      }
   }
}

