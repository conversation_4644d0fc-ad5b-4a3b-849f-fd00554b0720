package com.sounto.image
{
   public class LabelParser
   {
      
      private static var type_arr:Array = ["stand","move","squat","jump","fill","hurt","die","Skill","Attack","stru","Ride"];
      
      public function LabelParser()
      {
         super();
      }
      
      public static function getType(l0:String) : String
      {
         var n:* = undefined;
         var type0:String = null;
         for(n in type_arr)
         {
            type0 = type_arr[n];
            if(l0.indexOf(type0) >= 0)
            {
               return type0;
            }
         }
         return "";
      }
   }
}

