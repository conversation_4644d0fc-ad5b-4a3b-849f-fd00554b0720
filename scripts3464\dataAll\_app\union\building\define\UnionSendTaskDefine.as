package dataAll._app.union.building.define
{
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class UnionSendTaskDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function UnionSendTaskDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.gift.inData_byXML(xml0.gift);
      }
   }
}

