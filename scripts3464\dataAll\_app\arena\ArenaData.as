package dataAll._app.arena
{
   import UI.arena.ArenaTopCtrl;
   import UI.count.CountCtrl;
   import com.sounto.cf.NiuBiCF;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._player.PlayerData;
   import gameAll.level.arena.ArenaLevelData;
   import gameAll.level.arena.ArenaType;
   
   public class ArenaData
   {
      
      public static const CLOSE_PHASE:int = 999999;
      
      public static const CLOSE_LABEL:Array = ["top","newGift"];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var save:ArenaSave;
      
      public var nowCount:ArenaCountData = new ArenaCountData();
      
      private var beforeTopName:String = "";
      
      public var noConnectB:Boolean = false;
      
      private var addedScoreB:Boolean = true;
      
      public var playerData:PlayerData;
      
      public function ArenaData()
      {
         super();
         this.trueRank = -1;
         this.myRank = 10001;
      }
      
      public function get myRank() : Number
      {
         return this.CF.getAttribute("myRank");
      }
      
      public function set myRank(v0:Number) : void
      {
         this.CF.setAttribute("myRank",v0);
      }
      
      public function get trueRank() : Number
      {
         return this.CF.getAttribute("trueRank");
      }
      
      public function set trueRank(v0:Number) : void
      {
         this.CF.setAttribute("trueRank",v0);
      }
      
      public function inData_bySave(s0:ArenaSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
      }
      
      public function startLevel() : void
      {
         this.useTodayNum();
      }
      
      public function clearNowCount() : void
      {
         this.nowCount = new ArenaCountData();
      }
      
      public function inNowCount(winB0:Boolean, mePd0:PlayerData, a_da0:ArenaLevelData, nowTime0:String) : int
      {
         var overB0:Boolean = false;
         this.clearNowCount();
         var normalB0:Boolean = a_da0.arenaType == ArenaType.TYPE_NORMAL;
         if(normalB0)
         {
            if(!winB0)
            {
               this.save.streakNum = 0;
            }
            this.nowCount.inData(winB0,mePd0,this.myRank,this.trueRank,a_da0.rivalTopData);
            this.addScoreByCount(winB0);
            this.save.addUid(a_da0.rivalTopData.uid);
            this.save.addUname(a_da0.rivalTopData.uname);
            overB0 = ArenaTopCtrl.noAddScoreB();
            if(!overB0)
            {
               this.addRecord(a_da0.rivalTopData,nowTime0,a_da0.rivalPC.da.base.save.playerName,normalB0);
            }
            if(winB0)
            {
               this.playerData.getSave().gift.arenaWin();
            }
         }
         else
         {
            this.nowCount.inOnlyWinData(winB0);
         }
         var stampNum0:int = this.nowCount.stampNum;
         var addNum0:int = this.playerData.getDropMerge().arenaStampDropNum;
         if(addNum0 > 10)
         {
            addNum0 = 10;
         }
         if(stampNum0 <= 0)
         {
            addNum0 = 0;
         }
         stampNum0 += addNum0;
         this.nowCount.stampNum = stampNum0;
         this.addStampNum(stampNum0);
         return this.nowCount.score;
      }
      
      public function getOutWinNum() : int
      {
         return 2;
      }
      
      public function addScore(v0:int) : void
      {
         this.save.score += v0;
         this.playerData.main.addScore(v0);
      }
      
      public function addScoreByCount(winB0:Boolean) : void
      {
         var overB0:Boolean = false;
         if(!this.addedScoreB)
         {
            ++this.save.challengeNum;
            overB0 = ArenaTopCtrl.noAddScoreB();
            if(!overB0 && this.save.challengeNum <= 5)
            {
               this.addScore(this.nowCount.score);
            }
            else
            {
               this.nowCount.clearScore();
            }
            this.addedScoreB = true;
            if(winB0)
            {
               ++this.save.winNum;
               if(!overB0)
               {
                  ++this.save.seasonWinNum;
               }
               ++this.save.streakNum;
            }
            else
            {
               if(!overB0)
               {
                  ++this.save.seasonFailNum;
               }
               this.save.streakNum = 0;
            }
         }
      }
      
      public function inMyRank(v0:Number) : void
      {
         this.trueRank = v0;
         if(v0 == -1)
         {
            this.myRank = 10001;
         }
         else
         {
            this.myRank = v0;
         }
      }
      
      public function inNowPhase(phase0:int) : void
      {
         this.save.inNowPhase(phase0);
      }
      
      public function getTopBarDefineGroup() : TopBarDefineGroup
      {
         var sd0:TopBarDefineGroup = null;
         var d0:TopBarDefineGroup = null;
         var heroLv0:int = this.playerData.level;
         if(this.save.topName != "")
         {
            sd0 = Gaming.defineGroup.top.getDefine(this.save.topName);
            if(sd0.maxLevel < heroLv0)
            {
               this.save.topName = "";
            }
            else
            {
               d0 = sd0;
            }
         }
         if(!d0)
         {
            d0 = Gaming.defineGroup.top.getArenaDefineByLv(heroLv0);
         }
         return d0;
      }
      
      public function getCanChooseTopArr() : Array
      {
         var d0:TopBarDefineGroup = null;
         var lv0:int = this.playerData.level;
         var arr0:Array = Gaming.defineGroup.top.getArrByGather("arena");
         var narr0:Array = [];
         var num0:int = 0;
         for each(d0 in arr0)
         {
            if(lv0 <= d0.maxLevel)
            {
               narr0.unshift(d0);
               num0++;
               if(num0 >= 3)
               {
                  break;
               }
            }
         }
         return narr0;
      }
      
      public function chooseTop(name0:String) : void
      {
         this.beforeTopName = this.save.topName;
         this.save.topName = name0;
         this.save.topSetB = true;
      }
      
      public function recoveryTop() : void
      {
         this.save.topName = this.beforeTopName;
         this.save.topSetB = false;
      }
      
      public function addStampNum(num0:int) : void
      {
         this.playerData.thingsBag.addDataByName(PriceType.ARENA_STAMP,num0);
         this.save.arenaStampNum += num0;
      }
      
      public function addStampNumOnly(num0:int) : void
      {
         this.save.arenaStampNum += num0;
      }
      
      public function addRecord(da0:TopBarData, time0:String, playerName0:String, normalB0:Boolean) : void
      {
         this.save.record.addTopBarData(da0,this.nowCount,time0,playerName0,normalB0);
      }
      
      public function getRecordDataArr() : Array
      {
         return this.save.record.getDataArr();
      }
      
      public function useTodayNum() : void
      {
         var v0:int = this.save.todayNum;
         if(v0 < 1)
         {
            INIT.showError("挑战次数已经为0，无法消耗！");
         }
         v0--;
         this.save.todayNum = v0;
         this.addedScoreB = false;
         CountCtrl.arenaStart();
      }
      
      public function addTodayNum() : void
      {
         this.save.todayNum += 2;
         this.save.canAddNum -= 2;
      }
      
      public function zuobiPan() : String
      {
         return "";
      }
      
      public function maxScoreZuobiPan() : String
      {
         return "";
      }
      
      private function getMaxScoreByNum() : int
      {
         var winNum0:int = this.save.seasonWinNum;
         var failNum0:int = this.save.seasonFailNum;
         return winNum0 * 140 + failNum0 * 30 + 20;
      }
      
      public function overZuobiPan() : Boolean
      {
         var winNum0:int = this.save.seasonWinNum;
         var failNum0:int = this.save.seasonFailNum;
         var _max0:int = 5;
         if(winNum0 + failNum0 > _max0 * 6)
         {
            return true;
         }
         return false;
      }
   }
}

