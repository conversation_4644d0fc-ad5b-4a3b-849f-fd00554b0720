package dataAll.arms.define
{
   public class ArmsPro
   {
      
      public static const armsType:String = "armsType";
      
      public static const color:String = "color";
      
      public static const hurtRatio:String = "hurtRatio";
      
      public static const attackGap:String = "attackGap";
      
      public static const shootSpeed:String = "shootSpeed";
      
      public static const capacity:String = "capacity";
      
      public static const reloadGap:String = "reloadGap";
      
      public static const shootWidth:String = "shootWidth";
      
      public static const precision:String = "precision";
      
      public static const godSkillArr:String = "godSkillArr";
      
      public static const bulletWidth:String = "bulletWidth";
      
      public static const filterRangeArr:Array = [shootSpeed,capacity,reloadGap,precision,shootWidth];
      
      public static const filterArr:Array = [armsType,color].concat(filterRangeArr).concat([godSkillArr]);
      
      public function ArmsPro()
      {
         super();
      }
      
      public static function toUI(pro0:String) : String
      {
         if(pro0 == attackGap)
         {
            pro0 = shootSpeed;
         }
         return pro0;
      }
      
      public static function toUIValue(pro0:String, v0:Number) : Number
      {
         if(pro0 == attackGap)
         {
            v0 = 1 / v0;
         }
         return v0;
      }
      
      public static function toDef(pro0:String) : String
      {
         if(pro0 == shootSpeed)
         {
            pro0 = attackGap;
         }
         return pro0;
      }
      
      public static function toDefValue(pro0:String, v0:Number) : Number
      {
         if(pro0 == shootSpeed)
         {
            v0 = 1 / v0;
         }
         return v0;
      }
   }
}

