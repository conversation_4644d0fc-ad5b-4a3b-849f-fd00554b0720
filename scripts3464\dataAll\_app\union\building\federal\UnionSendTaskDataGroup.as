package dataAll._app.union.building.federal
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.UnionBuildingSaveGroup;
   import dataAll._app.union.building.cooking.CookingState;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class UnionSendTaskDataGroup
   {
      
      public var saveGroup:UnionBuildingSaveGroup;
      
      public var buildingData:UnionBuildingData;
      
      public var obj:Object = {};
      
      public var fleshB:Boolean = false;
      
      private var beforeGiftNum:int = 0;
      
      public function UnionSendTaskDataGroup()
      {
         super();
      }
      
      public function inData_bySaveGroup(sg0:UnionBuildingSaveGroup, buildingData0:UnionBuildingData) : void
      {
         var s0:UnionSendTaskSave = null;
         var da0:UnionSendTaskData = null;
         this.obj = {};
         this.saveGroup = sg0;
         this.buildingData = buildingData0;
         for each(s0 in sg0.sendTaskObj)
         {
            da0 = new UnionSendTaskData();
            da0.inData_bySave(s0,buildingData0);
            this.obj[s0.name] = da0;
         }
         this.sortDataArr();
      }
      
      public function getBuildGrowLv() : int
      {
         var da0:UnionBuildingData = this.buildingData;
         if(da0 is UnionBuildingData)
         {
            return da0.save.lv;
         }
         return 1;
      }
      
      private function sortDataArr() : void
      {
         var da0:UnionSendTaskData = null;
         var arr0:Array = this.getDataArr();
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            da0 = arr0[i];
            da0.save.index = i;
         }
      }
      
      public function newDayCtrl() : void
      {
         var da0:UnionSendTaskData = null;
         for each(da0 in this.obj)
         {
            da0.newDayCtrl();
         }
         this.beforeGiftNum = 0;
      }
      
      public function getDataArr() : Array
      {
         var da0:UnionSendTaskData = null;
         var arr2:Array = [];
         for each(da0 in this.obj)
         {
            arr2.push(da0);
         }
         arr2.sort(this.sortByIndex);
         return arr2;
      }
      
      private function getNowIngTaskData() : UnionSendTaskData
      {
         var da0:UnionSendTaskData = null;
         for each(da0 in this.obj)
         {
            if(da0.getState() == "ing")
            {
               return da0;
            }
         }
         return null;
      }
      
      private function getAllSurplusTime() : Number
      {
         var da0:UnionSendTaskData = null;
         var state0:String = null;
         var v0:Number = 0;
         for each(da0 in this.obj)
         {
            state0 = da0.getState();
            if(state0 == "ing")
            {
               v0 += da0.save.surplusTime;
            }
            else if(state0 == CookingState.WAITING)
            {
               v0 += da0.getMaxSurplusTime();
            }
         }
         return v0;
      }
      
      public function getIngStr() : String
      {
         var num0:int = 0;
         var str0:String = "";
         var state0:String = this.getAllState();
         var da0:UnionSendTaskData = this.getNowIngTaskData();
         if(!da0)
         {
            if(state0 == CookingState.COMPLETE)
            {
               str0 += ComMethod.color("已完成所有派遣任务","#00FF00");
            }
            else
            {
               str0 += "";
            }
         }
         else if(state0 == CookingState.ING)
         {
            str0 += da0.def.cnName + ComMethod.color("[进行中]","#00FF00");
            str0 += "\n" + ComMethod.color("总剩余时间：" + ComMethod.getTimeStrTwo(this.getAllSurplusTime()),"#00FFFF");
         }
         if(state0 == CookingState.ING || state0 == CookingState.COMPLETE)
         {
            num0 = this.getAllGiftNum();
            str0 += "\n" + ComMethod.color("累计获得军备物资x" + num0,"#FF99FF");
            if(num0 > 0)
            {
            }
         }
         return str0;
      }
      
      private function sortByIndex(da1:UnionSendTaskData, da2:UnionSendTaskData) : int
      {
         var n1:int = da1.save.index;
         var n2:int = da2.save.index;
         if(n1 < n2)
         {
            return -1;
         }
         if(n2 < n1)
         {
            return 1;
         }
         return 0;
      }
      
      public function oneFirst(da0:UnionSendTaskData) : void
      {
         var arr0:Array = this.getDataArr();
         var firstDa0:UnionSendTaskData = arr0[0];
         var index0:int = firstDa0.save.index;
         firstDa0.save.index = da0.save.index;
         da0.save.index = index0;
      }
      
      public function getAllState() : String
      {
         var da0:UnionSendTaskData = null;
         var state0:String = null;
         var noNum0:int = 0;
         var comNum0:int = 0;
         var allNum0:int = 0;
         for each(da0 in this.obj)
         {
            allNum0++;
            state0 = da0.getState();
            if(state0 == CookingState.NO)
            {
               noNum0++;
            }
            if(state0 == CookingState.COMPLETE)
            {
               comNum0++;
            }
         }
         if(allNum0 == noNum0)
         {
            return CookingState.NO;
         }
         if(allNum0 == comNum0)
         {
            return CookingState.COMPLETE;
         }
         return CookingState.ING;
      }
      
      private function getAllTime() : Number
      {
         var da0:UnionSendTaskData = null;
         var all0:Number = 0;
         for each(da0 in this.obj)
         {
            all0 += da0.getMaxSurplusTime();
         }
         return all0;
      }
      
      private function getAllOnHookTime() : Number
      {
         var da0:UnionSendTaskData = null;
         var ing0:Number = 0;
         for each(da0 in this.obj)
         {
            ing0 += da0.save.onHookTime;
         }
         return ing0;
      }
      
      public function getGiftAddDefineGroup() : GiftAddDefineGroup
      {
         var d0:GiftAddDefineGroup = null;
         var num0:int = this.getAllGiftNum(true);
         if(num0 > 0)
         {
            d0 = new GiftAddDefineGroup();
            d0.addGiftByStr("things;militarySupplies;" + num0);
            return d0;
         }
         return null;
      }
      
      public function getAllGiftNum(canB0:Boolean = false) : int
      {
         var all0:Number = this.getAllTime();
         var ing0:Number = this.getAllOnHookTime();
         var mul0:Number = ing0 / all0;
         if(canB0)
         {
            mul0 = (ing0 - this.saveGroup.haveSendTaskGiftTime) / all0;
         }
         var max0:int = Gaming.defineGroup.union.building.property.getPropertyValue("sendTaskGift",this.getBuildGrowLv()) * this.getDataArr().length;
         return int(mul0 * max0);
      }
      
      public function getGift() : void
      {
         this.saveGroup.haveSendTaskGiftTime = this.getAllOnHookTime();
      }
      
      public function nextEat() : void
      {
         var da0:UnionSendTaskData = null;
         var state0:String = null;
         var arr0:Array = this.getDataArr();
         var beforeState0:String = CookingState.COMPLETE;
         for(var i:int = 0; i < arr0.length; i++)
         {
            da0 = arr0[i];
            state0 = da0.getState();
            if(state0 == CookingState.NO)
            {
               if(i == 0)
               {
                  da0.setState(CookingState.ING);
               }
               else
               {
                  da0.setState(CookingState.WAITING);
               }
            }
            else if(state0 == CookingState.WAITING)
            {
               if(beforeState0 == CookingState.COMPLETE)
               {
                  da0.setState(CookingState.ING);
               }
            }
            else if(state0 == CookingState.ING)
            {
            }
            beforeState0 = da0.getState();
         }
         this.fleshB = true;
      }
      
      public function FTimerSecond() : void
      {
         var da0:UnionSendTaskData = null;
         var overB0:Boolean = false;
         for each(da0 in this.obj)
         {
            if(da0.FTimerSecond())
            {
               overB0 = true;
            }
            if(da0.getState() == CookingState.ING)
            {
            }
         }
         if(overB0)
         {
            this.nextEat();
         }
         var num0:int = this.getAllGiftNum();
         if(this.beforeGiftNum < num0)
         {
            this.beforeGiftNum = num0;
            this.fleshB = true;
         }
      }
   }
}

