package dataAll._app.top.player
{
   import com.sounto.utils.ClassProperty;
   
   public class PlayerTopReturnData
   {
      
      public static var pro_arr:Array = [];
      
      public var rId:int = 0;
      
      public var code:String = "";
      
      public var curRank:Number = 0;
      
      public var curScore:Number = 0;
      
      public var lastRank:Number = 0;
      
      public var lastScore:Number = 0;
      
      public function PlayerTopReturnData()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function inData_byTopBarData(obj0:Object) : void
      {
         this.curRank = obj0.rank;
         this.curScore = obj0.score;
         this.lastRank = 0;
         this.lastScore = 0;
      }
      
      public function setToSimulated(da0:PlayerTopUploadData) : void
      {
         this.rId = da0.rId;
         this.code = "10000";
         this.curRank = int(Math.random() * 500);
         this.curScore = da0.score;
      }
      
      public function setToSimulatedNoData(rId0:uint) : void
      {
         this.rId = rId0;
         this.code = "10000";
         this.curRank = int(Math.random() * 500);
         this.curScore = 321;
      }
   }
}

