package dataAll.arms.creator
{
   import com.sounto.image.DisplayMotionDefine;
   import dataAll.arms.define.GunPart;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Graphics;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class Gun<PERSON>mage extends Sprite
   {
      
      public static const BULLET_HAND_P:Point = new Point(-12,21);
      
      public var label:String = "";
      
      public var father:String = "";
      
      public var p_obj:Object = {};
      
      public var gun:Sprite = new Sprite();
      
      public var bullet:Sprite = new Sprite();
      
      public var iconBmp:BitmapData = null;
      
      public var clearResourceB:Boolean = true;
      
      public var die:int = 0;
      
      private var focoShape:Shape = null;
      
      public function GunImage()
      {
         super();
         addChild(this.bullet);
         addChild(this.gun);
      }
      
      public function gotoAndPlay(str0:String) : void
      {
      }
      
      private function addFocoShape() : void
      {
         this.focoShape = new Shape();
         addChild(this.focoShape);
         this.focoShape.x = -8;
         this.focoShape.visible = false;
         var gh0:Graphics = this.focoShape.graphics;
         gh0.beginFill(16711680,1);
         gh0.drawRect(-33,-12,33,3);
      }
      
      public function setFocoMul(v0:Number) : void
      {
         if(v0 > 0)
         {
            if(!this.focoShape)
            {
               this.addFocoShape();
            }
            this.focoShape.visible = true;
            this.focoShape.scaleX = v0;
         }
         else if(Boolean(this.focoShape))
         {
            this.focoShape.visible = false;
         }
      }
      
      public function get right_p() : DisplayMotionDefine
      {
         return this.p_obj[GunPart.right_hand];
      }
      
      public function get left_p() : DisplayMotionDefine
      {
         return this.p_obj[GunPart.left_hand];
      }
      
      public function get bullet_p() : DisplayMotionDefine
      {
         return this.p_obj[GunPart.bullet];
      }
      
      public function get bulletHand_p() : DisplayMotionDefine
      {
         return this.p_obj[GunPart.bullet_hand];
      }
      
      public function get shoot_p() : DisplayMotionDefine
      {
         return this.p_obj[GunPart.shoot];
      }
      
      public function getPoint(name0:String) : DisplayMotionDefine
      {
         return this.p_obj[name0];
      }
      
      public function setBulletHandToNormal() : void
      {
         var d0:DisplayMotionDefine = new DisplayMotionDefine();
         var bulletMot0:DisplayMotionDefine = this.getPoint(GunPart.bullet);
         if(!bulletMot0)
         {
            bulletMot0 = this.getPoint(GunPart.left_hand);
         }
         d0.x = 12 + bulletMot0.x;
         d0.y = 21 + bulletMot0.y;
         d0.rotation = 90;
         this.setPoint(GunPart.bullet_hand,d0);
      }
      
      public function setPoint(name0:String, d0:DisplayMotionDefine) : void
      {
         this.p_obj[name0] = d0;
      }
      
      public function inputPoint(sp0:Sprite, name0:String, scale:Number = 1) : void
      {
         var pointName0:String = null;
         var pointSp0:DisplayObject = null;
         var spMot0:DisplayMotionDefine = null;
         var mot0:DisplayMotionDefine = null;
         var pointArr0:Array = GunPart.POINT_ARR;
         for each(pointName0 in pointArr0)
         {
            pointSp0 = sp0.getChildByName("_" + pointName0);
            if(name0 == GunPart.body)
            {
               spMot0 = DisplayMotionDefine.ZERO;
            }
            else
            {
               spMot0 = this.getPoint(name0);
            }
            if(Boolean(pointSp0) && Boolean(spMot0))
            {
               mot0 = new DisplayMotionDefine();
               mot0.x = pointSp0.x * scale + spMot0.x;
               mot0.y = pointSp0.y * scale + spMot0.y;
               mot0.rotation = pointSp0.rotation;
               this.setPoint(pointName0,mot0);
               sp0.removeChild(pointSp0);
            }
         }
      }
      
      public function setPointScale(value0:Number) : void
      {
         var n:* = undefined;
         var mot0:DisplayMotionDefine = null;
         for(n in this.p_obj)
         {
            mot0 = this.p_obj[n];
            mot0.x *= value0;
            mot0.y *= value0;
         }
      }
      
      public function moveByRightHand() : void
      {
         var n:* = undefined;
         var cx0:Number = this.right_p.x;
         for(n in this.p_obj)
         {
            this.p_obj[n].x -= cx0;
         }
         this.bullet.x -= cx0;
         this.gun.x -= cx0;
      }
      
      public function clone() : GunImage
      {
         var img0:GunImage = new GunImage();
         this.copyBmp(this.gun,img0.gun);
         this.copyBmp(this.bullet,img0.bullet);
         img0.iconBmp = this.iconBmp;
         img0.p_obj = this.p_obj;
         img0.label = this.label;
         img0.father = this.father;
         return img0;
      }
      
      private function copyBmp(sp0:Sprite, sp1:Sprite) : void
      {
         var oneMC0:* = undefined;
         var bitmap0:Bitmap = null;
         var len0:int = sp0.numChildren;
         for(var i:int = 0; i < len0; i++)
         {
            oneMC0 = sp0.getChildAt(i);
            if(oneMC0 is Bitmap)
            {
               bitmap0 = new Bitmap(oneMC0.bitmapData);
               bitmap0.smoothing = oneMC0.smoothing;
               bitmap0.x = oneMC0.x;
               bitmap0.y = oneMC0.y;
               bitmap0.scaleX = oneMC0.scaleX;
               bitmap0.scaleY = oneMC0.scaleY;
               bitmap0.rotation = oneMC0.rotation;
               sp1.addChild(bitmap0);
            }
         }
         sp1.x = sp0.x;
         sp1.y = sp0.y;
      }
      
      public function clearBmpData() : void
      {
         var mc0:Bitmap = null;
         if(this.gun.numChildren > 0)
         {
            mc0 = this.gun.getChildAt(0) as Bitmap;
            if(Boolean(mc0))
            {
               mc0.bitmapData.dispose();
            }
         }
         if(this.bullet.numChildren > 0)
         {
            mc0 = this.bullet.getChildAt(0) as Bitmap;
            if(Boolean(mc0))
            {
               mc0.bitmapData.dispose();
            }
         }
         if(Boolean(this.iconBmp))
         {
            this.iconBmp.dispose();
         }
      }
   }
}

