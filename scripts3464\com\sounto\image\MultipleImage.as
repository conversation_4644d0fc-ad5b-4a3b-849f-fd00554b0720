package com.sounto.image
{
   import com.sounto.math.GeoMethod;
   import dataAll.body.img.BodyImageLabel;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class MultipleImage extends Sprite
   {
      
      public var enabled:Boolean = true;
      
      public var mcArr:Array = [];
      
      public var mcObj:Object = {};
      
      public var labelArr:Array = [];
      
      public var nowMc:SimulateMovieClip = null;
      
      public var nowLabel:String = "";
      
      public var nowLabelType:String = "";
      
      public var normal_scaleX:Number = 1;
      
      protected var lockLeftB:Boolean = false;
      
      private var _scaleX:Number = 1;
      
      protected var scaleBuff:Number = 1;
      
      protected var scaleBuffY:Number = 1;
      
      public function MultipleImage()
      {
         super();
      }
      
      public function add(mc0:SimulateMovieClip) : void
      {
         var label0:String = mc0.label;
         if(this.labelArr.indexOf(label0) == -1)
         {
            this.mcArr.push(mc0);
            this.labelArr.push(label0);
            this.mcObj[label0] = mc0;
            mc0.labelType = LabelParser.getType(label0);
            if(!this.nowMc)
            {
               this.show(label0);
            }
         }
      }
      
      protected function show(label0:String) : Boolean
      {
         if(this.mcObj.hasOwnProperty(label0))
         {
            if(Boolean(this.nowMc))
            {
               this.nowMc.stop();
               removeChild(this.nowMc);
            }
            this.nowMc = this.mcObj[label0];
            this.nowMc.enabled = this.enabled;
            this.nowLabel = label0;
            this.nowLabelType = this.nowMc.labelType;
            addChildAt(this.nowMc,0);
            this.fleshScale();
            if(label0 == BodyImageLabel.stand || label0 == BodyImageLabel.move || label0 == BodyImageLabel.run)
            {
               this.nowMc.gotoAndPlay(int(this.nowMc.totalFrames * Math.random() + 1));
            }
            else
            {
               this.nowMc.gotoAndPlay(1);
            }
            return true;
         }
         return false;
      }
      
      public function haveLabel(label0:String) : Boolean
      {
         return this.mcObj.hasOwnProperty(label0);
      }
      
      public function testShow() : void
      {
         this.show(this.labelArr[int(this.labelArr.length * Math.random())]);
      }
      
      public function pause() : void
      {
         this.enabled = false;
         this.nowMc.pause();
      }
      
      public function resume() : void
      {
         this.enabled = true;
         this.nowMc.resume();
      }
      
      public function flipToX(x0:int) : void
      {
         if(x > x0)
         {
            this.flipToLeft();
         }
         else
         {
            this.flipToRight();
         }
      }
      
      public function flipTo(v0:int) : void
      {
         if(v0 < 0)
         {
            this.flipToLeft();
         }
         else
         {
            this.flipToRight();
         }
      }
      
      public function flipToLeft() : Boolean
      {
         if(this._scaleX != this.normal_scaleX)
         {
            this._scaleX = this.normal_scaleX;
            this.fleshScale();
            return true;
         }
         return false;
      }
      
      public function flipToRight() : Boolean
      {
         if(this.lockLeftB)
         {
            return false;
         }
         if(this._scaleX != -this.normal_scaleX)
         {
            this._scaleX = -this.normal_scaleX;
            this.fleshScale();
            return true;
         }
         return false;
      }
      
      private function fleshScale() : void
      {
         if(this.nowMc.type == SimulateType.bmp)
         {
            this.nowMc.setMustScaleX(this._scaleX * this.scaleBuff);
            this.nowMc.setMustScaleY(this.scaleBuffY);
            super.scaleX = 1;
            super.scaleY = 1;
            if(this.scaleBuff < 1)
            {
            }
         }
         else
         {
            super.scaleX = this._scaleX * this.scaleBuff;
            super.scaleY = this.scaleBuffY;
         }
      }
      
      public function get rightB() : Boolean
      {
         return -this._scaleX * this.scaleBuff * this.normal_scaleX > 0;
      }
      
      public function getDirect() : int
      {
         return this.rightB ? 1 : -1;
      }
      
      public function get endFrameB() : Boolean
      {
         return this.nowMc.currentFrame >= this.nowMc.totalFrames;
      }
      
      override public function get scaleX() : Number
      {
         if(this.nowMc.type == SimulateType.bmp)
         {
            return this._scaleX * this.scaleBuff;
         }
         return super.scaleX;
      }
      
      public function setScaleBuff(v0:Number) : void
      {
         this.scaleBuff = v0;
         this.scaleBuffY = v0;
         this.fleshScale();
      }
      
      public function getScaleBuff() : Number
      {
         return this.scaleBuff;
      }
      
      public function getScaleBuffY() : Number
      {
         return this.scaleBuffY;
      }
      
      override public function set rotationX(value:Number) : void
      {
      }
      
      override public function set rotationY(value:Number) : void
      {
      }
      
      override public function set rotationZ(value:Number) : void
      {
      }
      
      override public function set scale9Grid(innerRectangle:Rectangle) : void
      {
      }
      
      override public function set scaleX(value:Number) : void
      {
      }
      
      override public function set scaleY(value:Number) : void
      {
      }
      
      override public function set height(value:Number) : void
      {
      }
      
      override public function set width(value:Number) : void
      {
      }
      
      public function getRa() : Number
      {
         return rotation / 180 * Math.PI;
      }
      
      public function clear() : void
      {
      }
      
      public function insidePointToOut(x0:Number, y0:Number) : Point
      {
         return GeoMethod.getTruePointInCon(x0,y0,x,y,rotation / 180 * Math.PI,this.scaleX);
      }
      
      public function getImgType() : String
      {
         if(Boolean(this.nowMc))
         {
            return this.nowMc.type;
         }
         return SimulateType.normal;
      }
      
      public function nowIsAttackB() : Boolean
      {
         return this.nowLabelType == "Attack";
      }
      
      public function FTimer(timeStopB0:Boolean = false) : void
      {
         if(this.enabled)
         {
            if(Boolean(this.nowMc))
            {
               this.nowMc.FTimer(timeStopB0);
            }
         }
      }
   }
}

