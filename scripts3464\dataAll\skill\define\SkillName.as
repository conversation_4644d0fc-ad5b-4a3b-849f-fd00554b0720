package dataAll.skill.define
{
   public class SkillName
   {
      
      public static const noUnderMulHurt:String = "noUnderMulHurt";
      
      public static const punctureGod:String = "punctureGod";
      
      public static const positionErrorGod:String = "positionErrorGod";
      
      public static const sameInvi:String = "sameInvi";
      
      public static const Antimatter_die:String = "Antimatter_die";
      
      public static const hurtAddYing:String = "hurtAddYing";
      
      public static const showSurplusArr:Array = ["uavYing"];
      
      public function SkillName()
      {
         super();
      }
   }
}

