package dataAll._app.union.building.cooking
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingCtrl;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.define.UnionCookingDefine;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   
   public class UnionCookingData
   {
      
      public var name:String = "";
      
      public var save:UnionCookingSave;
      
      public var def:UnionCookingDefine;
      
      public var buildingData:UnionBuildingData;
      
      public function UnionCookingData()
      {
         super();
      }
      
      public function inData_bySave(s0:UnionCookingSave, buildingData0:UnionBuildingData) : void
      {
         this.name = s0.name;
         this.save = s0;
         this.def = Gaming.defineGroup.union.building.getCookingDefine(this.name);
         this.buildingData = buildingData0;
      }
      
      public function getBuildGrowLv() : int
      {
         var da0:UnionBuildingData = this.buildingData;
         if(da0 is UnionBuildingData)
         {
            return da0.save.lv;
         }
         return 1;
      }
      
      public function getTimeStr() : String
      {
         var s0:String = this.getState();
         if(s0 == CookingState.NO)
         {
            return "";
         }
         if(s0 == CookingState.ING)
         {
            return "持续时间：" + ComMethod.getTimeStrTwo(this.save.surplusTime);
         }
         return "今日已进食";
      }
      
      public function getState() : String
      {
         return this.save.state;
      }
      
      public function setState(state0:String) : void
      {
         this.save.state = state0;
         if(state0 != CookingState.ING)
         {
            this.save.surplusTime = 0;
         }
      }
      
      public function getEffectLv() : int
      {
         var lv0:int = this.getBuildGrowLv();
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         return lv0;
      }
      
      public function getAddObj() : Object
      {
         return UnionBuildingCtrl.getCookingObjByProArr(this.def.addArr,this.getEffectLv());
      }
      
      public function getMaxSurplusTime() : Number
      {
         var lv0:int = this.getBuildGrowLv();
         var min0:int = Gaming.defineGroup.union.building.property.getPropertyValue("cookingSurplusTime",lv0);
         return min0 * 60;
      }
      
      public function startEat() : void
      {
         if(this.save.state == CookingState.NO)
         {
            this.setState(CookingState.ING);
            this.save.surplusTime = this.getMaxSurplusTime();
         }
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         str0 += "效果持续时间：<yellow " + Math.round(this.getMaxSurplusTime() / 60) + "分钟";
         str0 += "\n\n" + "<i1>|<blue <b>食用后效果(" + this.getEffectLv() + "级)：</b>/>";
         str0 += "\n" + EquipPropertyDataCreator.getText_byObjNoColor(this.getAddObj(),null,true);
         return str0 + ("\n" + "<green <b>" + this.buildingData.def.lvName + "越高，食用后效果越好</b>/>");
      }
      
      public function FTimerSecond() : void
      {
         var t0:Number = NaN;
         if(this.getState() == CookingState.ING)
         {
            t0 = this.save.surplusTime;
            t0--;
            if(t0 <= 0)
            {
               t0 = 0;
               this.setState(CookingState.NO);
            }
            this.save.surplusTime = t0;
         }
      }
   }
}

