package dataAll._app.outfit.define
{
   import com.sounto.utils.ClassProperty;
   import dataAll.skill.define.SkillDefine;
   
   public class OutfitDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var skill:String = "";
      
      public var mustArr:Array = [];
      
      public var iconUrl:String = "";
      
      public function OutfitDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var d0:OutfitMustDefine = null;
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         if(this.iconUrl == "")
         {
            this.iconUrl = "IconGather/" + this.name;
         }
         for each(x0 in xml0.must)
         {
            d0 = new OutfitMustDefine();
            d0.inData_byXML(x0);
            this.mustArr.push(d0);
         }
      }
      
      public function getSkillDefine() : SkillDefine
      {
         return Gaming.defineGroup.skill.getDefineBy("outfitSkill",this.skill);
      }
   }
}

