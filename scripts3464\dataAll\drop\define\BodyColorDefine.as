package dataAll.drop.define
{
   public class BodyColorDefine extends DropColorDefine
   {
      
      public function BodyColorDefine()
      {
         super();
      }
      
      public function getDropArr(type0:String, moreNum0:int = 0) : Array
      {
         var n:* = undefined;
         var pro0:Number = NaN;
         var name0:String = null;
         var num0:int = 0;
         var i:int = 0;
         var proArr0:Array = getProArray(type0);
         var arr0:Array = [];
         if(<PERSON><PERSON><PERSON>(proArr0))
         {
            for(n in proArr0)
            {
               pro0 = Number(proArr0[n]);
               name0 = name[n];
               if(name0 == "life" || name0 == "skillStone")
               {
                  pro0 *= 1 + moreNum0 * 0.2;
               }
               num0 = 1;
               if(type0 == "boss" && (name0 == "arms" || name0 == "equip"))
               {
                  num0 = 3;
               }
               for(i = 0; i < num0; i++)
               {
                  if(Math.random() < pro0)
                  {
                     arr0.push(name0);
                  }
               }
            }
         }
         return arr0;
      }
   }
}

