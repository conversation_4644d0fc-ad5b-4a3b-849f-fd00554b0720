package dataAll._app.edit._def
{
   import dataAll.level.define.LevelDefine;
   
   public class EditProPos
   {
      
      public static const base:String = "base";
      
      public static const level:String = "level";
      
      public static const levelInfo:String = "levelInfo";
      
      public static const levelBossUnit:String = "levelBossUnit";
      
      public function EditProPos()
      {
         super();
      }
      
      public static function getLevelPos(d0:LevelDefine, pos0:String) : Object
      {
         if(pos0 == level)
         {
            return d0;
         }
         if(pos0 == levelInfo)
         {
            return d0.info;
         }
         if(pos0 == levelBossUnit)
         {
            return d0.unitG.getBossOne();
         }
         return null;
      }
      
      public static function getUrlName(name0:String, proPos0:String) : String
      {
         return proPos0 + "." + name0;
      }
      
      public static function getPosObj(proPos0:String, obj0:Object, nullAddB0:Boolean = false) : Object
      {
         var m0:Object = obj0;
         if(proPos0 != "")
         {
            if(obj0.hasOwnProperty(proPos0) == false)
            {
               if(!nullAddB0)
               {
                  return null;
               }
               obj0[proPos0] = {};
            }
            m0 = obj0[proPos0];
         }
         return m0;
      }
   }
}

