package dataAll.gift.save
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.task.TaskData;
   import dataAll.gift.anniver.AnniverGmSave;
   import dataAll.gift.anniver.HolidaySignGiftSave;
   import dataAll.gift.dailySign.DailySignSave;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.guoQing.GuoQingSave;
   import dataAll.gift.zhongQiu.ZhongQiuSave;
   
   public class GiftSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var daily:DailySignSave = new DailySignSave();
      
      public var levelGiftObj:Object = {};
      
      public var isWeekendB:Boolean = false;
      
      public var giftGetNumObj:Object = {};
      
      public var cwObj:NumberEncodeObj = new NumberEncodeObj();
      
      public var dragonChestEnsure:DropEnsureSave = new DropEnsureSave();
      
      public var anniverGm:AnniverGmSave = new AnniverGmSave();
      
      public var millionpackDayB:Boolean = false;
      
      public var millionpackBestB:Boolean = false;
      
      public var kaixue2020_1B:Boolean = false;
      
      public var chunJie2020B:Boolean = false;
      
      public var dujia2019_7B:Boolean = false;
      
      public var darkgoldSuppleB:Boolean = false;
      
      public var zhongQiu24:ZhongQiuSave = new ZhongQiuSave();
      
      public var guoQing24:GuoQingSave = new GuoQingSave();
      
      public var annSign25:HolidaySignGiftSave = new HolidaySignGiftSave();
      
      public var summer25:SummerGiftSave = new SummerGiftSave();
      
      public var fastCar:Boolean = false;
      
      public var ws23:Array = [];
      
      public function GiftSave()
      {
         super();
         this.treasureNum = 0;
      }
      
      public function get treasureNum() : Number
      {
         return this.CF.getAttribute("treasureNum");
      }
      
      public function set treasureNum(v0:Number) : void
      {
         this.CF.setAttribute("treasureNum",v0);
      }
      
      public function get wilderKey() : Number
      {
         return this.CF.getAttribute("wilderKey");
      }
      
      public function set wilderKey(v0:Number) : void
      {
         this.CF.setAttribute("wilderKey",v0);
      }
      
      public function get YXD() : Number
      {
         return this.CF.getAttribute("YXD");
      }
      
      public function set YXD(v0:Number) : void
      {
         this.CF.setAttribute("YXD",v0);
      }
      
      public function childrenAdd() : void
      {
      }
      
      public function get wsNum23() : Number
      {
         return this.CF.getAttribute("wsNum23");
      }
      
      public function set wsNum23(v0:Number) : void
      {
         this.CF.setAttribute("wsNum23",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.levelGiftObj = ClassProperty.copyObj(obj0["levelGiftObj"]);
         this.giftGetNumObj = ClassProperty.copyObj(obj0["giftGetNumObj"]);
      }
      
      public function initSave() : void
      {
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         var da0:StringDate = new StringDate(timeStr0);
         this.isWeekendB = da0.isWeekendB();
         this.treasureNum = 0;
         this.millionpackDayB = false;
         this.wilderKey = 0;
         this.daily.newDayCtrl();
         this.annSign25.newDayCtrl(timeStr0);
         this.summer25.newDayCtrl(timeStr0);
         this.anniverGm.newDayCtrl(timeStr0);
         this.guoQing24.newDayCtrl(timeStr0);
         this.zhongQiu24.newDayCtrl(timeStr0);
         this.fastCar = false;
         this.YXD = 0;
      }
      
      public function newWeek(timeStr0:String) : void
      {
         this.ws23 = [];
         this.cwObj.clearData();
      }
      
      public function setNowReadTime(str0:String) : void
      {
         this.zhongQiu24.setNowReadTime(str0);
      }
      
      public function canShowTreasureB() : Boolean
      {
         if(this.isWeekendB)
         {
            return this.treasureNum < 54 / 9;
         }
         return this.treasureNum < 27 / 9;
      }
      
      public function getGiftGetNum(name0:String) : int
      {
         if(!this.giftGetNumObj.hasOwnProperty(name0))
         {
            return 0;
         }
         return this.giftGetNumObj[name0];
      }
      
      public function addGiftGetNum(name0:String, num0:int = 1) : int
      {
         if(!this.giftGetNumObj.hasOwnProperty(name0))
         {
            this.giftGetNumObj[name0] = 0;
         }
         this.giftGetNumObj[name0] += num0;
         return this.giftGetNumObj[name0];
      }
      
      public function dropEnsureFun(dg0:GiftAddDefineGroup) : int
      {
         var s0:DropEnsureSave = null;
         var varname0:String = dg0.name + "Ensure";
         if(this.hasOwnProperty(varname0))
         {
            s0 = this[varname0] as DropEnsureSave;
            if(Boolean(s0))
            {
               return s0.getDropIndex(dg0);
            }
         }
         return dg0.getRandomIndex();
      }
      
      public function taskComplete(da0:TaskData) : void
      {
         if(da0.def.father == "day")
         {
         }
      }
      
      public function normalLevelWin() : void
      {
      }
      
      public function arenaWin() : void
      {
      }
      
      public function wilderWin() : void
      {
      }
   }
}

