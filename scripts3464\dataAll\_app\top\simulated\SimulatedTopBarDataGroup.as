package dataAll._app.top.simulated
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.login.SaveData4399;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.top.player.PlayerTopUploadData;
   
   public class SimulatedTopBarDataGroup
   {
      
      private static var tempTopRankArr:Array = [1,2,3,4,5,6,7,8,9,10,15,20,30,40,50,60,70,80,90,100,500,1000,5000,10000];
      
      private static var tempTopRankIndex:int = 0;
      
      public function SimulatedTopBarDataGroup()
      {
         super();
      }
      
      public static function getRankListsData(rankListId:uint, pageSize:uint, pageNum:uint) : Array
      {
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefineById(rankListId);
         return TopBarDataGroup.getSimulatedArr(d0,pageSize);
      }
      
      public static function getUserData(uid:String, idx:uint) : Object
      {
         // 为了测试偷存档功能，根据不同的uid返回不同的模拟存档数据
         var baseData:Object = ClassProperty.copyObj(Gaming.PG.save);

         // 根据uid的最后一位数字来模拟不同的存档数据
         var lastDigit:int = parseInt(uid.charAt(uid.length - 1));
         if (isNaN(lastDigit)) lastDigit = 0;

         // 修改一些数据来模拟不同玩家的存档
         if (baseData.base && baseData.base.money) {
            baseData.base.money = baseData.base.money + lastDigit * 10000; // 不同的金币数量
         }
         if (baseData.base && baseData.base.exp) {
            baseData.base.exp = baseData.base.exp + lastDigit * 1000; // 不同的经验值
         }

         return getUserDataByObj(baseData);
      }
      
      public static function getUserDataByObj(obj0:Object) : SaveData4399
      {
         var ud0:SaveData4399 = new SaveData4399();
         ud0.base.setToTest();
         ud0.data = ClassProperty.copyObj(obj0);
         return ud0;
      }
      
      public static function submitScore(obj0:Object) : Object
      {
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.inData_byObj(obj0);
         var r_da0:PlayerTopReturnData = new PlayerTopReturnData();
         r_da0.setToSimulated(da0);
         return r_da0;
      }
      
      public static function getOneRankInfo(rId:uint, uname:String = "") : Object
      {
         var d0:TopBarDefineGroup = Gaming.defineGroup.top.getDefineById(rId);
         var arr0:Array = TopBarDataGroup.getSimulatedArr(d0,1);
         var da0:TopBarData = arr0[0];
         da0.rank = getRandomRank();

         // 如果提供了uname，则根据uname生成对应的uid和数据
         if(uname != "")
         {
            da0.uname = uname;
            // 根据uname生成一个模拟的uid，这里简单地使用uname作为uid的一部分
            da0.uid = "uid_" + uname;
            da0.index = Gaming.selectedPlayerIndex; // 使用选中的玩家索引
         }

         return da0;
      }
      
      public static function getRandomRank() : int
      {
         tempTopRankIndex = (tempTopRankIndex + 1) % tempTopRankArr.length;
         return tempTopRankArr[tempTopRankIndex];
      }
   }
}

