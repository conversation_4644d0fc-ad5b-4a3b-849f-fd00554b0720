package dataAll._app.union.extra
{
   import com.common.data.Base64;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.StringCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class MemberExtra
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var stringCF:StringCF = new StringCF();
      
      public var playerName:String = "";
      
      public var militaryRank:String = "";
      
      public var lv:int = 0;
      
      public var vip:String = "";
      
      public var life:Number = 0;
      
      public var dps:Number = 0;
      
      public var money:Number = 0;
      
      public var conObj:Object = {};
      
      public var conDay:Number = 0;
      
      public var loginTime:String = "";
      
      public function MemberExtra()
      {
         super();
         this.mp = "";
         this.bt = "";
      }
      
      public static function toExtra(obj0:Object) : String
      {
         return Base64.encodeObject(obj0);
      }
      
      public static function toObj(str0:String) : Object
      {
         var obj0:Object = {};
         try
         {
            obj0 = Base64.decodeObject(str0);
         }
         catch(e:Error)
         {
         }
         return obj0;
      }
      
      public function get bt() : String
      {
         return this.stringCF.getAttribute("bt") as String;
      }
      
      public function set bt(str0:String) : void
      {
         this.stringCF.setAttribute("bt",str0);
      }
      
      public function get lt() : Number
      {
         return this.CF.getAttribute("lt");
      }
      
      public function set lt(v0:Number) : void
      {
         this.CF.setAttribute("lt",v0);
      }
      
      public function get mp() : String
      {
         return this.stringCF.getAttribute("mp") as String;
      }
      
      public function set mp(str0:String) : void
      {
         this.stringCF.setAttribute("mp",str0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function getVipNumber() : int
      {
         return int(this.vip.substr(3));
      }
      
      public function inBattleExtra(e0:MemberExtra) : void
      {
         this.bt = e0.bt;
         this.lt = e0.lt;
         this.mp = e0.mp;
      }
      
      public function getDayCon(nowTime0:StringDate) : int
      {
         if(nowTime0.reductionOneStr(this.loginTime) == 0)
         {
            return this.conDay;
         }
         return 0;
      }
   }
}

