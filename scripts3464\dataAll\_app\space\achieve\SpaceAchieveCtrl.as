package dataAll._app.space.achieve
{
   public class SpaceAchieveCtrl
   {
      
      private static const ARR:Array = [];
      
      private static const OBJ:Object = {};
      
      public function SpaceAchieveCtrl()
      {
         super();
      }
      
      public static function afterGiftInit() : void
      {
         newDefine("craftMaxLv","cLv1",5,"5级飞船","拥有[must]级或以上的飞船。","无双水晶*20");
         newDefine("craftMaxLv","cLv01",8,"8级飞船","拥有[must]级或以上的飞船。","万能球*20");
         newDefine("craftMaxLv","cLv2",10,"10级飞船","拥有[must]级或以上的飞船。","氩石*20");
         newDefine("craftMaxLv","cLv3",13,"13级飞船","拥有[must]级或以上的飞船。","无双水晶*30");
         newDefine("craftMaxLv","cLv4",15,"15级飞船","拥有[must]级或以上的飞船。","氩石*20");
         newDefine("craftMaxLv","cLv5",18,"18级飞船","拥有[must]级或以上的飞船。","无双水晶*40");
         newDefine("craftMaxLv","cLv6",20,"20级飞船","拥有[must]级或以上的飞船。","氩石*20");
         newDefine("craftMaxLv","cLv7",25,"25级飞船","拥有[must]级或以上的飞船。","无双水晶*30");
         newDefine("craftMaxLv","cLv8",30,"30级飞船","拥有[must]级或以上的飞船。","氩石*20");
         newDefine("craftMaxLv","cLv9",35,"35级飞船","拥有[must]级或以上的飞船。","战神之心*20");
         newDefine("craftMaxLv","cLv10",40,"40级飞船","拥有[must]级或以上的飞船。","氩岗岩*40");
         newDefine("mapWin","map0",4,"刷图x1","通关[extra]次以上的地图有[must]张。","无双水晶*15",1);
         newDefine("mapWin","map1",4,"刷图x2","通关[extra]次以上的地图有[must]张。","无双水晶*30",2);
         newDefine("mapWin","map2",4,"刷图x4","通关[extra]次以上的地图有[must]张。","氩石*20",4);
         newDefine("mapWin","map3",4,"刷图x6","通关[extra]次以上的地图有[must]张。","氩石*20",6);
         newDefine("mapWin","map4",4,"刷图x8","通关[extra]次以上的地图有[must]张。","氩石*20",8);
         newDefine("mapWin","map5",4,"刷图x10","通关[extra]次以上的地图有[must]张。","氩石*20",10);
         newDefine("mapWin","map6",4,"刷图x12","通关[extra]次以上的地图有[must]张。","氩石*20",12);
         newDefine("diffWinNum","diff1_4",4,"普通地图x4","通关[must]张地图的普通难度。","战神之心*20",1);
         newDefine("diffWinNum","diff2_2",2,"困难地图x2","通关[must]张地图的困难难度。","战神之心*25",2);
         newDefine("diffWinNum","diff2_4",4,"困难地图x4","通关[must]张地图的困难难度。","氩石*20",2);
         newDefine("diffWinNum","diff3_2",2,"超难地图x2","通关[must]张地图的超难难度。","战神之心*25",3);
         newDefine("diffWinNum","diff3_4",4,"超难地图x4","通关[must]张地图的超难难度。","氩石*20",3);
         newDefine("diffWinNum","diff4_4",4,"神难地图x4","通关[must]张地图的神难难度。","氩岗岩*40",4);
         newDefine("coveDiff","cove2",1,"困难·黄金隼","通关1次“木卫三洞穴”的困难难度。","氩石*20",2);
         newDefine("coveDiff","cove3",1,"超难·黄金隼","通关1次“木卫三洞穴”的超难难度。","氩石*20",3);
         newDefine("screenKill","screen1",900,"全域光波V1","用全域光波击毁[must]个敌人或陨石。","无双水晶*30");
         newDefine("screenKill","screen2",1800,"全域光波V2","用全域光波击毁[must]个敌人或陨石。","氩石*20");
         newDefine("screenKillB","screen3",4,"全域光波V3","用全域光波击毁[must]个首领。","战神之心*20");
         newDefine("shieldKill","shield1",300,"无敌护盾V1","用无敌护盾击毁[must]个敌人或陨石。","无双水晶*25");
         newDefine("playerSkillNum","playerSkill4",4,"14级技能x4","拥有[must]个14级的人物主动技能。","氩石*20");
      }
      
      private static function newDefine(fun0:String, name0:String, must0:Number, cn0:String, info0:String, giftCn0:String, extra0:Number = 0) : SpaceAchieveDefine
      {
         var d0:SpaceAchieveDefine = null;
         d0 = getDefine(name0);
         if(d0 == null)
         {
            d0 = new SpaceAchieveDefine();
            d0.name = name0;
            d0.cnName = cn0;
            d0.fun = fun0;
            d0.must = must0;
            d0.extra = extra0;
            d0.info = info0;
            d0.gift.inData_byCnStr(giftCn0);
            d0.fleshInfo();
            ARR.push(d0);
            OBJ[name0] = d0;
            return d0;
         }
         INIT.showErrorMust("SpaceAchieveDefine定义重复：" + name0);
         return null;
      }
      
      public static function getDefine(name0:String) : SpaceAchieveDefine
      {
         return OBJ[name0];
      }
      
      public static function getUIArr() : Array
      {
         return ARR;
      }
   }
}

