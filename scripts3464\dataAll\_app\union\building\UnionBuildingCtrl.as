package dataAll._app.union.building
{
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class UnionBuildingCtrl
   {
      
      public function UnionBuildingCtrl()
      {
         super();
      }
      
      public static function getUplevelMust(da0:UnionBuildingData) : MustDefine
      {
         var lv0:int = da0.save.lv;
         var d0:MustDefine = new MustDefine();
         var num0:int = Gaming.defineGroup.union.building.property.getPropertyValue("upgradeMust",lv0);
         d0.inThingsDataByArr(["militarySupplies;" + num0]);
         return d0;
      }
      
      public static function getMaxLv() : int
      {
         return 20;
      }
      
      public static function getNowMaxLv() : int
      {
         return 20;
      }
      
      public static function getDayEatNum() : int
      {
         return 2;
      }
      
      public static function getCookingObjByProArr(arr0:Array, lv0:int) : Object
      {
         var name0:String = null;
         var v0:Number = NaN;
         var proD0:PropertyArrayDefine = null;
         var obj0:Object = {};
         for each(name0 in arr0)
         {
            v0 = getCookingValueByName(name0,lv0);
            proD0 = Gaming.defineGroup.getPropertyArrayDefine(name0);
            v0 = proD0.fixedNumber(v0);
            obj0[name0] = v0;
         }
         return obj0;
      }
      
      private static function getCookingValueByName(name0:String, lv0:int) : Number
      {
         return Gaming.defineGroup.union.building.property.getPropertyValue(name0,lv0);
      }
   }
}

