package dataAll.things.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll.image.ImageUrlDefine;
   import gameAll.level.PlayMode;
   
   public class ThingsEffectDefine
   {
      
      public static const NO_LIMIT:int = 9999;
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var type:String = "";
      
      public var value:Number = 0;
      
      public var sTaskNoB:Boolean = false;
      
      public var useInLevelB:Boolean = false;
      
      public var useSurplusBagB:Boolean = false;
      
      private var _levelUseLimitNum:String = "";
      
      private var _arenaLevelUseLimitNum:String = "";
      
      public var moreLimitB:Boolean = false;
      
      public var effectLabel:String = "";
      
      public var effectSound:String = "";
      
      private var effectImg:ImageUrlDefine = new ImageUrlDefine();
      
      public var skillArr:Array = [];
      
      public var autoUseCondition:String = "";
      
      public var endlessAutoUseOneB:Boolean = false;
      
      public function ThingsEffectDefine()
      {
         super();
         this.minLv = 0;
         this.dayUseLimit = NO_LIMIT;
         this.levelUseLimitNum = NO_LIMIT;
         this.arenaLevelUseLimitNum = NO_LIMIT;
      }
      
      public static function haveLimitPan(limit0:int) : Boolean
      {
         return limit0 < NO_LIMIT;
      }
      
      public function get dayUseLimit() : Number
      {
         return this.CF.getAttribute("dayUseLimit");
      }
      
      public function set dayUseLimit(v0:Number) : void
      {
         this.CF.setAttribute("dayUseLimit",v0);
      }
      
      public function get minLv() : Number
      {
         return this.CF.getAttribute("minLv");
      }
      
      public function set minLv(v0:Number) : void
      {
         this.CF.setAttribute("minLv",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.fleshEffectImg();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.fleshEffectImg();
      }
      
      private function fleshEffectImg() : void
      {
         this.effectImg.con = "add";
         this.effectImg.url = this.effectLabel;
         this.effectImg.soundUrl = this.effectSound;
      }
      
      public function getLevelUseLimitNum(model0:String) : Number
      {
         var v0:Number = this.levelUseLimitNum;
         var v1:Number = this.arenaLevelUseLimitNum;
         if(model0 == PlayMode.ARENA)
         {
            if(haveLimitPan(v1))
            {
               v0 = v1;
            }
         }
         return v0;
      }
      
      public function canAddVipB(model0:String) : Boolean
      {
         if(model0 == PlayMode.ARENA)
         {
            return this.arenaLevelUseLimitNum == NO_LIMIT;
         }
         return true;
      }
      
      public function set levelUseLimitNum(v0:Number) : void
      {
         this._levelUseLimitNum = Sounto64.encode(String(v0));
      }
      
      public function get levelUseLimitNum() : Number
      {
         return Number(Sounto64.decode(this._levelUseLimitNum));
      }
      
      public function set arenaLevelUseLimitNum(v0:Number) : void
      {
         this._arenaLevelUseLimitNum = Sounto64.encode(String(v0));
      }
      
      public function get arenaLevelUseLimitNum() : Number
      {
         return Number(Sounto64.decode(this._arenaLevelUseLimitNum));
      }
      
      public function getEffectDefine() : ImageUrlDefine
      {
         return this.effectImg;
      }
   }
}

