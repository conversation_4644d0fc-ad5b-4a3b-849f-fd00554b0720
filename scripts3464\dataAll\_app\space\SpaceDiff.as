package dataAll._app.space
{
   import com.sounto.utils.ArrayMethod;
   
   public class SpaceDiff
   {
      
      public static var lifeDiff:Array = [1,1.6,1.8,2.5,3.3];
      
      public static var dpsDiff:Array = [1,1.6,1.8,2.5,3.2];
      
      public static var expMul:Array = [1,1.4,1.9,2.5,3.3];
      
      private static var moveMul:Array = [1,1.15,1.5,1.5,1.5];
      
      private static var moveMul2:Array = [1,1.1,1.2,1.3,1.3];
      
      private static var diff2SkillO:Object = null;
      
      public function SpaceDiff()
      {
         super();
      }
      
      private static function initSkillData() : void
      {
         var ob0:Object = {};
         ob0["EarthSky"] = ["rotateLighting"];
         ob0["SolarInside"] = ["targetBlade"];
         ob0["CeresSouth"] = [];
         ob0["Ganymede"] = [];
         diff2SkillO = ob0;
      }
      
      public static function getMapExpMax(name0:String, diff0:int) : Number
      {
         var maxArr0:Array = [400000,500000,600000,700000,800000];
         return ArrayMethod.getElementLimit(maxArr0,diff0);
      }
      
      public static function getDiffMax() : int
      {
         return 4;
      }
      
      public static function getBossSkillArrDiff(mapName0:String, diff0:int) : Array
      {
         var arr2:Array = null;
         if(diff2SkillO == null)
         {
            initSkillData();
         }
         var arr0:Array = [];
         if(diff0 >= 2)
         {
            arr2 = diff2SkillO[mapName0];
            if(Boolean(arr2))
            {
               arr0 = arr0.concat(arr2);
            }
         }
         return arr0;
      }
      
      public static function getMoveMul(diff0:int, mapName0:String) : Number
      {
         if(mapName0 == "Ganymede")
         {
            return ArrayMethod.getElementLimit(moveMul2,diff0);
         }
         return ArrayMethod.getElementLimit(moveMul,diff0);
      }
   }
}

