package gameAll.hero.image
{
   import com.common.data.Line2;
   import com.sounto.image.DisplayMotionDefine;
   import com.sounto.math.GeoMethod;
   import com.sounto.math.Maths;
   import com.sounto.math.TwoPoint;
   import com.sounto.motion.tween.CoorTweenMotion;
   import com.sounto.motion.tween.LinesTweenMotion;
   import com.sounto.utils.DisplayMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.GunImage;
   import flash.display.DisplayObjectContainer;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.media.SoundTransform;
   import gameAll.body.image.movieParser.PartSprite;
   import gameAll.body.sound.NormalSoundCtrl;
   
   public class HeroShootImage extends HeroPartImage
   {
      
      protected var body_mot:SpriteImageData = new SpriteImageData();
      
      protected var gun_mot:SpriteImageData = new SpriteImageData();
      
      protected var gun_mot2:SpriteImageData = new SpriteImageData();
      
      private var gunDefine:ArmsData = null;
      
      public var gun_right_len:Number = 0.99;
      
      public var gunNum:int = 2;
      
      public var gunShootPoint:Point = new Point();
      
      public var gunUpValue:int = 0;
      
      public var gunHandShootGap:int = 0;
      
      public var gunFocusAngleRange:Number = 0;
      
      public var nowGunIndex:int = 0;
      
      private var inMouseX:Number = 0;
      
      private var inMouseY:Number = 0;
      
      private var shoot_lines:Line2 = new Line2();
      
      public var head_point:Point = new Point();
      
      public var head_angle:Number = 0;
      
      protected var raTween:LinesTweenMotion = new LinesTweenMotion();
      
      protected var shootRaTween:LinesTweenMotion = new LinesTweenMotion();
      
      protected var shootRaTween2:LinesTweenMotion = new LinesTweenMotion();
      
      protected var shootLenTween:LinesTweenMotion = new LinesTweenMotion();
      
      public var mx:int = 0;
      
      public var my:int = 0;
      
      public var mTween:CoorTweenMotion = new CoorTweenMotion();
      
      public var swapA:HeroSwapGunAnimotion = new HeroSwapGunAnimotion();
      
      public var launchA:HeroLaunchGunAnimotion = new HeroLaunchGunAnimotion();
      
      private var swapRaTween:LinesTweenMotion = new LinesTweenMotion();
      
      public var outGunScale:Number = 0.5;
      
      public var rideShootB:Boolean = false;
      
      public function HeroShootImage()
      {
         super();
         this.raTween.max = 5;
         this.shootRaTween.max = 15;
         this.shootRaTween.max = this.shootRaTween.max;
         this.shootLenTween.max = 8;
         this.swapRaTween.max = 6;
         this.mTween.max = 100;
      }
      
      public function inSoundCtrl(ctrl0:NormalSoundCtrl) : void
      {
         this.swapA.soundCtrl = ctrl0;
         this.launchA.soundCtrl = ctrl0;
      }
      
      public function setGun(gun1:GunImage, gun2:GunImage = null) : void
      {
         this.clearGun();
         gun_img = gun1;
         gun_img2 = gun2;
         this.gunShootPoint = gun_img.shoot_p.toPoint();
         this.gunShootPoint.x *= this.outGunScale;
         this.gunShootPoint.y *= this.outGunScale;
         addChild(gun_img);
         gun_img.x = this.gun_mot.x;
         gun_img.y = this.gun_mot.y;
         if(Boolean(gun2))
         {
            this.gunNum = 2;
            addChild(gun_img2);
            gun_img2.x = this.gun_mot2.x;
            gun_img2.y = this.gun_mot2.y;
         }
         else
         {
            this.gunNum = 1;
         }
         this.setToGunNum(this.gunNum);
         this.inGunXY();
      }
      
      public function setGunDefine(d0:ArmsData) : void
      {
         this.gun_right_len = d0.armsArmMul;
         this.gunNum = d0.gunNum;
         this.gunUpValue = d0.upValue;
         this.gunHandShootGap = d0.handShootGap;
         this.gunFocusAngleRange = d0.focusAngleRange / 180 * Math.PI;
         this.gunDefine = d0;
         this.launchA.gunDefine = d0;
      }
      
      public function setMXY(x0:Number, y0:Number) : void
      {
         this.mx = x0;
         this.my = y0;
      }
      
      public function clearGun() : void
      {
         var con:DisplayObjectContainer = this;
         if(Boolean(gun_img))
         {
            this.gunBulletGoBack();
            con.removeChild(gun_img);
            gun_img = null;
         }
         if(Boolean(gun_img2))
         {
            con.removeChild(gun_img2);
            gun_img2 = null;
         }
         this.swapA.stop();
         this.launchA.init();
      }
      
      public function gunBulletOut() : void
      {
         var bullet_point0:Point = null;
         if(gun_img.bullet.parent != bulletCon)
         {
            bulletCon.addChild(gun_img.bullet);
            bullet_point0 = this.getTrueP(gun_img.p_obj["bullet"].x,gun_img.p_obj["bullet"].y,this.gun_mot);
            bulletCon.x = bullet_point0.x;
            bulletCon.y = bullet_point0.y;
            gun_img.bullet.x = -gun_img.p_obj["bullet_hand"].x + gun_img.p_obj["bullet"].x;
            gun_img.bullet.y = -gun_img.p_obj["bullet_hand"].y + gun_img.p_obj["bullet"].y;
            this.swapA.bux = bullet_point0.x;
            this.swapA.buy = bullet_point0.y;
         }
      }
      
      public function gunBulletGoBack() : void
      {
         if(gun_img.bullet.parent == bulletCon)
         {
            gun_img.addChildAt(gun_img.bullet,0);
            gun_img.bullet.x = gun_img.p_obj["bullet"].x;
            gun_img.bullet.y = gun_img.p_obj["bullet"].y;
         }
      }
      
      private function setToGunNum(num0:int) : void
      {
         var con:DisplayObjectContainer = null;
         con = this;
         this.gunNum = num0;
         con.setChildIndex(gun_img,con.getChildIndex(hand_right));
         if(this.gunNum == 1)
         {
            if(this.gunDefine.leftHandImg == 0)
            {
               con.setChildIndex(hand_left,con.numChildren - 1);
            }
            else if(this.gunDefine.leftHandImg == 1)
            {
               con.setChildIndex(hand_left,con.getChildIndex(weapon_img) + 1);
            }
         }
         else
         {
            con.setChildIndex(hand_left,con.getChildIndex(weapon_img) + 1);
            gun_img2.visible = true;
            con.setChildIndex(gun_img2,con.getChildIndex(hand_left));
         }
         if(Boolean(aircraftTop))
         {
            if(aircraftTop.parent == con)
            {
               con.setChildIndex(aircraftTop,con.numChildren - 1);
            }
         }
         this.fleshHandLeftLabel();
      }
      
      override public function getShootLines() : Line2
      {
         var m_mot:SpriteImageData = this.gun_mot;
         var _cy:int = 0;
         if(this.gunNum >= 2)
         {
            if(this.nowGunIndex == 1)
            {
               m_mot = this.gun_mot2;
               _cy = 5;
            }
         }
         this.shoot_lines.x = m_mot.x;
         this.shoot_lines.y = m_mot.y;
         this.shoot_lines.ra = Math.atan2(this.inMouseY + _cy - this.shoot_lines.y,this.inMouseX - this.shoot_lines.x) + (this.raTween.now + this.swapRaTween.now) / 360 * Math.PI;
         this.shoot_lines.len = Maths.Long(this.inMouseX - this.shoot_lines.x,this.inMouseY + _cy - this.shoot_lines.y);
         var p00:Point = this.getRelativeP(this.gunShootPoint.x,this.gunShootPoint.y,m_mot);
         this.shoot_lines.x += p00.x;
         this.shoot_lines.y += p00.y;
         this.shoot_lines.x *= scaleBuff;
         this.shoot_lines.y *= scaleBuffY;
         var outP0:Point = DisplayMethod.getTempTruePoint(this.shoot_lines.x,this.shoot_lines.y,x,y,getRa(),-getDirect());
         this.shoot_lines.x = outP0.x;
         this.shoot_lines.y = outP0.y;
         if(this.scaleX < 0)
         {
            this.shoot_lines.ra = Maths.flipRa_Y(this.shoot_lines.ra);
         }
         this.shoot_lines.ra += getRa();
         return this.shoot_lines;
      }
      
      public function setTweenShootPoint(mx0:Number, my0:Number, tweenMul0:Number = 1) : void
      {
         if(tweenMul0 > 0)
         {
            this.mTween.maxPer = tweenMul0;
            this.mTween.tweenPer = tweenMul0;
         }
         this.mTween.inData(mx0,my0);
         this.mx = this.mTween.x;
         this.my = this.mTween.y;
      }
      
      public function setShootPoint(mx0:Number, my0:Number) : void
      {
         this.mx = mx0;
         this.my = my0;
         this.mTween.init(mx0,my0);
      }
      
      public function setFocoMul(v0:Number) : void
      {
         if(gun_img is GunImage)
         {
            gun_img.setFocoMul(v0);
         }
      }
      
      public function playShoot(mul0:Number) : void
      {
         this.shootLenTween.now = -this.gunDefine.shootRecoil;
         var snow0:Number = this.gunDefine.shootShakeAngle * (1 - 0.5 * Math.random()) * mul0;
         if(this.gunNum == 1)
         {
            this.shootRaTween.now = snow0;
            gun_img.gotoAndPlay("shoot");
            this.nowGunIndex = 0;
            this.tryPlayLaunch();
         }
         else
         {
            this.nowGunIndex = (this.nowGunIndex + 1) % 2;
            if(this.nowGunIndex == 0)
            {
               this.shootRaTween.now = snow0;
               gun_img2.gotoAndPlay("shoot");
            }
            else
            {
               this.shootRaTween2.now = snow0;
               gun_img.gotoAndPlay("shoot");
            }
         }
      }
      
      public function tryPlayLaunch() : Boolean
      {
         var launchLabel0:String = null;
         if(this.gunNum == 1)
         {
            launchLabel0 = this.gunDefine.getLaunchLabel();
            if(launchLabel0 != "")
            {
               this.launchA.start(launchLabel0);
               return true;
            }
         }
         return false;
      }
      
      public function playReloadBullet(time0:Number) : void
      {
         if(this.gunNum == 1)
         {
            this.swapA.start(time0);
         }
         else if(this.gunNum == 2)
         {
            this.swapA.soundCtrl.playSoundFull("sound/swapEnd");
         }
      }
      
      public function playHurt(an0:Number = 0) : void
      {
         this.raTween.now = an0;
      }
      
      public function setSoundVolume(value0:Number = 1) : void
      {
         var st0:SoundTransform = new SoundTransform(value0);
         this.setSoundTransform(st0);
      }
      
      public function setSoundTransform(st0:SoundTransform, volumeMul:Number = 1) : void
      {
         st0.volume *= volumeMul;
         if(Boolean(gun_img))
         {
            gun_img.soundTransform = st0;
         }
         if(Boolean(gun_img2))
         {
            gun_img2.soundTransform = st0;
         }
      }
      
      public function getTweenRa() : Number
      {
         return this.raTween.now + this.shootRaTween.now + this.swapRaTween.now;
      }
      
      public function getTweenRa2() : Number
      {
         return this.raTween.now + this.shootRaTween2.now;
      }
      
      public function inGunXY(tweenCountB0:Boolean = true) : void
      {
         var left_p1:Point = null;
         var swap_ra_range0:Number = NaN;
         var angle_a0:Number = NaN;
         var cos00:Number = NaN;
         var gun_left0:Number = NaN;
         var gun_ra0:Number = NaN;
         var gun_x0:Number = NaN;
         var gun_y0:Number = NaN;
         var bullet_hand_p:DisplayMotionDefine = null;
         var bullet_hand_p2:Point = null;
         var sin00:Number = NaN;
         var gun_twop2:TwoPoint = null;
         var gun_left2:Number = NaN;
         var gun_ra2:Number = NaN;
         var gun_x2:Number = NaN;
         var gun_y2:Number = NaN;
         var arm_left_1_scaleX:Number = NaN;
         var x0:Number = this.mx;
         var y0:Number = this.my;
         this.fleshGunShow();
         this.fleshHandLeftLabel();
         if(nowLabelType == "die" || nowLabelType == "fill" || nowLabelType == "Attack" || nowLabelType == "stru" || nowLabelType == "Ride" && !this.rideShootB)
         {
            this.followAnimation();
            return;
         }
         var PI:Number = Math.PI;
         var x1:Number = 0;
         var y1:Number = h_py + body.y;
         var p0:Point = DisplayMethod.getTempInsidePoint(x0,y0,this.x,this.y,getRa());
         x0 = p0.x;
         y0 = p0.y;
         if(x0 > 0)
         {
            flipToRight();
            x0 = -x0;
         }
         else
         {
            flipToLeft();
         }
         x0 /= scaleBuff;
         y0 /= scaleBuffY;
         var a:Number = Math.atan2(y0 - y1,x0 - x1);
         var len:Number = Maths.Long(y0 - y1,x0 - x1);
         var right_hand_max:Number = right_as.bone_2.length + right_as.bone_4.length;
         var left_hand_max:Number = left_as.bone_2.length + left_as.bone_4.length;
         if(this.gunFocusAngleRange > 0)
         {
            if(a < 0)
            {
               a = (a + PI) * this.gunFocusAngleRange * 2 / PI - PI;
            }
            else if(a > 0)
            {
               a = PI - (PI - a) * this.gunFocusAngleRange * 2 / PI;
            }
         }
         if(len < right_hand_max * 1.5 || this.gunFocusAngleRange > 0)
         {
            x0 = x1 + right_hand_max * 1.5 * Math.cos(a);
            y0 = y1 + right_hand_max * 1.5 * Math.sin(a);
         }
         this.inMouseX = x0;
         this.inMouseY = y0;
         var _a0:Number = Maths.ZhunJ(-a - PI);
         if(tweenCountB0)
         {
            this.raTween.inData(0);
            this.shootRaTween.inData(0);
            this.shootRaTween2.inData(0);
            this.shootLenTween.inData(0);
            if(this.swapA.state == "" || this.swapA.state == "back")
            {
               this.swapRaTween.inData(0);
            }
            else
            {
               swap_ra_range0 = -45;
               angle_a0 = _a0 * 180 / PI;
               if(angle_a0 > 45)
               {
                  swap_ra_range0 = -45 + (angle_a0 - 45) / 15 * 45;
                  if(swap_ra_range0 > 0)
                  {
                     swap_ra_range0 = 0;
                  }
               }
               this.swapRaTween.inData(swap_ra_range0);
            }
         }
         var body_tween_ra:Number = this.raTween.now / 180 * PI;
         var head_tween_ra:Number = (this.raTween.now + this.swapRaTween.now / 2) / 180 * PI;
         var body_d:DisplayMotionDefine = mc.getPartMotion("body");
         var body_ra0:Number = -body_ra_range / (PI / 2) * _a0 + body_tween_ra;
         this.body_mot.ra = body_ra0 + body_d.rotation / 180 * PI;
         this.body_mot.x = body_d.x;
         this.body_mot.y = body_d.y;
         body.x = this.body_mot.x;
         body.y = this.body_mot.y;
         body.rotation = this.body_mot.ra * 180 / PI;
         var head_ra0:Number = -_a0;
         if(head_ra0 - body_ra0 > head_ra_range)
         {
            head_ra0 = head_ra_range + body_ra0;
         }
         if(head_ra0 - body_ra0 < -head_ra_range)
         {
            head_ra0 = -head_ra_range + body_ra0;
         }
         head_ra0 += head_tween_ra;
         if(head.scaleZ != 1)
         {
            head.scaleZ = 1;
         }
         if(head.rotationX != 0)
         {
            head.rotationX = 0;
         }
         if(head.rotationY != 0)
         {
            head.rotationY = 0;
         }
         head.rotation = head_ra0 * 180 / PI;
         var head_p0:Point = this.getTrueP(h_px,h_py,this.body_mot);
         head.x = head_p0.x;
         head.y = head_p0.y;
         this.head_angle = head_ra0;
         this.head_point.x = head_p0.x;
         this.head_point.y = head_p0.y;
         var g_r_px:Number = 0;
         var g_r_py:Number = 0;
         var g_l_px:Number = 0;
         var g_l_py:Number = 0;
         g_r_px = gun_img.right_p.x;
         g_r_py = gun_img.right_p.y;
         if(Boolean(gun_img2))
         {
            gun_img2.right_p.visible = false;
            gun_img2.left_p.visible = false;
         }
         var handShootGap0:Number = this.gunHandShootGap == 0 ? Math.abs(g_r_py) : this.gunHandShootGap;
         var right_p0:Point = this.getTrueP(r_px,r_py,this.body_mot);
         var left_p0:Point = this.getTrueP(l_px,l_py,this.body_mot);
         var upValue0:Number = 0;
         if(this.gunUpValue != 0)
         {
            cos00 = Math.cos(Math.PI - Math.abs(a));
            upValue0 = cos00 * cos00 * this.gunUpValue;
         }
         var gun_twop0:TwoPoint = GeoMethod.getAll_LineCircle(x0,y0,right_p0.x,right_p0.y + upValue0,handShootGap0);
         if(gun_twop0.pNum == 1)
         {
            gun_left0 = this.gun_right_len * right_hand_max;
            if(this.nowGunIndex == 0)
            {
               gun_left0 += this.shootLenTween.now;
            }
            gun_ra0 = gun_twop0.ra1;
            if(this.nowGunIndex == 0)
            {
               gun_ra0 += this.getTweenRa() / 180 * PI / 2;
            }
            gun_x0 = gun_twop0.x1 + gun_left0 * Math.cos(gun_ra0 - PI);
            gun_y0 = gun_twop0.y1 + gun_left0 * Math.sin(gun_ra0 - PI);
            this.gun_mot.x = gun_x0;
            this.gun_mot.y = gun_y0;
            this.gun_mot.ra = gun_ra0;
            gun_img.rotation = gun_ra0 * 180 / PI;
            gun_img.x = gun_x0;
            gun_img.y = gun_y0;
            if(this.outGunScale != gun_img.scaleX)
            {
               gun_img.scaleX = this.outGunScale;
               gun_img.scaleY = this.outGunScale;
            }
         }
         var right_p1:Point = this.getTrueP(g_r_px,g_r_py,this.gun_mot);
         if(this.gunNum == 1)
         {
            g_l_px = gun_img.left_p.x;
            g_l_py = gun_img.left_p.y;
            left_p1 = this.getTrueP(g_l_px,g_l_py,this.gun_mot);
            if(this.swapA.state != "")
            {
               bullet_hand_p = gun_img.bulletHand_p;
               bullet_hand_p2 = this.getTrueP(bullet_hand_p.x,bullet_hand_p.y,this.gun_mot);
               this.swapA.inData(left_p0.x,left_p0.y,left_p1.x,left_p1.y,this.body_mot.x - 8,this.body_mot.y + 5,bullet_hand_p2.x,bullet_hand_p2.y,gun_img.p_obj["left_hand"].rotation);
               this.swapA.count(tweenCountB0);
               left_p1 = new Point(this.swapA.x,this.swapA.y);
               hand_left.rotation = this.swapA.ra;
               if(this.swapA.bulletPosition == 0)
               {
                  this.gunBulletGoBack();
               }
               else
               {
                  if(this.swapA.bulletPosition == 1)
                  {
                     this.gunBulletOut();
                  }
                  bulletCon.x = this.swapA.bux;
                  bulletCon.y = this.swapA.buy;
                  bulletCon.rotation = this.swapA.bura;
                  bulletCon.visible = this.swapA.buvis;
               }
            }
            else
            {
               hand_left.visible = true;
               hand_left.rotation = gun_img.left_p.rotation + gun_img.rotation;
               this.launchA.count(tweenCountB0);
               if(this.launchA.state != "no")
               {
                  cos00 = Math.cos(gun_ra0);
                  sin00 = Math.sin(gun_ra0);
                  left_p1.x += -cos00 * this.launchA.leftHand_x - sin00 * this.launchA.leftHand_y;
                  left_p1.y += -sin00 * this.launchA.leftHand_x + cos00 * this.launchA.leftHand_y;
                  hand_left.visible = this.launchA.leftHand_visible;
               }
            }
         }
         else
         {
            g_l_px = gun_img2.right_p.x;
            g_l_py = gun_img2.right_p.y;
            gun_twop2 = GeoMethod.getAll_LineCircle(x0,y0 + 5,left_p0.x,left_p0.y + upValue0,handShootGap0);
            if(gun_twop0.pNum == 1)
            {
               gun_left2 = this.gun_right_len * left_hand_max;
               if(this.nowGunIndex == 1)
               {
                  gun_left2 += this.shootLenTween.now;
               }
               gun_ra2 = gun_twop2.ra1;
               if(this.nowGunIndex == 1)
               {
                  gun_ra2 += this.getTweenRa2() / 180 * PI / 2;
               }
               gun_x2 = gun_twop2.x1 + gun_left2 * Math.cos(gun_ra2 - PI);
               gun_y2 = gun_twop2.y1 + gun_left2 * Math.sin(gun_ra2 - PI);
               this.gun_mot2.x = gun_x2;
               this.gun_mot2.y = gun_y2;
               this.gun_mot2.ra = gun_ra2;
               gun_img2.rotation = gun_ra2 * 180 / PI;
               gun_img2.x = gun_x2;
               gun_img2.y = gun_y2;
               if(this.outGunScale != gun_img2.scaleX)
               {
                  gun_img2.scaleX = this.outGunScale;
                  gun_img2.scaleY = this.outGunScale;
               }
            }
            left_p1 = this.getTrueP(g_l_px,g_l_py,this.gun_mot2);
            hand_left.rotation = gun_img2.right_p.rotation + gun_img2.rotation;
         }
         hand_right.x = right_p1.x;
         hand_right.y = right_p1.y;
         hand_right.rotation = gun_img.right_p.rotation + gun_img.rotation;
         hand_left.x = left_p1.x;
         hand_left.y = left_p1.y;
         this.inArmSkeletonData(right_p0.x,right_p0.y,right_p1.x,right_p1.y,arm_right_0,arm_right_1,right_as);
         this.inArmSkeletonData(left_p0.x,left_p0.y,left_p1.x,left_p1.y,arm_left_0,arm_left_1,left_as);
         var left_len2:Number = (left_p1.x - left_as.bone_4.x) * (left_p1.x - left_as.bone_4.x) + (left_p1.y - left_as.bone_4.y) * (left_p1.y - left_as.bone_4.y);
         var left_len3:Number = left_as.bone_4.length * left_as.bone_4.length;
         if(left_len2 > left_len3)
         {
            arm_left_1_scaleX = left_len2 / left_len3;
            if(arm_left_1_scaleX > 1.5)
            {
               arm_left_1_scaleX = 1.5;
            }
            arm_left_1.scaleX = arm_left_1_scaleX;
         }
         else
         {
            arm_left_1.scaleX = 1;
         }
      }
      
      public function followAnimation() : void
      {
         var PI:Number = Math.PI;
         var body_d:DisplayMotionDefine = mc.getPartMotion("body");
         this.body_mot.ra = body_d.rotation / 180 * PI;
         this.body_mot.x = body_d.x;
         this.body_mot.y = body_d.y;
         body.x = this.body_mot.x;
         body.y = this.body_mot.y;
         body.rotation = this.body_mot.ra * 180 / PI;
         var head_ra0:Number = head_ra_range + this.body_mot.ra;
         head.rotation = head_ra0 * 180 / PI;
         var head_p0:Point = this.getTrueP(h_px,h_py,this.body_mot);
         head.x = head_p0.x;
         head.y = head_p0.y;
         this.head_angle = head_ra0;
         this.head_point.x = head_p0.x;
         this.head_point.y = head_p0.y;
         var right_p0:Point = this.getTrueP(r_px,r_py,this.body_mot);
         var left_p0:Point = this.getTrueP(l_px,l_py,this.body_mot);
         fleshPart_byNowMc();
         arm_left_1.scaleX = 1;
      }
      
      private function fleshHandLeftLabel() : void
      {
         if(isWeaponImgB)
         {
            hand_left.showLabel("take");
         }
         else
         {
            hand_left.showLabel(this.gunNum == 1 ? "hold" : "take");
         }
      }
      
      private function fleshGunShow() : void
      {
         if(getGunAndWeaponVisible())
         {
            if(gun_img is Sprite)
            {
               gun_img.visible = !isWeaponImgB;
            }
            if(gun_img2 is Sprite)
            {
               gun_img2.visible = gun_img.visible;
            }
         }
      }
      
      protected function inArmSkeletonData(x1:Number, y1:Number, x2:Number, y2:Number, arm1:PartSprite, arm2:PartSprite, as0:ArmSkeleton) : void
      {
         as0.inData(x1,y1,x2,y2);
         arm1.x = as0.bone_2.x;
         arm1.y = as0.bone_2.y;
         arm1.rotation = as0.bone_2.rotation;
         arm2.x = as0.bone_4.x;
         arm2.y = as0.bone_4.y;
         arm2.rotation = as0.bone_4.rotation;
      }
      
      protected function getTrueP(x0:Number, y0:Number, con:SpriteImageData) : Point
      {
         var l0:Number = Math.sqrt(x0 * x0 + y0 * y0);
         var ra0:Number = Math.atan2(y0,x0) + con.ra;
         return new Point(l0 * Math.cos(ra0) + con.x,l0 * Math.sin(ra0) + con.y);
      }
      
      protected function getRelativeP(x0:Number, y0:Number, con:SpriteImageData) : Point
      {
         var l0:Number = Math.sqrt(x0 * x0 + y0 * y0);
         var ra0:Number = Math.atan2(y0,x0) + con.ra;
         return new Point(l0 * Math.cos(ra0),l0 * Math.sin(ra0));
      }
      
      public function setIndexList(arr0:Array) : void
      {
      }
      
      override public function FTimer(timeStopB0:Boolean = false) : void
      {
         this.inGunXY();
         super.FTimer(timeStopB0);
      }
   }
}

