package dataAll.arms.creator
{
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.*;
   import dataAll.arms.define.*;
   import dataAll.arms.save.ArmsSave;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.bullet.BulletDefine;
   import dataAll.drop.define.ArmsColorDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class ArmsDataCreator
   {
      
      public function ArmsDataCreator()
      {
         super();
         ArmsUpgradeCtrl.init();
         ArmsStrengthenCtrl.init();
      }
      
      public static function getDps(d0:ArmsDefine, hurtRatio0:Number = -1) : Number
      {
         if(hurtRatio0 <= -1)
         {
            hurtRatio0 = d0.hurtRatio;
         }
         var l_dps0:Number = hurtRatio0 * d0.bulletNum * d0.shootNum / d0.attackGap * d0.getPrecision();
         var r_mul0:Number = 1 - d0.reloadGap / (d0.attackGap * d0.capacity + d0.reloadGap);
         return r_mul0 * l_dps0;
      }
      
      public static function countDps(hurtRatio:Number, bulletNum:int, shootNum:int, attackGap:Number, reloadGap:Number, capacity:Number, shakeAngle:Number, shootAngle:Number, bulletWidth:Number, bulletSpeed:Number, hitType:String) : Number
      {
         var shootRange:Number = hitType == BulletDefine.LONG_LINE ? bulletWidth : bulletSpeed * 30 * 0.7 + bulletWidth;
         var s0:Number = 1 - shakeAngle / 30;
         if(s0 < 0.4)
         {
            s0 = 0.4;
         }
         var a0:Number = 1 - shootAngle / 30;
         if(a0 < 0.3)
         {
            a0 = 0.3;
         }
         var l0:Number = (shootRange + 500) / 1100;
         if(l0 > 1)
         {
            l0 = 1;
         }
         var precision:Number = s0 * a0 * l0;
         var l_dps0:Number = hurtRatio * bulletNum * shootNum / attackGap * precision;
         var r_mul0:Number = 1 - reloadGap / (attackGap * capacity + reloadGap);
         return r_mul0 * l_dps0;
      }
      
      public static function getHurt(d0:ArmsDefine, dps0:Number) : Number
      {
         var xx0:* = undefined;
         var r_mul0:Number = 1 - d0.reloadGap / (d0.attackGap * d0.capacity + d0.reloadGap);
         var hurt0:Number = dps0 / r_mul0 / (d0.bulletNum * d0.shootNum / d0.attackGap * d0.getPrecision());
         if(isNaN(hurt0) || hurt0 > 999999)
         {
            xx0 = 0;
         }
         return hurt0;
      }
      
      public static function countHurt(:*, attackGap:Number, capacity:Number, reloadGap:Number, bulletNum:Number, shootNum:Number) : void
      {
      }
      
      private static function zuobiPan(c0:ArmsColorDefine, unitType0:String, dropName0:String) : void
      {
      }
      
      public static function dealAllArms() : Array
      {
         var da0:ArmsData = null;
         var bb0:Boolean = false;
         var arr0:Array = Gaming.PG.da.getArmsDataArr(true,true,true,true);
         var cnArr0:Array = [];
         for each(da0 in arr0)
         {
            bb0 = dealBaseHurt(da0);
            if(bb0)
            {
               cnArr0.push(da0.def.cnName);
            }
         }
         return cnArr0;
      }
      
      private static function dealBaseHurt(da0:ArmsData) : Boolean
      {
         var name0:String = null;
         var s0:ArmsSave = null;
         var newS0:ArmsSave = null;
         var c0:Number = NaN;
         if(EquipColor.moreBlackB(da0.save.color))
         {
            name0 = da0.def.name;
            if(name0 != "christmasGun")
            {
               s0 = da0.save;
               newS0 = Gaming.defineGroup.armsCreator.getBlackSave(s0.getTrueLevel(),da0.def);
               c0 = newS0.hurtRatio - s0.hurtRatio;
               if(c0 > 1)
               {
                  SaveTestBox.addText(da0.def.cnName + "：" + s0.hurtRatio + " => " + newS0.hurtRatio);
                  s0.hurtRatio = newS0.hurtRatio;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function getLevelSaveByName(name0:String) : ArmsSave
      {
         var color0:String = null;
         var s0:ArmsSave = null;
         var rangeD0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(name0);
         if(Boolean(rangeD0))
         {
            color0 = rangeD0.def.color;
            if(EquipColor.moreBlackB(color0))
            {
               s0 = this.getSuperSaveByArmsRangeName(10,name0,color0);
            }
            else
            {
               s0 = this.getSave_byArmsRangeName(name0,10,EquipColor.GREEN,"diff_0");
            }
         }
         return s0;
      }
      
      public function getLevelSaveGroup(nameArr0:Array, addImageB0:Boolean = false) : ArmsSaveGroup
      {
         var name0:String = null;
         var s0:ArmsSave = null;
         var g0:ArmsSaveGroup = new ArmsSaveGroup();
         for each(name0 in nameArr0)
         {
            s0 = this.getLevelSaveByName(name0);
            g0.addSaveByOther(s0);
            if(addImageB0)
            {
               GameArmsCtrl.addArmsSaveResoure(s0,false);
            }
         }
         return g0;
      }
      
      public function getSaveByBody(unitType0:String, bodyLv0:int, dropName0:String, setType0:String = "", dropMul0:Number = 0) : ArmsSave
      {
         var cd0:ArmsColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).arms;
         var color0:String = cd0.getOneDrop(unitType0,bodyLv0,dropMul0);
         zuobiPan(cd0,unitType0,dropName0);
         return this.getSaveByColor(color0,bodyLv0,dropName0,setType0);
      }
      
      public function getSaveByColor(color0:String, bodyLv0:int, dropName0:String, setType0:String = "") : ArmsSave
      {
         var cd0:ArmsColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).arms;
         var index0:int = int(cd0.name.indexOf(color0));
         var itemsLvRange0:Array = cd0.itemsLvRange[index0];
         var r0:Number = Number(itemsLvRange0[0]);
         var r1:Number = Number(itemsLvRange0[1]);
         if(bodyLv0 <= 8)
         {
            r0 *= 1 / (9 - bodyLv0);
            r1 *= 1 / (9 - bodyLv0);
         }
         var lv0:int = bodyLv0 + r0 + Math.random() * (r1 - r0 + 1);
         if(lv0 < 1)
         {
            lv0 = 1;
         }
         if(lv0 > cd0.MAX_LEVEL)
         {
            lv0 = cd0.MAX_LEVEL;
         }
         var typeArr0:Array = cd0.getColorTypeArr(color0);
         var type0:String = typeArr0[int(Math.random() * typeArr0.length)];
         if(setType0 != "")
         {
            type0 = setType0;
         }
         return this.getSave(color0,lv0,type0,dropName0);
      }
      
      public function getSave(color0:String, lv0:int, type0:String, dropName0:String) : ArmsSave
      {
         var d0:ArmsRangeDefine = this.getRandomArmsRangeDefineByType(type0);
         if(Boolean(d0))
         {
            return this.getSave_byArmsRangeDefine(d0,lv0,color0,dropName0);
         }
         return null;
      }
      
      public function getSave_byArmsRangeName(name0:String, lv0:int, color0:String, dropName0:String) : ArmsSave
      {
         var d0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(name0);
         if(!d0)
         {
            INIT.showError("找不到定义ArmsRangeDefine：" + name0);
         }
         return this.getSave_byArmsRangeDefine(d0,lv0,color0,dropName0);
      }
      
      public function getSave_byArmsRangeDefine(d0:ArmsRangeDefine, lv0:int, color0:String, dropName0:String, specialAndSkillB:Boolean = true) : ArmsSave
      {
         var s0:ArmsSave = d0.getArmsSave(color0);
         return this.getSave_bySave(s0,lv0,color0,dropName0,specialAndSkillB);
      }
      
      public function getSave_bySave(s0:ArmsSave, lv0:int, color0:String, dropName0:String, specialAndSkillB:Boolean = true) : ArmsSave
      {
         var cd0:ArmsColorDefine = Gaming.defineGroup.dropColor.getByName(dropName0).arms;
         var d0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(s0.name);
         var index0:int = int(cd0.name.indexOf(color0));
         var proPro0:Number = Number(cd0.proPro[index0]);
         var dpsRange0:Array = cd0.dpsRange[index0];
         var true_min_lv0:Number = Number(dpsRange0[0]);
         var min_lv0:Number = Math.floor(dpsRange0[0]);
         var max_lv0:Number = Math.floor(dpsRange0[1]);
         var min_per0:Number = 0;
         if(min_lv0 < max_lv0 && true_min_lv0 > min_lv0)
         {
            min_per0 = (true_min_lv0 - min_lv0) / (max_lv0 - min_lv0);
            if(min_per0 > 1)
            {
               min_per0 = 1;
            }
            if(min_per0 < 0)
            {
               min_per0 = 0;
            }
         }
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(s0,null);
         var min_dps0:Number = Gaming.defineGroup.normal.getArmsDps(Math.floor(dpsRange0[0]) + lv0);
         var max_dps0:Number = Gaming.defineGroup.normal.getArmsDps(Math.floor(dpsRange0[1]) + lv0);
         if(min_per0 > 0)
         {
            min_dps0 += (max_dps0 - min_dps0) * min_per0;
         }
         var ave_dps0:Number = (min_dps0 + max_dps0) / 2;
         var dps2:Number = 0;
         if(Math.random() < proPro0)
         {
            dps2 = min_dps0 + Math.random() * (ave_dps0 - min_dps0);
         }
         else
         {
            dps2 = ave_dps0 + Math.random() * (max_dps0 - ave_dps0);
         }
         dps2 *= d0.def.getDpsMul();
         var hurt0:Number = Math.ceil(getHurt(da0,dps2));
         s0.hurtRatio = hurt0;
         s0.itemsLevel = lv0;
         s0.color = color0;
         if(specialAndSkillB)
         {
            ArmsSpecialAndSkill.setSave(s0,dropName0);
         }
         return s0;
      }
      
      private function getRandomArmsRangeDefineByType(type0:String) : ArmsRangeDefine
      {
         var obj0:Object = Gaming.defineGroup.bullet.rangeTypeObj;
         var arr0:Array = Gaming.defineGroup.bullet.rangeTypeObj[type0];
         var proArr0:Array = Gaming.defineGroup.bullet.rangeRandomTypeObj[type0];
         var index0:int = ComMethod.getPro_byArrSum(proArr0);
         return arr0[index0];
      }
      
      public function getSuperSave(color0:String, lv0:int, type0:String, position0:String, specialNameArr0:Array, skillArr0:Array, godSkillArr0:Array, armsName0:String = "") : ArmsSave
      {
         var d0:ArmsRangeDefine = null;
         if(armsName0 == "")
         {
            d0 = this.getRandomArmsRangeDefineByType(type0);
         }
         else
         {
            d0 = Gaming.defineGroup.bullet.getArmsRangeDefine(armsName0);
         }
         if(!skillArr0)
         {
            skillArr0 = d0.def.skillArr.concat([]);
         }
         if(!godSkillArr0)
         {
            godSkillArr0 = d0.def.godSkillArr.concat([]);
         }
         if(type0 == "")
         {
            type0 = d0.def.armsType;
         }
         var s0:ArmsSave = d0.getSuperArmsSave(color0,position0);
         var dps0:Number = this.getDpsMulByColor(color0,lv0) * d0.def.getDpsMul();
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(s0,null);
         var hurt0:Number = Math.ceil(getHurt(da0,dps0));
         s0.hurtRatio = hurt0;
         s0.itemsLevel = lv0;
         s0.color = color0;
         if(armsName0 != "")
         {
            if(d0.def.cnName != "")
            {
               s0.cnName = d0.def.cnName;
            }
         }
         ArmsSpecialAndSkill.setSuperSave(s0,specialNameArr0,skillArr0,godSkillArr0);
         return s0;
      }
      
      private function getDpsMulByColor(color0:String, lv0:int) : Number
      {
         if(color0 == EquipColor.RED)
         {
            lv0 += 1;
         }
         else if(EquipColor.moreBlackB(color0))
         {
            lv0 += 5;
         }
         return Gaming.defineGroup.normal.getArmsDps(lv0);
      }
      
      public function getSuperSaveByArmsRangeName(lv0:int, armsName0:String, color0:String = "red") : ArmsSave
      {
         if(lv0 < 50)
         {
            lv0 = 50;
         }
         return this.getSuperSave(color0,lv0,"","max",null,null,null,armsName0);
      }
      
      public function getSuperDataByArmsRangeName(armsName0:String, pd0:NormalPlayerData, color0:String = "red") : ArmsData
      {
         var s0:ArmsSave = this.getSuperSaveByArmsRangeName(pd0.level,armsName0,color0);
         var da0:ArmsData = new ArmsData();
         da0.inData_bySave(s0,pd0);
         return da0;
      }
      
      public function getTempData(armsName0:String) : ArmsData
      {
         return this.getSuperDataByArmsRangeName(armsName0,Gaming.PG.da);
      }
      
      public function getRareSave(armsName0:String, lv0:int, dropName0:String, meSkillB0:Boolean = false) : ArmsSave
      {
         var d0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(armsName0);
         var s0:ArmsSave = this.getSave_byArmsRangeDefine(d0,lv0,"red",dropName0,false);
         s0.cnName = d0.def.cnName;
         if(meSkillB0)
         {
            ArmsSpecialAndSkill.setSkillByNameArr(s0,d0.def.skillArr,false);
         }
         else
         {
            ArmsSpecialAndSkill.setSkill(s0,2);
         }
         if(d0.def.godSkillArr.length > 0)
         {
            ArmsSpecialAndSkill.setSkillByNameArr(s0,d0.def.godSkillArr,true);
         }
         else
         {
            ArmsSpecialAndSkill.setSkill(s0,2,true);
         }
         return s0;
      }
      
      public function getBlackSave(lv0:int, armsD0:ArmsDefine) : ArmsSave
      {
         return this.getSuperSaveByArmsRangeName(lv0,armsD0.name,armsD0.color);
      }
      
      public function getByGift(d0:GiftAddDefine, heroLv0:int) : ArmsSave
      {
         var color0:String = null;
         var s0:ArmsSave = null;
         var rangeD0:ArmsRangeDefine = Gaming.defineGroup.bullet.getArmsRangeDefine(d0.name);
         if(Boolean(rangeD0))
         {
            color0 = rangeD0.def.color;
            if(EquipColor.moreBlackB(color0))
            {
               s0 = this.getSuperSaveByArmsRangeName(rangeD0.def.composeLv,d0.name,color0);
            }
            else
            {
               s0 = this.getSuperSaveByArmsRangeName(heroLv0,d0.name,color0);
            }
            s0.lockB = true;
         }
         else
         {
            s0 = GiftArmsDataCreator.getArmsSave(d0);
         }
         return s0;
      }
      
      public function getTempDataByGift(d0:GiftAddDefine) : ArmsData
      {
         var da0:ArmsData = null;
         var s0:ArmsSave = this.getByGift(d0,Gaming.PG.da.level);
         if(Boolean(s0))
         {
            da0 = new ArmsData();
            da0.inData_bySave(s0,Gaming.PG.da);
            return da0;
         }
         return null;
      }
      
      private function getInitDps(color0:String, lv0:int, d0:ArmsDefine) : Number
      {
         return this.getDpsMulByColor(color0,lv0) * d0.getDpsMul();
      }
      
      private function getInitHurt(color0:String, lv0:int, d0:ArmsDefine) : Number
      {
         var dps0:Number = this.getInitDps(color0,lv0,d0);
         return Math.ceil(getHurt(d0,dps0));
      }
      
      public function setArmsToInitHurt(da0:ArmsData) : void
      {
         var s0:ArmsSave = da0.save;
         var hurt0:Number = this.getInitHurt(s0.color,s0.itemsLevel,da0);
         s0.hurtRatio = hurt0;
      }
   }
}

