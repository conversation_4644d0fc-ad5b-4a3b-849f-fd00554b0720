package dataAll.gift
{
   import UI.UIShow;
   import UI.lottery.NormalLotteryGift;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.creator.MoreAddData;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.creator.GiftEquipDataCreator;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.jewelry.JewelryDataCreator;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.shield.ShieldDataCreator;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.gift.define.BagSpaceMust;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.GiftBase;
   import dataAll.gift.define.GiftType;
   import dataAll.pet.gene.creator.GeneDataCreator;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.things.define.ThingsDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class GiftAddit
   {
      
      public static const lottery:NormalLotteryGift = new NormalLotteryGift();
      
      public function GiftAddit()
      {
         super();
      }
      
      public static function bagSpacePan(gg0:GiftAddDefineGroup, allNum0:int = 1, pd0:PlayerData = null) : String
      {
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var type0:String = null;
         var nowNum0:int = 0;
         var mpd0:MorePlayerData = null;
         var must0:BagSpaceMust = gg0.getBagSpaceMust(allNum0);
         if(!pd0)
         {
            pd0 = Gaming.PG.da;
         }
         var bagTypeArr0:Array = BagSpaceMust.pro_arr;
         for(n in bagTypeArr0)
         {
            type0 = bagTypeArr0[n];
            nowNum0 = pd0.getBagSpaceByType(type0);
            if(nowNum0 < must0[type0])
            {
               return "背包空位不足！\n需要 " + ComMethod.color(must0[type0] + "","#FFFF00") + " 个" + BagSpaceMust.getCnNameByName(type0) + "背包空位！";
            }
         }
         for each(d0 in gg0.arr)
         {
            if(d0.type == GiftType.base)
            {
               if(d0.name == GiftBase.exploit)
               {
                  mpd0 = Gaming.PG.DATA as MorePlayerData;
                  if(!Boolean(mpd0))
                  {
                     return "主角无法添加功勋值，请切换到指定角色的界面。";
                  }
                  if(mpd0.partner.getExplotSurplus() <= 0)
                  {
                     return mpd0.getCnName() + "的功勋值已满，无法添加。";
                  }
               }
            }
         }
         return "";
      }
      
      public static function addAndAutoBagSpacePan(gg0:GiftAddDefineGroup, tipStr0:String = null, errorTip0:String = null, yesFun0:Function = null) : Boolean
      {
         var str0:String = bagSpacePan(gg0);
         if(str0 == "")
         {
            add(gg0,tipStr0,false,yesFun0);
            return true;
         }
         if(errorTip0 != "")
         {
            if(errorTip0 != null)
            {
               str0 = errorTip0;
            }
            Gaming.uiGroup.alertBox.showError(str0);
         }
         return false;
      }
      
      public static function addAndTip(gg0:GiftAddDefineGroup, yesFun0:Function = null) : Boolean
      {
         return addAndAutoBagSpacePan(gg0,"获得：\n" + gg0.getDescription(),null,yesFun0);
      }
      
      public static function add(gg0:GiftAddDefineGroup, tipStr0:String = "", tipNoBtnB:Boolean = false, yesFun0:Function = null) : Array
      {
         var getArr0:Array = addByDefineGroup(gg0,Gaming.PG.da);
         UIShow.flesh_coinChange();
         if(!(tipStr0 is String))
         {
            tipStr0 = "领取礼包成功！";
         }
         if(tipStr0 != "")
         {
            if(tipNoBtnB)
            {
               Gaming.uiGroup.alertBox.showCheck(tipStr0,"",0.5,yesFun0,null,"yes","icon");
            }
            else
            {
               Gaming.uiGroup.alertBox.showNormal(tipStr0,"yes",yesFun0,null,"yes");
            }
         }
         return getArr0;
      }
      
      public static function addByDefineGroup(gg0:GiftAddDefineGroup, pd0:PlayerData) : Array
      {
         var d2:GiftAddDefine = null;
         var n:* = undefined;
         var d0:GiftAddDefine = null;
         var getArr0:Array = [];
         if(gg0.randomB)
         {
            d2 = gg0.getRandomDefine(pd0.getSave().gift.dropEnsureFun);
            addByDefine(d2,pd0);
            getArr0.push(d2);
         }
         else
         {
            for(n in gg0.arr)
            {
               d0 = gg0.arr[n];
               addByDefine(d0,pd0);
               getArr0.push(d0);
            }
         }
         return getArr0;
      }
      
      public static function addByDefine(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var mpd0:MorePlayerData = null;
         var thingsD0:ThingsDefine = null;
         if(d0.type == "base")
         {
            if(d0.name == GiftBase.exp)
            {
               pd0.addAllExp(d0.num);
            }
            else if(d0.name == GiftBase.craftExp)
            {
               pd0.space.addNowCraftExp(d0.num);
            }
            else if(d0.name == GiftBase.bossCardNum)
            {
               pd0.bossCard.outAddDraw(d0.num);
            }
            else if(d0.name == GiftBase.coin)
            {
               pd0.main.addCoin(d0.num);
            }
            else if(d0.name == GiftBase.anniCoin)
            {
               pd0.main.addAnniCoin(d0.num);
            }
            else if(d0.name == GiftBase.tenCoin)
            {
               pd0.main.addTenCoin(d0.num);
            }
            else if(d0.name == GiftBase.partsCoin)
            {
               pd0.main.addPartsCoin(d0.num);
            }
            else if(d0.name == GiftBase.wilderKey)
            {
               pd0.wilder.addKey(d0.num);
            }
            else if(d0.name == GiftBase.pumpkin)
            {
               pd0.main.addPumpkin(d0.num);
            }
            else if(d0.name == GiftBase.daySweeping)
            {
               pd0.main.addDaySweeping(d0.num);
            }
            else if(d0.name == GiftBase.exploit)
            {
               mpd0 = Gaming.PG.DATA as MorePlayerData;
               if(Boolean(mpd0))
               {
                  mpd0.partner.addExploitAll(d0.num);
               }
            }
         }
         else if(d0.type == "time")
         {
            pd0.time.addDoubleTime(d0.name,d0.num);
         }
         else if(d0.type == "things")
         {
            thingsD0 = Gaming.defineGroup.things.getDefine(d0.name);
            if(thingsD0.isPartsB())
            {
               addParts(d0,pd0);
            }
            else
            {
               pd0.thingsBag.addDataByName(d0.name,d0.num);
               if(d0.name == "arenaStamp")
               {
                  pd0.arena.addStampNumOnly(d0.num);
               }
               else if(Boolean(thingsD0))
               {
                  pd0.drop.save.giftThingsAdd(thingsD0,d0.num);
               }
            }
         }
         else if(d0.type == "parts")
         {
            addParts(d0,pd0);
         }
         else if(d0.type == "arms")
         {
            addArmsData(d0,pd0);
         }
         else if(d0.type == "equip")
         {
            addEquipData(d0,pd0);
         }
         else if(d0.type == "gene")
         {
            addGeneData(d0,pd0);
         }
         else if(d0.type == "more")
         {
            addMoreData(d0,pd0);
         }
         else if(d0.type == "head")
         {
            addHeadData(d0,pd0);
         }
         else if(d0.type == GiftType.foodRaw)
         {
            addFoodRaw(d0,pd0);
         }
         else if(d0.type == GiftType.bossCard)
         {
            pd0.bossCard.addByDefId(d0.name);
         }
      }
      
      public static function addDefineGroupByOtherPlayerData(gg0:GiftAddDefineGroup, pd0:PlayerData) : Boolean
      {
         var tipStr0:String = bagSpacePan(gg0,1,pd0);
         if(tipStr0 == "")
         {
            addByDefineGroup(gg0,pd0);
            return true;
         }
         Gaming.uiGroup.alertBox.showError(tipStr0);
         return false;
      }
      
      private static function addParts(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var d2:GiftAddDefine = null;
         if(d0.name == "all")
         {
            nameArr0 = Gaming.defineGroup.things.normalPartsNameArr;
            for each(name0 in nameArr0)
            {
               d2 = new GiftAddDefine();
               d2.inData_byStr("parts;" + name0 + "_" + d0.lv + ";" + d0.num);
               addParts(d2,pd0);
            }
         }
         else
         {
            pd0.partsBag.addDataByName(d0.name,d0.num);
         }
      }
      
      private static function addArmsData(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var s0:ArmsSave = d0.itemsSave as ArmsSave;
         if(!s0)
         {
            s0 = Gaming.defineGroup.armsCreator.getByGift(d0,pd0.level);
         }
         if(Boolean(s0))
         {
            pd0.armsBag.addSave(s0);
            GameArmsCtrl.addArmsSaveResoure(s0);
         }
      }
      
      private static function addEquipData(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var s2:EquipSave = null;
         var equipD0:EquipDefine = Gaming.defineGroup.getAllEquipDefine(d0.name);
         var s0:EquipSave = d0.itemsSave as EquipSave;
         var equipBag0:EquipDataGroup = pd0.equipBag;
         if(Boolean(equipD0))
         {
            s2 = getEquipSaveByName(d0.name,pd0.level,d0.num,s0);
            if(Boolean(s2))
            {
               s0 = s2;
            }
         }
         if(!s0)
         {
            s0 = GiftEquipDataCreator.getEquipSave(d0);
         }
         if(Boolean(s0))
         {
            equipBag0.addSave(s0);
         }
      }
      
      public static function getEquipSaveByName(name0:String, heroLv0:int, num0:int = 1, setSave0:EquipSave = null) : EquipSave
      {
         var s0:EquipSave = null;
         var equipD0:EquipDefine = Gaming.defineGroup.getAllEquipDefine(name0);
         if(Boolean(equipD0))
         {
            if(equipD0.type == EquipType.VEHICLE)
            {
               s0 = VehicleDataCreator.getSave(name0,heroLv0);
            }
            else if(equipD0.type == EquipType.DEVICE)
            {
               s0 = DeviceDataCreator.getSave(name0,num0);
            }
            else if(equipD0.type == EquipType.WEAPON)
            {
               s0 = WeaponDataCreator.getSave(name0,num0);
            }
            else if(equipD0.type == EquipType.JEWELRY)
            {
               s0 = JewelryDataCreator.getSave(name0,num0);
            }
            else if(equipD0.type == EquipType.SHIELD)
            {
               s0 = ShieldDataCreator.getSave(name0,num0);
            }
            else if(equipD0.type == EquipType.FASHION)
            {
               s0 = Gaming.defineGroup.equipCreator.getFashionSave(name0);
            }
            else if(equipD0.isNormalB())
            {
               if(Boolean(setSave0))
               {
                  s0 = setSave0;
               }
               else
               {
                  s0 = Gaming.defineGroup.equipCreator.getSuperSave("red",heroLv0,name0,false);
               }
            }
         }
         return s0;
      }
      
      public static function getEquipDataByName(name0:String, pd0:PlayerData, num0:int = 1) : EquipData
      {
         var da0:EquipData = null;
         var s0:EquipSave = getEquipSaveByName(name0,pd0.level,num0);
         if(Boolean(s0))
         {
            da0 = s0.getDataClass();
            da0.inData_bySave(s0,pd0);
            return da0;
         }
         return null;
      }
      
      private static function addGeneData(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var s0:GeneSave = d0.itemsSave as GeneSave;
         var creator0:GeneDataCreator = Gaming.defineGroup.geneCreator;
         if(!s0 && d0.name != "")
         {
            s0 = creator0.getSaveByGift(d0,pd0.level);
         }
         if(Boolean(s0))
         {
            pd0.geneBag.addSave(s0);
         }
      }
      
      private static function addMoreData(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         var addDa0:MoreAddData = new MoreAddData(d0);
         var da0:MoreData = pd0.getMoreDataByName(d0.name);
         if(!(da0 is MoreData))
         {
            pd0.moreBag.addByAddData(addDa0);
         }
      }
      
      private static function addHeadData(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         pd0.head.addHead(d0.name,Gaming.api.save.getNowServerDate().getStr());
      }
      
      private static function addFoodRaw(d0:GiftAddDefine, pd0:PlayerData) : void
      {
         pd0.food.addRawNum(d0.name,d0.num);
      }
   }
}

