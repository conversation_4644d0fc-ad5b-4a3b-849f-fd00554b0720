package dataAll.equip.shield
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.must.define.MustDefine;
   
   public class ShieldData extends EquipData
   {
      
      public var shieldDefine:ShieldDefine = null;
      
      public function ShieldData()
      {
         super();
      }
      
      public function getShieldSave() : ShieldSave
      {
         return save as ShieldSave;
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         super.inData_bySave(s0,pd0,dg0);
         this.shieldDefine = this.getShieldSave().getShieldDefine();
         save.obj = this.shieldDefine.getAddObj();
      }
      
      override public function clone() : EquipData
      {
         var da0:ShieldData = super.clone() as ShieldData;
         da0.shieldDefine = this.shieldDefine;
         return da0;
      }
      
      override public function isCanNumSwapB() : Boolean
      {
         return true;
      }
      
      override public function isCanOverlayB() : Boolean
      {
         return true;
      }
      
      override public function getNowNum() : int
      {
         return save.nowNum;
      }
      
      override public function setNowNum(num0:int) : void
      {
         save.nowNum = num0;
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         addNowTrueNum(num0,otherDa0);
      }
      
      override public function getSellPrice() : Number
      {
         return save.nowNum * 2000 * save.itemsLevel;
      }
      
      public function getUpradeData() : ShieldData
      {
         var upgradeName0:String = this.shieldDefine.getUpgradeName();
         var s0:ShieldSave = ShieldDataCreator.getSave(upgradeName0,1);
         var da0:ShieldData = new ShieldData();
         da0.inData_bySave(s0,normalPlayerData);
         return da0;
      }
      
      public function getUpradeMust() : MustDefine
      {
         return ShieldDataCreator.getUpgradeMust(this.shieldDefine,this.shieldDefine.lv,placeType);
      }
      
      public function changeToOneData(da0:ShieldData) : void
      {
         var name0:String = null;
         var proArr0:Array = ["skillArr","imgName","nowNum","itemsLevel","name","cnName","newB","lockB"];
         for each(name0 in proArr0)
         {
            save[name0] = da0.save[name0];
         }
         this.shieldDefine = this.getShieldSave().getShieldDefine();
         save.obj = this.shieldDefine.getAddObj();
      }
      
      override public function getGatherTip(compareDa0:EquipData = null, tipType0:String = "", showSkillB:Boolean = true) : String
      {
         return this.shieldDefine.getGatherTip();
      }
   }
}

