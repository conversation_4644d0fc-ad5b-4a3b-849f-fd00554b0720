package dataAll.level.define.info
{
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   
   public class LevelInfoDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var _enemyLv:String = "";
      
      public var diff:Number = 1;
      
      public var music:String = "";
      
      public var overWarn:String = "";
      
      public var noMoreB:Boolean = false;
      
      public var allMoreB:Boolean = false;
      
      public var noPartnerB:Boolean = false;
      
      public var noPetB:Boolean = false;
      
      public var noVehicleB:Boolean = false;
      
      public var noPropsB:Boolean = false;
      
      public var noDeviceB:Boolean = false;
      
      public var dropSmallMapB:Boolean = false;
      
      public var preBulletArr:Array = [];
      
      public var preSkillArr:Array = [];
      
      public var diy:String = "";
      
      public var modeDiy:String = "";
      
      public var dropNoGravityTime:Number = 0;
      
      public var noTreasureB:Boolean = false;
      
      public var mustSingleB:Boolean = false;
      
      public var sightCover:int = 0;
      
      public var dumB:Boolean = false;
      
      public var tm:Number = 0;
      
      public var hb:Boolean = false;
      
      public var overBackB:Boolean = false;
      
      public var firstLostB:Boolean = false;
      
      public var noRestartB:Boolean = false;
      
      public var noAIB:Boolean = false;
      
      public function LevelInfoDefine()
      {
         super();
         this.enemyLv = 0;
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function set enemyLv(v0:Number) : void
      {
         this._enemyLv = Sounto64.encode(String(v0));
      }
      
      public function get enemyLv() : Number
      {
         return Number(Sounto64.decode(this._enemyLv));
      }
      
      public function fixedBy(f0:LevelInfoDefine, type0:String) : void
      {
         if(type0 == "all")
         {
            this.inData_byObj(f0);
         }
         else if(type0 == "noMoreB")
         {
            this.noMoreB = f0.noMoreB;
         }
      }
      
      public function isNoPet() : Boolean
      {
         return this.noPetB || this.noMoreB;
      }
      
      public function isNoVehicle() : Boolean
      {
         return this.noVehicleB;
      }
      
      public function isNoPartner() : Boolean
      {
         return this.noPartnerB || this.noMoreB;
      }
      
      public function isNoDevice() : Boolean
      {
         return this.noDeviceB;
      }
      
      public function isNoProps() : Boolean
      {
         return this.noPropsB;
      }
      
      public function isMustSingleB() : Boolean
      {
         return this.mustSingleB;
      }
      
      public function toOne() : void
      {
         this.mustSingleB = true;
         this.noMoreB = true;
      }
      
      public function noAll() : void
      {
         this.noVehicleB = true;
         this.noPropsB = true;
         this.noDeviceB = true;
         this.toOne();
      }
   }
}

