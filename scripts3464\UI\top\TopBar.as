package UI.top
{
   import UI.bag.ItemsGrid;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import dataAll._app.top.TopBarData;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.UnionInfo;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class TopBar extends ItemsGrid
   {
      
      private var txtArr:Array = [];
      
      private var clickAddB:Boolean = false;
      
      public function TopBar()
      {
         super();
      }
      
      public function inData_byTopBarData(da0:TopBarData) : void
      {
         this.setTextArr(da0.getStringArr());
         itemsData = da0;
         actived = true;  // 确保排行榜条目可以被点击
         setShopBtnBackMc(1);
         if(Boolean(da0.define))
         {
            if(da0.define.objType == "arena")
            {
               setShopBtnBackMc(da0.isChallengedB() ? "yes" : "no");
            }
         }
      }
      
      public function inData_byUnionInfo(i0:UnionInfo) : void
      {
         itemsData = i0;
         this.setTextArr(i0.getStringArr());
         setShopBtnBackMc(1);
      }
      
      public function inData_byMemberInfo(i0:MemberInfo) : void
      {
         itemsData = i0;
         this.setTextArr(i0.getStringArr());
         setShopBtnBackMc(1);
      }
      
      public function inData_byCheckInfo(i0:MemberInfo) : void
      {
         itemsData = i0;
         this.setTextArr(i0.getCheckStringArr());
         setShopBtnBackMc(1);
      }
      
      public function setTextArr(arr0:Array) : void
      {
         var n:* = undefined;
         var txt0:TextField = null;
         var str0:String = null;
         for(n in this.txtArr)
         {
            txt0 = this.txtArr[n];
            str0 = arr0[n];
            if(str0 is String)
            {
               txt0.htmlText = str0;
            }
            else
            {
               txt0.htmlText = "";
            }
         }
      }
      
      override public function setImg(img0:MovieClip) : void
      {
         var mc0:DisplayObject = null;
         var numChildren0:int = img0.numChildren;
         for(var i:int = 0; i < numChildren0; i++)
         {
            mc0 = img0.getChildAt(i);
            if(mc0 is TextField)
            {
               this.txtArr.push(mc0);
               FontDeal.dealOne(mc0 as TextField);
            }
         }
         super.setImg(img0);
      }
      
      public function changeToTitle() : void
      {
         var n:* = undefined;
         var txt0:TextField = null;
         actived = false;
         setShopBtnBackMc("");
         for(n in this.txtArr)
         {
            txt0 = this.txtArr[n];
            txt0.textColor = 6749952;
         }
      }
      
      public function addClickEvent() : void
      {
         var txt0:TextField = null;
         if(this.clickAddB == false)
         {
            this.mouseChildren = true;
            this.mouseIconEffectB = true;
            this.clickAddB = true;
            for each(txt0 in this.txtArr)
            {
               txt0.addEventListener(MouseEvent.CLICK,this.textClick);
            }
         }
      }
      
      private function textClick(e:MouseEvent) : void
      {
         var e0:ClickEvent = new ClickEvent(ClickEvent.TOP_TITLE_CLICK);
         var txt0:TextField = e.target as TextField;
         e0.child = txt0;
         e0.father = this;
         e0.childData = txt0.text;
         dispatchEvent(e0);
      }
   }
}

