package dataAll.image
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._base.IO_Define;
   import dataAll._base.IO_TipDefine;
   import dataAll._base.NormalDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class SoundDefine extends NormalDefine implements IO_TipDefine
   {
      
      public static const SHOOT:String = "shoot";
      
      public static const SKILL:String = "skill";
      
      public static const SKILL_OTHER:String = "skillOther";
      
      public static const BULLET:String = "bullet";
      
      public static const OTHER:String = "other";
      
      public static const ALL_ARR:Array = [SHOOT,BULLET,SKILL,SKILL_OTHER];
      
      public static const ALL_CN:Array = ["射击","子弹相关","技能","技能相关","其他"];
      
      public function SoundDefine()
      {
         super();
      }
      
      public static function getFatherCn(father0:String) : String
      {
         var f0:int = int(ALL_ARR.indexOf(father0));
         return ArrayMethod.getElement(ALL_CN,f0,"","") as String;
      }
      
      public static function getFatherCnArr(fatherArr0:Array) : Array
      {
         var father0:String = null;
         var cnArr0:Array = [];
         for each(father0 in fatherArr0)
         {
            cnArr0.push(getFatherCn(father0));
         }
         return cnArr0;
      }
      
      public static function getFatherByPos(d0:IO_Define, posId0:String) : String
      {
         if(d0 is BulletDefine)
         {
            return BULLET;
         }
         if(d0 is SkillDefine)
         {
            if(posId0 == ImageUrlFather.me || posId0 == ImageUrlFather.target)
            {
               return SKILL;
            }
            return SKILL_OTHER;
         }
         return OTHER;
      }
      
      public function getTipStr() : String
      {
         return name;
      }
   }
}

