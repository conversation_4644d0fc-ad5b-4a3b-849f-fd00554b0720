package dataAll.equip.vehicle
{
   public class VehicleOneProDataGroup
   {
      
      public var arr:Array;
      
      public var dat:VehicleData = null;
      
      public function VehicleOneProDataGroup(num0:int = 0)
      {
         var da0:VehicleOneProData = null;
         this.arr = [];
         super();
         for(var i:int = 0; i < num0; i++)
         {
            da0 = new VehicleOneProData();
            this.add(da0);
         }
      }
      
      public function add(da0:VehicleOneProData) : void
      {
         this.arr.push(da0);
      }
      
      public function isCanStrengthenB() : Boolean
      {
         var pda0:VehicleOneProData = null;
         for each(pda0 in this.arr)
         {
            if(pda0.isCanStrengthenB())
            {
               return true;
            }
         }
         return false;
      }
      
      public function getCanStrengthenArr() : Array
      {
         var pda0:VehicleOneProData = null;
         var arr0:Array = [];
         for each(pda0 in this.arr)
         {
            if(pda0.isCanStrengthenB())
            {
               arr0.push(pda0);
            }
         }
         return arr0;
      }
      
      public function getRandom() : VehicleOneProData
      {
         var arr0:Array = this.getCanStrengthenArr();
         if(arr0.length > 0)
         {
            return arr0[int(Math.random() * arr0.length)];
         }
         return null;
      }
   }
}

