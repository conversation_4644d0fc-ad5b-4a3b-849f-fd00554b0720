package dataAll._app.partner
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.edit.TorSave;
   import dataAll._app.partner.ability.PartnerAbility;
   import dataAll._app.partner.ability.PartnerAbilityData;
   import dataAll._app.partner.ability.PartnerAbilityDefine;
   import dataAll._app.partner.ai.HeroAITorData;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.level.define.unit.UnitType;
   import gameAll.body.ai.one.PlayerAiData;
   import gameAll.level.LevelGroup;
   
   public class PartnerData implements IO_HavePlayerData
   {
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var PD:NormalPlayerData;
      
      public var save:PartnerSave = null;
      
      private var abilityObj:Object = null;
      
      private var aiArr:Array = [];
      
      private var tempAiData:PlayerAiData = null;
      
      public function PartnerData()
      {
         super();
      }
      
      public static function get maxDayExploit() : int
      {
         return 10000;
      }
      
      public static function get maxExploit() : int
      {
         return 300000;
      }
      
      public static function get aiExploit() : int
      {
         return 45000;
      }
      
      public function get hurtExploit() : Number
      {
         return this.CF.getAttribute("hurtExploit");
      }
      
      public function set hurtExploit(v0:Number) : void
      {
         this.CF.setAttribute("hurtExploit",v0);
      }
      
      public function inData_bySave(s0:PartnerSave) : void
      {
         var defineArr0:Array = null;
         var d0:PartnerAbilityDefine = null;
         var da0:PartnerAbilityData = null;
         this.save = s0;
         if(!this.abilityObj)
         {
            defineArr0 = Gaming.defineGroup.partner.getAbilityArr();
            this.abilityObj = {};
            for each(d0 in defineArr0)
            {
               da0 = new PartnerAbilityData();
               da0.inData(d0,this);
               this.abilityObj[d0.name] = da0;
            }
         }
         this.fleshAiData();
      }
      
      private function fleshAiData() : void
      {
         var s0:TorSave = null;
         var da0:HeroAITorData = null;
         this.aiArr.length = 0;
         for each(s0 in this.save.aiArr)
         {
            da0 = new HeroAITorData();
            da0.PD = this.PD;
            da0.inData_bySave(s0);
            da0.initByDefine(this.PD.heroData.def);
            this.aiArr.push(da0);
         }
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.PD = pd0;
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         var da0:HeroAITorData = null;
         this.setPlayerData(pd0);
         for each(da0 in this.aiArr)
         {
            da0.PD = pd0;
         }
      }
      
      public function newDayCtrl(nowTimeStr0:String) : void
      {
         this.save.newDayCtrl(nowTimeStr0);
      }
      
      private function addExploit(v0:Number) : void
      {
         v0 = Math.round(v0);
         var day0:Number = this.save.dayExploit;
         var newDay0:Number = day0 + v0;
         if(newDay0 > maxDayExploit)
         {
            newDay0 = maxDayExploit;
         }
         var add0:Number = newDay0 - day0;
         if(add0 > 0)
         {
            this.addExploitAll(add0,newDay0);
         }
      }
      
      public function addExploitAll(add0:Number, setNewDay0:Number = -1) : void
      {
         var exploit0:Number = this.save.exploit;
         var new0:Number = exploit0 + add0;
         if(new0 > maxExploit)
         {
            new0 = maxExploit;
         }
         add0 = new0 - exploit0;
         if(add0 > 0)
         {
            if(setNewDay0 >= 0)
            {
               this.save.dayExploit = setNewDay0;
            }
            this.save.exploit += add0;
         }
      }
      
      public function getExplotSurplus() : Number
      {
         return maxExploit - this.save.exploit;
      }
      
      public function getPointAll() : int
      {
         return Math.floor(this.save.exploit / 1000);
      }
      
      public function getPointAllUse() : int
      {
         var v0:Number = NaN;
         var obj0:Object = this.save.ability.saveObj;
         var all0:Number = 0;
         for each(v0 in obj0)
         {
            all0 += v0;
         }
         return all0;
      }
      
      public function getPointSurplus() : int
      {
         return this.getPointAll() - this.getPointAllUse();
      }
      
      public function resetPointAll() : void
      {
         this.save.ability.clearData();
      }
      
      public function startLevel() : void
      {
         this.hurtExploit = 0;
      }
      
      public function killEnemy(b0:IO_PartnerEnemy) : void
      {
         var v0:Number = this.getExploitByUnitType(b0.getUnitType());
         var cLv0:int = this.PD.level - b0.getLv();
         var cMul0:Number = 1 - cLv0 * 0.1;
         cMul0 = NumberMethod.limitRange(cMul0,0.05,1);
         v0 *= cMul0;
         if(v0 > 0)
         {
            this.addExploit(v0);
         }
      }
      
      private function getExploitByUnitType(type0:String) : int
      {
         if(type0 == UnitType.SUPER)
         {
            return 20;
         }
         if(type0 == UnitType.BOSS)
         {
            return 80;
         }
         return 4;
      }
      
      public function underHurt(hurt0:Number) : void
      {
         var baseHurt0:Number = NaN;
         var c0:Number = NaN;
         var v0:Number = NaN;
         var level0:Number = NaN;
         if(hurt0 > 0)
         {
            baseHurt0 = Gaming.defineGroup.normal.getEnemyDps(this.PD.level);
            c0 = hurt0 / baseHurt0;
            v0 = c0 * 24;
            v0 = NumberMethod.limitRange(v0,0,100);
            v0 = Math.round(v0);
            if(v0 > 0)
            {
               level0 = this.hurtExploit;
               if(level0 < 400)
               {
                  level0 += v0;
                  this.hurtExploit = level0;
                  this.addExploit(v0);
               }
            }
         }
      }
      
      public function getAbilityData(name0:String) : PartnerAbilityData
      {
         return this.abilityObj[name0];
      }
      
      public function getBasePoint(name0:String) : Number
      {
         var dpsMul0:Number = NaN;
         var underHurtMul0:Number = NaN;
         var v0:Number = 0;
         if(name0 == PartnerAbility.attack)
         {
            dpsMul0 = this.PD.heroData.def.moreD.dpsMul;
            v0 = PartnerAbility.countAttack(dpsMul0);
         }
         else if(name0 == PartnerAbility.defence)
         {
            underHurtMul0 = this.PD.heroData.def.moreD.underHurtMul;
            v0 = PartnerAbility.countDefence(underHurtMul0);
         }
         return Math.round(v0);
      }
      
      public function getPoint(name0:String) : Number
      {
         var da0:PartnerAbilityData = this.getAbilityData(name0);
         if(Boolean(da0))
         {
            return da0.getPoint();
         }
         return 0;
      }
      
      public function getWearPointStr(name0:String) : String
      {
         var da0:PartnerAbilityData = this.getAbilityData(name0);
         if(Boolean(da0))
         {
            return da0.getWearPointStr();
         }
         return "";
      }
      
      public function getWearPointPer(name0:String) : Number
      {
         var p0:Number = this.getPoint(name0);
         return p0 / 200;
      }
      
      public function getDpsMul() : Number
      {
         var p0:Number = this.getPoint(PartnerAbility.attack);
         return PartnerAbility.getDpsMul(p0);
      }
      
      public function getUnderHurtMul() : Number
      {
         var p0:Number = this.getPoint(PartnerAbility.defence);
         return PartnerAbility.getUnderHurtMul(p0);
      }
      
      public function aiOpenB() : Boolean
      {
         if(this.PD.isMainPlayerB())
         {
            return LevelGroup.getOpenAITaskB();
         }
         return this.save.exploit >= aiExploit;
      }
      
      public function getAiOpenError() : String
      {
         var bb0:Boolean = this.aiOpenB();
         if(bb0)
         {
            return "";
         }
         if(this.PD.isMainPlayerB())
         {
            return "完成任务" + LevelGroup.getOpenAITaskCn() + "才能开启该功能。";
         }
         return "角色功勋值超过 " + PartnerData.aiExploit + " 才能开启此功能。";
      }
      
      public function getNowAITor() : HeroAITorData
      {
         return this.aiArr[this.save.aiIndex];
      }
      
      public function getOneAIData() : PlayerAiData
      {
         if(this.tempAiData == null)
         {
            this.tempAiData = new PlayerAiData();
         }
         this.getNowAITor().dealAiData(this.tempAiData);
         return this.tempAiData;
      }
   }
}

