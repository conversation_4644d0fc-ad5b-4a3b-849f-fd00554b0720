package dataAll._app.peak
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.NumberMethod;
   import dataAll._base.NormalDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class PeakProDefine extends NormalDefine
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var iconUrl:String = "";
      
      public var skill:String = "";
      
      public var unit:String = "";
      
      public var pro:String = "";
      
      public var descrip:String = "";
      
      public function PeakProDefine()
      {
         super();
         this.point = 1;
      }
      
      public function get max() : Number
      {
         return this.CF.getAttribute("max");
      }
      
      public function set max(v0:Number) : void
      {
         this.CF.setAttribute("max",v0);
      }
      
      public function get firstPoint() : Number
      {
         return this.CF.getAttribute("firstPoint");
      }
      
      public function set firstPoint(v0:Number) : void
      {
         this.CF.setAttribute("firstPoint",v0);
      }
      
      public function get point() : Number
      {
         return this.CF.getAttribute("point");
      }
      
      public function set point(v0:Number) : void
      {
         this.CF.setAttribute("point",v0);
      }
      
      public function get add() : Number
      {
         return this.CF.getAttribute("add");
      }
      
      public function set add(v0:Number) : void
      {
         this.CF.setAttribute("add",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         if(father0 == "peak")
         {
            if(this.skill == "")
            {
               this.firstPoint = this.point;
               if(this.pro == "")
               {
                  this.pro = name;
               }
            }
            else
            {
               this.point = 2;
               this.firstPoint = this.point;
            }
         }
         else
         {
            this.firstPoint = this.point;
         }
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      public function isProB() : Boolean
      {
         return this.skill == "";
      }
      
      public function getMustPointByLv(lv0:int) : int
      {
         if(lv0 == 1)
         {
            return this.firstPoint;
         }
         return this.point;
      }
      
      public function getMustPointSum(lv0:int) : int
      {
         var sum0:Number = 0;
         for(var i:int = 1; i <= lv0; i++)
         {
            sum0 += this.getMustPointByLv(i);
         }
         return sum0;
      }
      
      public function getMaxPoint(surplus0:int, now0:int) : int
      {
         var must0:int = 0;
         var max0:Number = this.max;
         var add0:int = 0;
         for(var lv0:int = now0 + 1; lv0 <= max0; lv0++)
         {
            must0 = this.getMustPointByLv(lv0);
            if(must0 <= surplus0)
            {
               add0++;
               surplus0 -= must0;
            }
         }
         var v0:Number = add0 + now0;
         if(v0 > this.max)
         {
            v0 = this.max;
         }
         return v0;
      }
      
      public function getProAdd(lv0:int) : Number
      {
         return lv0 * this.add;
      }
      
      public function getProAddStr(lv0:int) : String
      {
         var v0:Number = this.getProAdd(lv0);
         var s0:String = "";
         if(this.isIntB())
         {
            s0 = Math.round(v0) + "";
         }
         else
         {
            s0 = NumberMethod.toPer(v0);
         }
         s0 += this.unit;
         return "+" + s0;
      }
      
      public function isIntB() : Boolean
      {
         if(this.add >= 1)
         {
            return true;
         }
         return false;
      }
      
      public function getAddBtnTip() : String
      {
         var s0:String = null;
         var first0:int = this.getMustPointByLv(1);
         var v0:int = this.getMustPointByLv(2);
         if(first0 != v0)
         {
            s0 = "第1级消耗<yellow " + first0 + "点/>点数。";
            return s0 + ("\n剩下每级消耗<yellow " + v0 + "点/>点数。");
         }
         if(v0 > 1)
         {
            return "每级消耗<yellow " + v0 + "点/>点数。";
         }
         return "";
      }
      
      public function getIconUrl() : String
      {
         if(this.iconUrl != "")
         {
            return this.iconUrl;
         }
         var sd0:SkillDefine = this.getSkillDefine();
         if(Boolean(sd0))
         {
            return sd0.getIconImgUrl(50,50);
         }
         return "";
      }
      
      public function getSkillDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.skill) as HeroSkillDefine;
      }
      
      public function getLevelSkillName(lv0:int) : String
      {
         return this.skill + "_" + lv0;
      }
   }
}

