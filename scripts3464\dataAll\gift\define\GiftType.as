package dataAll.gift.define
{
   public class GiftType
   {
      
      public static const head:String = "head";
      
      public static const base:String = "base";
      
      public static const things:String = "things";
      
      public static const parts:String = "parts";
      
      public static const equip:String = "equip";
      
      public static const arms:String = "arms";
      
      public static const gene:String = "gene";
      
      public static const foodRaw:String = "foodRaw";
      
      public static const bossCard:String = "bossCard";
      
      public static const armsSkin:String = "armsSkin";
      
      public function GiftType()
      {
         super();
      }
   }
}

