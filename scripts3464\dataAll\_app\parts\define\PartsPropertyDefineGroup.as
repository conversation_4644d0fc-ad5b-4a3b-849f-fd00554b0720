package dataAll._app.parts.define
{
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class PartsPropertyDefineGroup
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public var rarePro:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public function PartsPropertyDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var skillXML0:* = undefined;
         var fatherName0:String = null;
         var n:* = undefined;
         var d0:PartsPropertyDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            skillXML0 = fatherXML0[i].pro;
            fatherName0 = fatherXML0[i].@name;
            for(n in skillXML0)
            {
               d0 = new PartsPropertyDefine();
               d0.inData_byXML(skillXML0[n]);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
            }
         }
      }
      
      public function getDeifne(name0:String) : PartsPropertyDefine
      {
         return this.obj[name0];
      }
   }
}

