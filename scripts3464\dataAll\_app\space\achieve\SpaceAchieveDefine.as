package dataAll._app.space.achieve
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._base.NormalDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class SpaceAchieveDefine extends NormalDefine
   {
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var fun:String = "";
      
      public var gift:GiftAddDefine = new GiftAddDefine();
      
      public var info:String = "";
      
      public function SpaceAchieveDefine()
      {
         super();
      }
      
      public function get must() : Number
      {
         return this.CF.getAttribute("must");
      }
      
      public function set must(v0:Number) : void
      {
         this.CF.setAttribute("must",v0);
      }
      
      public function get extra() : Number
      {
         return this.CF.getAttribute("extra");
      }
      
      public function set extra(v0:Number) : void
      {
         this.CF.setAttribute("extra",v0);
      }
      
      public function fleshInfo() : void
      {
         this.info = String(this.info).replace("[must]",this.must);
         this.info = String(this.info).replace("[extra]",this.extra);
      }
      
      public function getText() : String
      {
         var s0:String = ComMethod.color(cnName,"#00CCFF",14);
         return s0 + ("\n" + this.info);
      }
      
      public function getGiftG() : GiftAddDefineGroup
      {
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         g0.addGift(this.gift);
         g0.cnName = "太空成就礼包";
         return g0;
      }
   }
}

