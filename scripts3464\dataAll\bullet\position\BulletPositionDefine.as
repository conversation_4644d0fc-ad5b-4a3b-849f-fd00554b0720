package dataAll.bullet.position
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.bullet.BulletDefine;
   import flash.geom.Point;
   
   public class BulletPositionDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var shootPoint:Point = new Point();
      
      public var bulletAngle:Number = -1000;
      
      public var shootAngle:Number = 0;
      
      public var shakeAngle:Number = 0;
      
      public var extendGap:int = 0;
      
      public var gatlinRange:Number = 0;
      
      public var gatlinNum:int = 0;
      
      public var bulletAngleRange:Number = 0;
      
      public function BulletPositionDefine()
      {
         super();
      }
      
      public function inData_byXML_BulletDefine(xml0:XML, d0:BulletDefine) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         for(n in pro_arr)
         {
            pro0 = pro_arr[n];
            if(String(xml0[pro0]) != "")
            {
               if(this[pro0] is Point)
               {
                  this[pro0] = ComMethod.getPoint(xml0[pro0]);
               }
               else
               {
                  this[pro0] = Number(xml0[pro0]);
               }
            }
            else
            {
               this[pro0] = d0[pro0];
            }
         }
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}

