package dataAll.items.creator
{
   import dataAll.items.save.ItemsSave;
   
   public class OneProData
   {
      
      public var type:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var value:Number = 0;
      
      public var valueString:String = "";
      
      public var lockB:Boolean = false;
      
      public var noChangeLockB:Boolean = false;
      
      public var save:ItemsSave;
      
      public var tipString:String = "";
      
      public function OneProData()
      {
         super();
      }
      
      public static function setLockByOther(arr0:Array, otherArr0:Array) : void
      {
         var otherDa0:OneProData = null;
         var da0:OneProData = null;
         for each(otherDa0 in otherArr0)
         {
            if(otherDa0.lockB)
            {
               for each(da0 in arr0)
               {
                  if(da0.name == otherDa0.name)
                  {
                     da0.lockB = true;
                  }
               }
            }
         }
      }
   }
}

