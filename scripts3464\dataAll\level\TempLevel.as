package dataAll.level
{
   import dataAll._app.task.TaskData;
   import dataAll.level.define.LevelDefine;
   
   public class TempLevel
   {
      
      private static var obj:Object = {};
      
      private static var db1:Array = ["僵尸王","游尸王","狂战尸","巨毒尸","暴君","钢铁僵尸王","飓风巫尸","无疆骑士","狂战狼","狂野收割者","野帝","毒魔","狂人机器","古飙","幻影X","斩之使者","异火龙","看门狗","球形闪电","冥王","末日坦克"];
      
      private static var db2:Array = ["守望者","异角龙","异祖龙","异齿虎","异猛象","虚空客","年兽","虚炎狼","虚洪螈","虚晶蝎","炸裂者","极速守卫","决斗者","王者兔","黑暗雷尔","幻影Z","骷髅巫师","阿巴达斯","电棍僵尸","多肉","掘地兽","爆骷S","运毒尸","古惑僵尸"];
      
      private static var db3:Array = ["防毒僵尸","科研僵尸","制图尸","会计尸","水管僵尸","战争狂人","女爵尸","卫队尸","关东尸","童灵尸","掘金尸","伏地尸","窃听者","哨兵","星锤干尸","战斗干尸","监狱僵尸","火炮尸狼","嗜血尸狼","独眼僵尸","吸血蝙蝠","银锤","霸王毒蛛","毒蛛","冥刃游尸","鬼目游尸","屠刀僵尸","肥胖僵尸","橄榄僵尸","携弹僵尸","无头自爆僵尸","战斗僵尸","鬼爵","鬼影战士","奇皇博士","天鹰小美","狂战射手","亚瑟","鬼目射手","天鹰空降兵","天鹰特种兵","火炮僵尸王","防暴僵尸","僵尸炮兵总管","僵尸空军总管","僵尸暴枪兵","僵尸狙击兵","僵尸空降兵","僵尸突击兵"];
      
      private static var dbArr:Array = [db1,db2,db3];
      
      public function TempLevel()
      {
         super();
      }
      
      public static function fixedLevel(taskLevelUrl0:String, normalLevelName0:String, lv0:int = 0, coverB0:Boolean = false) : LevelDefine
      {
         var d00:LevelDefine = null;
         var d22:LevelDefine = null;
         if(!coverB0)
         {
            if(getLevelDefine(taskLevelUrl0) is LevelDefine)
            {
               return null;
            }
         }
         var d0:LevelDefine = Gaming.defineGroup.level.getDefineUrl(taskLevelUrl0);
         var d2:LevelDefine = Gaming.defineGroup.level.getDefineUrl(normalLevelName0);
         if(Boolean(d0) && Boolean(d2))
         {
            d00 = d0.clone();
            d22 = d2.clone();
            d22.fixedBy(d00);
            d22.name = d00.name;
            if(lv0 != 0)
            {
               d22.info.enemyLv = lv0;
            }
            addInObj(d22,taskLevelUrl0);
            return d22;
         }
         return null;
      }
      
      public static function addInObj(d0:LevelDefine, name0:String) : void
      {
         obj[name0] = d0;
      }
      
      public static function getLevelDefine(name0:String) : LevelDefine
      {
         return obj[name0];
      }
      
      public static function delLevelDefineByName(name0:String) : void
      {
         if(obj.hasOwnProperty(name0))
         {
            obj[name0] = null;
         }
      }
      
      public static function clearData() : void
      {
         obj = {};
      }
      
      public static function test() : void
      {
         var xx:int = 0;
         var dg:int = 0;
      }
      
      public static function addArenaLevel(worldMapId0:String) : LevelDefine
      {
         var normalLevelName0:String = Gaming.defineGroup.worldMap.getDefine(worldMapId0).getLevelName();
         return fixedLevel("other/arena",normalLevelName0,0,true);
      }
      
      public static function getArenaLevel() : LevelDefine
      {
         return getLevelDefine("other/arena");
      }
      
      public static function addFederal(d0:LevelDefine) : void
      {
         addInObj(d0,"federal");
      }
      
      public static function getFederal() : LevelDefine
      {
         return getLevelDefine("federal");
      }
      
      public static function addEndLess(d0:LevelDefine) : void
      {
         addInObj(d0,"endlessFixed");
      }
      
      public static function getEndLess() : LevelDefine
      {
         return getLevelDefine("endlessFixed");
      }
      
      public static function addDoubleBoss() : void
      {
         var levelName0:String = null;
         var d0:LevelDefine = null;
         var levelBossArr:Array = null;
         var xmlStr0:String = null;
         var dayIndex0:int = 0;
         var i:int = 0;
         var xml0:XML = null;
         var now_d0:LevelDefine = null;
         var db0:Array = null;
         var len0:int = 0;
         var index0:int = 0;
         var bname0:String = null;
         var replace0:String = null;
         var name2:String = "activeTask/doubleBossTemp";
         if(getLevelDefine(name2) == null)
         {
            levelName0 = "doubleBoss";
            d0 = Gaming.defineGroup.level.getDefine(levelName0,"activeTask");
            levelBossArr = ["僵尸突击兵","火炮尸狼","末日坦克"];
            xmlStr0 = d0.originalXml.toXMLString();
            dayIndex0 = Gaming.PG.da.time.getReadTimeDate().reductionOneStr("2024-1-5");
            for(i = 0; i < 3; i++)
            {
               db0 = dbArr[i];
               len0 = int(db0.length);
               index0 = (dayIndex0 + len0 * 100) % len0;
               bname0 = db0[index0];
               if(bname0 != null)
               {
                  replace0 = levelBossArr[i];
                  xmlStr0 = xmlStr0.replace(replace0,bname0);
               }
            }
            xml0 = new XML(xmlStr0);
            now_d0 = new LevelDefine();
            now_d0.inData_byXML(xml0,"activeTask");
            TempLevel.addInObj(now_d0,name2);
         }
      }
      
      public static function addCustomTask(da0:TaskData) : void
      {
      }
   }
}

