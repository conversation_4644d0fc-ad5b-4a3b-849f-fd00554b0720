package dataAll.level.define.event
{
   import com.common.text.TextWay;
   
   public class LevelEventOrderDefine
   {
      
      public var xmlText:String = "";
      
      public var targetType:String = "";
      
      public var targetId:String = "";
      
      public var order:String = "";
      
      public var property:String = "";
      
      public var rectId:String = "";
      
      public function LevelEventOrderDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var arr2:Array = null;
         var t0:String = String(xml0);
         this.xmlText = t0;
         var arr0:Array = t0.split(";");
         var s1:String = TextWay.toHanSpace(arr0[0]);
         var s2:String = TextWay.toHanSpace(arr0[1]);
         var arr1:Array = TextWay.splitOne(s1,":");
         var type0:String = arr1[0];
         var value0:String = arr1[1];
         if(!value0)
         {
            value0 = "";
         }
         var s21:String = null;
         var s22:String = null;
         if(<PERSON><PERSON><PERSON>(s2))
         {
            arr2 = TextWay.splitOne(s2,":");
            s21 = arr2[0];
            if(!s21)
            {
               s21 = "";
            }
            s22 = arr2[1];
            if(!s22)
            {
               s22 = "";
            }
         }
         this.targetType = type0;
         var funName0:String = "pan_" + type0;
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](value0,s21,s22);
         }
         else
         {
            this.pan_default(value0,s21,s22);
         }
      }
      
      public function pan_default(value0:String, s21:String, s22:String) : void
      {
         this.targetId = value0;
         this.order = s21;
         this.property = s22;
      }
      
      public function pan_createUnit(value0:String, s21:String, s22:String) : void
      {
         this.order = this.targetType;
         this.property = value0;
         this.rectId = s21;
         this.targetId = s22;
      }
      
      public function pan_body(value0:String, s21:String, s22:String) : void
      {
         this.targetId = value0;
         this.order = s21;
         this.property = s22;
      }
      
      public function pan_obstacle(value0:String, s21:String, s22:String) : void
      {
         this.targetId = value0;
         this.order = s21;
         this.property = s22;
      }
   }
}

