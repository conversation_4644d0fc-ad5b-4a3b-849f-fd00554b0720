package dataAll.pet.creator
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.save.GeneSave;
   
   public class PetDataEvoCtrl
   {
      
      public function PetDataEvoCtrl()
      {
         super();
      }
      
      public static function getOpenLv() : int
      {
         return 75;
      }
      
      public static function evolution(da0:PetData) : void
      {
         var beforeGeneData0:GeneData = da0.gene;
         var newGeneData0:GeneData = getEvoGeneData(da0.gene);
         da0.setNewGeneData(newGeneData0);
         da0.PD.pet.fleshSaveGroup();
         if(da0.base.save.playerName == beforeGeneData0.save.getDefine().cnName)
         {
            da0.base.save.playerName = da0.getGeneDefine().cnName;
         }
      }
      
      private static function getEvoGeneData(da0:GeneData) : GeneData
      {
         var newSkillArr0:Array = null;
         var d0:GeneDefine = da0.save.getDefine();
         var d2:GeneDefine = d0.getEvoGeneDefine();
         var s2:GeneSave = da0.save.clone();
         s2.inDataByDefine(d2);
         if(d2.talentSkillArr.length > d0.talentSkillArr.length)
         {
            s2.talentSkillArr = d2.talentSkillArr;
         }
         else
         {
            newSkillArr0 = getAddSkillNameArr(s2.laterSkillArr.concat(s2.talentSkillArr),1);
            s2.laterSkillArr = s2.laterSkillArr.concat(newSkillArr0);
         }
         var da2:GeneData = new GeneData();
         da2.inData_bySave(s2,da0.normalPlayerData);
         return da2;
      }
      
      private static function getAddSkillNameArr(before0:Array, num0:int) : Array
      {
         var arr0:Array = Gaming.defineGroup.skill.petSkillNameArr;
         arr0 = ComMethod.deductArr(arr0,before0);
         return ComMethod.getRandomArray(arr0,num0);
      }
      
      public static function getMust(da0:PetData) : MustDefine
      {
         var d0:MustDefine = null;
         var mustArr0:Array = null;
         var geneD0:GeneDefine = da0.getGeneDefine();
         d0 = getMust4(da0);
         if(Boolean(d0))
         {
            return d0;
         }
         d0 = new MustDefine();
         d0.lv = getOpenLv();
         mustArr0 = geneD0.getMustStringArr(getMustNum());
         d0.inThingsDataByArr(mustArr0);
         return d0;
      }
      
      private static function getMustNum() : int
      {
         return 100;
      }
      
      private static function getMust4(da0:PetData) : MustDefine
      {
         var d0:MustDefine = null;
         var geneD0:GeneDefine = da0.getGeneDefine();
         var thingsArr0:Array = [];
         if(geneD0.name == "PetBoomSkullThird")
         {
            thingsArr0.push("wisdomGem;200");
            thingsArr0.push("fireGem;200");
            thingsArr0.push(geneD0.getBookThingsName() + ";400");
         }
         else if(geneD0.name == "PetIronChiefThird")
         {
            thingsArr0.push("defenceGem;150");
            thingsArr0.push("frozenGem;150");
            thingsArr0.push(geneD0.getBookThingsName() + ";400");
         }
         else if(geneD0.name == "PetLaer")
         {
            thingsArr0.push("electricGem;200");
            thingsArr0.push("poisonGem;200");
            thingsArr0.push(geneD0.getBookThingsName() + ";500");
         }
         if(thingsArr0.length > 0)
         {
            d0 = new MustDefine();
            d0.lv = 90;
            d0.inThingsDataByArr(thingsArr0);
            return d0;
         }
         return null;
      }
   }
}

