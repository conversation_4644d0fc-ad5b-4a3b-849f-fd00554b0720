package dataAll.skill.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.edit.TorData;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll.arms.define.ArmsType;
   import dataAll.pet.PetCount;
   
   public class SkillDefineGroup
   {
      
      private var fromXmlArr:Array = [];
      
      public var obj:Object = {};
      
      public var fatherObj:Object = {};
      
      private var fatherArrObj:Object = {};
      
      public var heroObj:Object = {};
      
      public var heroOriginalCnObj:Object = {};
      
      public var heroOriginalArr:Array = [];
      
      public var heroOriginalObj:Object = {};
      
      public var haveAddHeroOriginalObj:Object = {};
      
      public var armsSkillNameArr:Array = [];
      
      public var godArmsSkillNameArr:Array = [];
      
      public var petSkillNameArr:Array = [];
      
      public var askEffectFatherObj:Object = {};
      
      public var fatherCnNameObj:Object = {};
      
      private var editAgentObj:Object = {};
      
      public function SkillDefineGroup()
      {
         super();
      }
      
      public function init() : void
      {
         this.createAllHeroSkillDefine("heroSkill");
         this.createAllHeroSkillDefine("heroSkillLink");
         this.createAllHeroSkillDefine("petSkill");
         this.createAllHeroSkillDefine("petBodySkill");
         this.createAllHeroSkillDefine("deviceSkill");
         this.createAllHeroSkillDefine("vehicleSkill");
         this.createAllHeroSkillDefine("peakSkill");
         this.createAllHeroSkillDefine("partsSkill");
         this.createAllHeroSkillDefine("shield");
         this.createAllHeroSkillDefine("jewelry");
         this.createAllHeroSkillDefine("craft");
      }
      
      public function inData_byXML(xml0:XML, fleshDescriptionB0:Boolean = false) : void
      {
         var i:* = undefined;
         var skillXML0:* = undefined;
         var fatherName0:String = null;
         var fatherCnName0:String = null;
         var n:* = undefined;
         var oneXml0:XML = null;
         var d0:SkillDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            skillXML0 = fatherXML0[i].skill;
            fatherName0 = fatherXML0[i].@name;
            fatherCnName0 = fatherXML0[i].@cnName;
            this.fatherCnNameObj[fatherName0] = fatherCnName0;
            for(n in skillXML0)
            {
               oneXml0 = skillXML0[n];
               if(oneXml0.growth.length() > 0)
               {
                  d0 = new HeroSkillDefine();
               }
               else
               {
                  d0 = new SkillDefine();
               }
               d0.inData_byXML(oneXml0,fatherName0);
               this.obj[d0.name] = d0;
               this.fromXmlArr.push(d0);
               if(!this.fatherObj[fatherName0])
               {
                  this.fatherObj[fatherName0] = {};
                  this.fatherArrObj[fatherName0] = [];
               }
               this.fatherObj[fatherName0][d0.name] = d0;
               this.fatherArrObj[fatherName0].push(d0);
               if(fatherName0 == "heroSkill")
               {
                  this.heroOriginalArr.push(d0);
                  this.heroOriginalObj[d0.name] = d0;
                  this.heroOriginalCnObj[d0.cnName] = d0;
                  if((d0 as HeroSkillDefine).addD.pro != "")
                  {
                     this.haveAddHeroOriginalObj[d0.name] = d0;
                  }
                  this.addAskEffect(d0);
               }
               else if(fatherName0 == "armsSkill" || fatherName0 == "godArmsSkill")
               {
                  this.dealArmsSkill(d0,skillXML0[n],fatherName0);
                  if(!d0.noRandomListB)
                  {
                     if(fatherName0 == "armsSkill")
                     {
                        this.armsSkillNameArr.push(d0.name);
                     }
                     else if(fatherName0 == "godArmsSkill")
                     {
                        this.godArmsSkillNameArr.push(d0.name);
                     }
                  }
               }
               else if(fatherName0 == "petSkill" || fatherName0 == "petBodySkill")
               {
                  if(fatherName0 == "petSkill")
                  {
                     this.petSkillNameArr.push(d0.name);
                  }
                  if(d0 is HeroSkillDefine)
                  {
                     (d0 as HeroSkillDefine).studyMustMul = PetCount.getSkillStoneMul();
                     (d0 as HeroSkillDefine).upgradeMustMul = PetCount.getSkillStoneMul();
                     (d0 as HeroSkillDefine).coinMustMul = PetCount.getSkillCoinMul();
                  }
               }
               if(fleshDescriptionB0)
               {
               }
               if(!d0.isActiveB() && d0.condition == "avtiveSkillCdOver")
               {
                  INIT.showError("d0.isActiveB() && d0.condition == \'avtiveSkillCdOver\'");
               }
            }
         }
      }
      
      public function getSuperEnemySkillNameArr(enemyLv0:int, haveHideB0:Boolean = true) : Array
      {
         var i:* = undefined;
         var d0:SkillDefine = null;
         var obj0:Object = this.fatherObj["enemySuper"];
         var nameArr0:Array = [];
         for(i in obj0)
         {
            d0 = obj0[i];
            if(enemyLv0 >= d0.mustLv)
            {
               if(haveHideB0 || d0.name.indexOf("hiding") == -1)
               {
                  nameArr0.push(d0.name);
               }
            }
         }
         return nameArr0;
      }
      
      public function getFromXmlArr() : Array
      {
         return this.fromXmlArr;
      }
      
      public function getDefine(name0:String) : SkillDefine
      {
         return this.obj[name0];
      }
      
      public function getDefineByCn(cn0:String, father0:String) : SkillDefine
      {
         var d0:SkillDefine = null;
         var arr0:Array = this.getArrByFather(father0);
         for each(d0 in arr0)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getDefineBy(father0:String, name0:String) : SkillDefine
      {
         if(this.fatherObj[father0] is Object)
         {
            return this.fatherObj[father0][name0];
         }
         return null;
      }
      
      public function getArrByCnNameArr(arr0:Array) : Array
      {
         var n:* = undefined;
         var cnName0:String = null;
         var d0:HeroSkillDefine = null;
         var arr2:Array = [];
         for(n in arr0)
         {
            cnName0 = arr0[n];
            d0 = this.getHeroDefineByCn(cnName0);
            arr2.push(d0);
         }
         return arr2;
      }
      
      public function getArrByFather(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function getFatherCnName(f0:String) : String
      {
         return this.fatherCnNameObj[f0];
      }
      
      public function getFatherCnByArr(arr0:Array) : Array
      {
         var f0:String = null;
         var cnArr0:Array = [];
         for each(f0 in arr0)
         {
            cnArr0.push(this.getFatherCnName(f0));
         }
         return cnArr0;
      }
      
      public function getNameArrByFather(father0:String) : Array
      {
         var d0:SkillDefine = null;
         var arr0:Array = [];
         var obj0:Object = this.fatherObj[father0];
         if(Boolean(obj0))
         {
            for each(d0 in obj0)
            {
               arr0.push(d0.name);
            }
         }
         return arr0;
      }
      
      public function getAllEnemySkillStr() : String
      {
         var str0:String = "";
         str0 += "--------------------------------------------------------";
         str0 += "\n" + this.getOneEnemySkillStr("enemy",true);
         str0 += "\n" + this.getOneEnemySkillStr("enemySuper",true);
         return str0 + "\n--------------------------------------------------------";
      }
      
      private function getOneEnemySkillStr(father0:String, showInLifeBarB0:Boolean) : String
      {
         var n:* = undefined;
         var d0:SkillDefine = null;
         var obj0:Object = this.fatherObj[father0];
         var str0:String = "";
         for(n in obj0)
         {
            d0 = obj0[n];
            if(d0.showInLifeBarB || !showInLifeBarB0)
            {
               str0 += "【" + d0.cnName + "】" + d0.getDescription() + "\n";
            }
         }
         return str0;
      }
      
      private function createAllHeroSkillDefine(fatherName0:String) : void
      {
         var n:* = undefined;
         var d0:HeroSkillDefine = null;
         var arr0:Array = null;
         var i:* = undefined;
         var d2:HeroSkillDefine = null;
         var obj0:Object = this.fatherObj[fatherName0];
         var nowAddArr0:Array = [];
         for(n in obj0)
         {
            if(obj0[n] is HeroSkillDefine)
            {
               d0 = obj0[n];
               arr0 = d0.getGrowthArr();
               this.heroObj[d0.name] = arr0;
               nowAddArr0.push(arr0);
               for(i in arr0)
               {
                  d2 = arr0[i];
                  this.obj[d2.name] = d2;
               }
            }
         }
         for(n in nowAddArr0)
         {
            arr0 = nowAddArr0[n];
            for(i in arr0)
            {
               d2 = arr0[i];
               this.fatherObj[fatherName0][d2.name] = d2;
            }
         }
      }
      
      public function getHeroDefine(baseName0:String, lv0:int) : HeroSkillDefine
      {
         if(Boolean(this.heroObj[baseName0]))
         {
            return this.heroObj[baseName0][lv0 - 1];
         }
         return null;
      }
      
      public function getOriginalHeroDefine(baseName0:String) : HeroSkillDefine
      {
         return this.heroOriginalObj[baseName0];
      }
      
      public function getHeroDefineArr(baseName0:String) : Array
      {
         if(Boolean(this.heroObj[baseName0]))
         {
            return this.heroObj[baseName0];
         }
         return null;
      }
      
      public function getHeroDefineByCn(cnName0:String) : HeroSkillDefine
      {
         return this.heroOriginalCnObj[cnName0];
      }
      
      public function getRandomHeroSkillNameArr(num0:int) : Array
      {
         var n:* = undefined;
         var index0:int = 0;
         var d0:HeroSkillDefine = null;
         var nameArr0:Array = [];
         var r_arr0:Array = this.heroOriginalArr.concat([]);
         for(var i:int = 0; i < 8; i++)
         {
            if(r_arr0.length == 0)
            {
               break;
            }
            index0 = r_arr0.length * Math.random();
            d0 = r_arr0[index0];
            r_arr0.splice(index0,1);
            nameArr0.push(d0.name);
         }
         for(n in nameArr0)
         {
            nameArr0[n] += "_" + int(1 + Math.random() * 7);
         }
         return nameArr0;
      }
      
      public function getIconUrlByNameArr(arr0:Array) : Array
      {
         var n:* = undefined;
         var d0:HeroSkillDefine = null;
         var arr2:Array = [];
         for(n in arr0)
         {
            d0 = this.getDefine(arr0[n]) as HeroSkillDefine;
            if(d0 is HeroSkillDefine)
            {
               arr2.push(d0.iconUrl36);
            }
         }
         return arr2;
      }
      
      public function getArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            arr0.push(this.getDefine(name0));
         }
         return arr0;
      }
      
      public function getCnArrByNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            d0 = this.getDefine(name0);
            if(Boolean(d0))
            {
               cnArr0.push(d0.cnName);
            }
         }
         return cnArr0;
      }
      
      public function getCn(name0:*) : String
      {
         var d0:SkillDefine = this.getDefine(name0);
         if(Boolean(d0))
         {
            return d0.cnName;
         }
         return "";
      }
      
      public function getSkillNameArrByCnArr(cnArr0:Array, father0:String) : Array
      {
         var cn0:String = null;
         var d0:SkillDefine = null;
         var arr0:Array = [];
         for each(cn0 in cnArr0)
         {
            d0 = this.getDefineByCn(cn0,father0);
            if(Boolean(d0))
            {
               arr0.push(d0.name);
            }
         }
         return arr0;
      }
      
      private function dealArmsSkill(d0:SkillDefine, xml0:XML, father0:String) : void
      {
         var typeArr0:Array = null;
         var n:* = undefined;
         var type0:String = null;
         var mul0:Number = NaN;
         var newD0:SkillDefine = null;
         var pro0:String = d0.createByArmsTypePro;
         if(pro0 != "")
         {
            typeArr0 = ArmsType.TYPE_ARR;
            for(n in typeArr0)
            {
               type0 = typeArr0[n];
               mul0 = ArmsType.getHitProMul(type0);
               newD0 = new SkillDefine();
               newD0.inData_byXML(xml0,father0);
               newD0.name = d0.name + "_" + type0;
               if(pro0 == "effectProArr")
               {
                  newD0.effectProArr[0] = ComMethod.toFixed(newD0.effectProArr[0] * mul0,3);
               }
               this.obj[newD0.name] = newD0;
               this.fatherObj[father0][newD0.name] = newD0;
            }
         }
      }
      
      public function traceRanGodSkill() : void
      {
         var d0:SkillDefine = null;
         var arr0:Array = this.getArrByFather(SkillFather.godArmsSkill);
         for each(d0 in arr0)
         {
            if(d0.noRandomListB == false)
            {
               INIT.TRACE(d0.cnName + "：" + d0.getDescription());
            }
         }
      }
      
      public function getSkillNameArr_byFather(father0:String) : Array
      {
         var n:* = undefined;
         var d0:SkillDefine = null;
         var obj0:Object = this.fatherObj[father0];
         var arr0:Array = [];
         for(n in obj0)
         {
            d0 = obj0[n];
            arr0.push(d0.name);
         }
         return arr0;
      }
      
      public function getSkillCnNameArr_byFather(father0:String) : Array
      {
         var n:* = undefined;
         var d0:SkillDefine = null;
         var obj0:Object = this.fatherObj[father0];
         var arr0:Array = [];
         for(n in obj0)
         {
            d0 = obj0[n];
            arr0.push(d0.cnName);
         }
         return arr0;
      }
      
      public function showDescription(father0:String) : String
      {
         var d0:SkillDefine = null;
         var str0:String = "";
         var obj0:Object = this.fatherObj[father0];
         for each(d0 in obj0)
         {
            str0 += d0.cnName;
            str0 += "\n" + d0.getDescription(false) + "\n\n";
         }
         return str0;
      }
      
      public function getEquipSkillNameByCn(cn0:String) : String
      {
         var d0:SkillDefine = null;
         var obj2:Object = null;
         var d2:SkillDefine = null;
         var obj0:Object = this.fatherObj["headSkill"];
         for each(d0 in obj0)
         {
            if(d0.cnName == cn0)
            {
               return d0.name;
            }
         }
         obj2 = this.fatherObj["coatSkill"];
         for each(d2 in obj2)
         {
            if(d2.cnName == cn0)
            {
               return d2.name;
            }
         }
         return "";
      }
      
      private function addAskEffect(d0:SkillDefine) : void
      {
         var effect0:String = null;
         var arr0:Array = null;
         var fatherName0:String = d0.father;
         if(!this.askEffectFatherObj[fatherName0])
         {
            this.askEffectFatherObj[fatherName0] = {};
         }
         var obj0:Object = this.askEffectFatherObj[fatherName0];
         for each(effect0 in d0.effectInfoArr)
         {
            if(!obj0.hasOwnProperty(effect0))
            {
               obj0[effect0] = [];
            }
            arr0 = obj0[effect0];
            arr0.push(d0.cnName);
         }
      }
      
      public function getAskEffectArrObj(father0:String) : Object
      {
         var arr0:Array = [];
         if(this.askEffectFatherObj.hasOwnProperty(father0))
         {
            return this.askEffectFatherObj[father0];
         }
         return [];
      }
      
      public function getHaveAskEffectByFather(father0:String, effect0:String) : Array
      {
         if(this.askEffectFatherObj.hasOwnProperty(father0))
         {
            if(Boolean(this.askEffectFatherObj[father0].hasOwnProperty(effect0)))
            {
               return this.askEffectFatherObj[father0][effect0];
            }
            return [];
         }
         return [];
      }
      
      public function getNoAskEffectByFather(father0:String, effect0:String) : Array
      {
         var d0:SkillDefine = null;
         var bb0:Boolean = false;
         var cnArr0:Array = [];
         var obj0:Object = this.fatherObj[father0];
         for each(d0 in obj0)
         {
            bb0 = true;
            if(d0 is HeroSkillDefine)
            {
               bb0 = (d0 as HeroSkillDefine).lv == 1;
            }
            if(bb0)
            {
               if(d0.effectInfoArr.indexOf(effect0) == -1)
               {
                  cnArr0.push(d0.cnName);
               }
            }
         }
         return cnArr0;
      }
      
      public function getRandomAddDefineArr(lv0:int, num0:int) : Array
      {
         var arr0:Array = null;
         var d0:HeroSkillDefine = null;
         if(lv0 < 76)
         {
            return [];
         }
         arr0 = [];
         for each(d0 in this.haveAddHeroOriginalObj)
         {
            if(d0.addD.openLv <= lv0)
            {
               arr0.push(d0);
            }
         }
         return ComMethod.getRandomArray(arr0,num0);
      }
      
      public function getIconNum() : int
      {
         var d0:SkillDefine = null;
         var heroD0:HeroSkillDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            heroD0 = d0 as HeroSkillDefine;
            if(Boolean(heroD0))
            {
               if(!obj0.hasOwnProperty(heroD0.iconUrl))
               {
                  obj0[heroD0.iconUrl] = 0;
                  num0++;
               }
               if(!obj0.hasOwnProperty(heroD0.iconUrl36))
               {
                  obj0[heroD0.iconUrl36] = 0;
                  num0++;
               }
            }
         }
         return num0;
      }
      
      public function getEditAgent(proD0:EditProDefine, da0:TorData, noArr0:Array) : EditListAgent
      {
         var a0:EditListAgent = null;
         if(proD0.gather == EditProGather.arms)
         {
            a0 = this.getEditAgentByFather("armsSkill",noArr0,true);
         }
         return a0;
      }
      
      private function getEditAgentByFather(type0:String, noArr0:Array, noRandomListPanB0:Boolean = false) : EditListAgent
      {
         var fatherArr0:Array = null;
         var cnArr0:Array = null;
         var father0:String = null;
         var arr0:Array = null;
         var d0:SkillDefine = null;
         var a0:EditListAgent = this.editAgentObj[type0];
         if(Boolean(a0))
         {
            a0.clearFun();
         }
         else
         {
            a0 = new EditListAgent();
            fatherArr0 = [];
            if(type0 != "")
            {
               fatherArr0.push(type0);
            }
            cnArr0 = this.getFatherCnByArr(fatherArr0);
            a0.inTitle(fatherArr0,cnArr0);
            for each(father0 in fatherArr0)
            {
               arr0 = this.getArrByFather(father0);
               if(Boolean(arr0))
               {
                  for each(d0 in arr0)
                  {
                     if(noRandomListPanB0 == false || d0.noRandomListB == false)
                     {
                        a0.addDataLast(d0,father0);
                     }
                  }
               }
            }
            a0.cutPan();
            this.editAgentObj[type0] = a0;
         }
         a0.setNoLinkArr(noArr0);
         a0.createAllText();
         return a0;
      }
   }
}

