package dataAll.equip.vehicle
{
   import com.adobe.serialization.json.JSON2;
   import com.sounto.net.SWFLoaderManager;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.equip.define.EquipDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   
   public class VehicleDefineGroup
   {
      
      private var nowIndex:int = 0;
      
      public var obj:Object = {};
      
      public var fatherArrObj:Object = {};
      
      public var arr:Array = [];
      
      public var baseProperty:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public var bulletProperty:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public function VehicleDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var x0:XML = null;
         var father0:String = null;
         var xl2:XMLList = null;
         var x2:XML = null;
         var defineType0:String = null;
         var class0:Class = null;
         var d0:VehicleDefine = null;
         var xl0:XMLList = xml0.father;
         for each(x0 in xl0)
         {
            father0 = x0.@name;
            xl2 = x0.equip;
            for each(x2 in xl2)
            {
               defineType0 = VehicleDefineType.SHOOT;
               if(String(x2.@defineType) != "")
               {
                  defineType0 = x2.@defineType;
               }
               class0 = VehicleDefineType.getClass(defineType0);
               d0 = new class0();
               d0.defineType = defineType0;
               d0.inData_byXML(x2,father0);
               this.obj[d0.name] = d0;
               this.arr.push(d0);
               this.addInFatherArr(d0,father0);
            }
         }
      }
      
      public function inPropertyData_byXML(xml0:XML) : void
      {
         this.baseProperty.inData_byXML(xml0.base[0]);
         this.bulletProperty.inData_byXML(xml0.bullet[0]);
      }
      
      public function traceNameArr() : void
      {
         var d0:VehicleDefine = null;
         var arr0:Array = [];
         for each(d0 in this.obj)
         {
            arr0.push(d0.name);
         }
         trace(JSON2.encode(arr0));
      }
      
      private function addInFatherArr(d0:EquipDefine, father0:String) : void
      {
         d0.index = this.nowIndex;
         ++this.nowIndex;
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
         }
         this.fatherArrObj[father0].push(d0);
      }
      
      public function addInSwf(swfM0:SWFLoaderManager) : void
      {
         var d0:VehicleDefine = null;
         var bodyD0:NormalBodyDefine = null;
         var url0:String = null;
         for each(d0 in this.obj)
         {
            bodyD0 = d0.getBodyDefine();
            url0 = bodyD0.swfUrl;
            swfM0.addSWFLoader(url0,bodyD0.getSwfName(),"载具");
         }
      }
      
      public function getDefine(name0:String) : VehicleDefine
      {
         return this.obj[name0];
      }
      
      public function getCanComposeArr() : Array
      {
         var d0:VehicleDefine = null;
         var arr0:Array = [];
         for each(d0 in this.arr)
         {
            if(d0.canComposeB)
            {
               arr0.push(d0);
            }
         }
         return arr0;
      }
      
      public function getDefineByCn(cn0:String) : VehicleDefine
      {
         var d0:VehicleDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.cnName == cn0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getPropertyDefine(name0:String) : PropertyArrayDefine
      {
         var d0:PropertyArrayDefine = this.baseProperty.getDefine(name0);
         if(!(d0 is PropertyArrayDefine))
         {
            d0 = this.bulletProperty.getDefine(name0);
         }
         return d0;
      }
      
      public function getRandomName() : String
      {
         var d0:VehicleDefine = this.arr[int(Math.random() * this.arr.length)];
         return d0.name;
      }
      
      public function getBeforeDefine(name0:String) : VehicleDefine
      {
         var d0:VehicleDefine = null;
         for each(d0 in this.obj)
         {
            if(d0.evolutionLabel == name0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getIconNum() : int
      {
         var d0:EquipDefine = null;
         var obj0:Object = {};
         var num0:int = 0;
         for each(d0 in this.obj)
         {
            if(!obj0.hasOwnProperty(d0.iconLabel))
            {
               obj0[d0.iconLabel] = 0;
               num0++;
            }
         }
         return num0;
      }
      
      public function test() : void
      {
         var d0:VehicleDefine = null;
         for each(d0 in this.arr)
         {
            d0.test();
         }
      }
   }
}

