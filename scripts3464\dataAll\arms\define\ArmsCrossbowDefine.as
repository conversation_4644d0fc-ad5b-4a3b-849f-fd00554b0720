package dataAll.arms.define
{
   import com.sounto.utils.ClassProperty;
   
   public class ArmsCrossbowDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var focoB:Boolean = false;
      
      public var minDelayMul:Number = 0;
      
      public var vAtt:Number = 0;
      
      public function ArmsCrossbowDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function delayBreakB() : Boolean
      {
         return this.minDelayMul > 0 && this.vAtt > 0;
      }
   }
}

