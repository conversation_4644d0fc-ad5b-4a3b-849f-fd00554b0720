package dataAll.level.define.event
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   
   public class LevelEventConditionDefine
   {
      
      public static const CHOOSE_NO:String = "";
      
      public static const CHOOSE_RANDOM:String = "random";
      
      public static const CHOOSE_SEQUENCE:String = "sequence";
      
      public static const CHOOSE_RANDOM_ONE:String = "randomOne";
      
      public static const CHOOSE_SEQUENCE_ONE:String = "sequenceOne";
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var type:String = "";
      
      public var typeId:String = "";
      
      public var value:String = "";
      
      public var valueId:String = "";
      
      public var compareValue:Number = 0;
      
      public var compareType:String = "";
      
      public var bodyId:String = "";
      
      public var bodyEvent:String = "";
      
      public var rectId:String = "";
      
      public var eventId:String = "";
      
      public var doNumber:int = 1;
      
      public var extraText:String = "";
      
      public var orderChooseType:String = "";
      
      public function LevelEventConditionDefine()
      {
         super();
      }
      
      public function get delay() : Number
      {
         return this.CF.getAttribute("delay");
      }
      
      public function set delay(v0:Number) : void
      {
         this.CF.setAttribute("delay",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var arr2:Array = null;
         if(!xml0)
         {
            return;
         }
         this.delay = xml0.@delay;
         this.doNumber = int(xml0.@doNumber);
         if(this.doNumber == 0)
         {
            this.doNumber = 1;
         }
         this.extraText = String(xml0.@extraText);
         this.orderChooseType = String(xml0.@orderChooseType);
         var t0:String = String(xml0);
         if(t0 == "")
         {
            return;
         }
         var arr0:Array = t0.split(";");
         var s1:String = TextWay.toHanSpace(arr0[0]);
         var s2:String = TextWay.toHanSpace(arr0[1]);
         var arr1:Array = s1.split(":");
         var type0:String = arr1[0];
         var value0:String = arr1[1];
         var s21:String = null;
         var s22:String = null;
         if(Boolean(s2))
         {
            arr2 = s2.split(":");
            s21 = arr2[0];
            s22 = arr2[1];
         }
         this.type = type0;
         var funName0:String = "pan_" + type0;
         if(this.hasOwnProperty(funName0))
         {
            this[funName0](value0,s21,s22);
         }
         else
         {
            this.pan_default(value0,s21,s22);
         }
      }
      
      public function isDoOneOrderB() : Boolean
      {
         if(this.orderChooseType == CHOOSE_RANDOM_ONE || this.orderChooseType == CHOOSE_SEQUENCE_ONE)
         {
            return true;
         }
         return false;
      }
      
      public function pan_default(value0:String, s21:String, s22:String) : void
      {
         this.typeId = value0;
         this.value = s21;
         this.valueId = s22;
      }
      
      public function pan_enemyNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_mineNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_liveEnemyNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_frontEnemyNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_rightEnemyNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_weNumber(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
      }
      
      public function pan_hitMapRect(value0:String, s21:String, s22:String) : void
      {
         this.rectId = value0;
         this.bodyId = s21;
      }
      
      public function pan_affterDoLevelEvent(value0:String, s21:String, s22:String) : void
      {
         this.eventId = value0;
      }
      
      public function pan_affterDelLevelEvent(value0:String, s21:String, s22:String) : void
      {
         this.eventId = value0;
      }
      
      public function pan_bodyEvent(value0:String, s21:String, s22:String) : void
      {
         this.bodyEvent = value0;
         this.bodyId = s21;
      }
      
      public function pan_bodyGap(value0:String, s21:String, s22:String) : void
      {
         this.getCompareAll(value0);
         this.bodyId = s21;
         this.bodyEvent = s22;
      }
      
      private function getCompareAll(str0:String) : *
      {
         var arr0:Array = str0.split("_");
         var s0:String = arr0[0];
         if(arr0.length == 1)
         {
            this.compareType = "=";
            this.compareValue = Number(s0);
         }
         else
         {
            if(s0 == "less")
            {
               this.compareType = "<";
            }
            else if(s0 == "greater")
            {
               this.compareType = ">";
            }
            else
            {
               this.compareType = "=";
            }
            this.compareValue = Number(arr0[1]);
         }
      }
      
      public function checkNumber(num0:Number) : Boolean
      {
         if(this.compareType == "=")
         {
            return num0 == this.compareValue;
         }
         if(this.compareType == "<")
         {
            return num0 < this.compareValue;
         }
         if(this.compareType == ">")
         {
            return num0 > this.compareValue;
         }
         return false;
      }
   }
}

