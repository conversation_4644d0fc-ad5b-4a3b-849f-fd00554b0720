package dataAll.body.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class BodyExtraDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var lifeMin:Number = 1;
      
      public var skillArr:Array = [];
      
      public var stateArr:Array = [];
      
      public var eyeLight:String = "";
      
      public function BodyExtraDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

