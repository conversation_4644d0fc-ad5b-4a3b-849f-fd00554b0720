package dataAll._app.top
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.top.define.TopBarDefine;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.extra.ArenaTopExtra;
   import dataAll._app.top.extra.ArmsTopExtra;
   import dataAll._app.top.extra.BossEditTopExtra;
   import dataAll._app.top.extra.DpsTopExtra;
   import dataAll._app.top.extra.TopExtra;
   import dataAll._app.top.extra.UnionBattleExtra;
   import dataAll._player.PlayerData;
   import dataAll.test.ZuoBiMax;
   
   public class TopBarData
   {
      
      public static var pro_arr:Array = [];
      
      public var index:int = 0;
      
      public var uid:String = "";
      
      public var userName:String = "";
      
      public var uname:String = "";
      
      public var score:Number = 0;
      
      private var topScore:Number = 0;
      
      public var rank:Number = 0;
      
      public var extra:String = "";
      
      public var extraObj:TopExtra;
      
      private var nor:int = 0;
      
      public var define:TopBarDefineGroup = null;
      
      public function TopBarData()
      {
         super();
      }
      
      public static function getExtraObjByType(objType0:String) : TopExtra
      {
         var obj0:TopExtra = new TopExtra();
         if(objType0 == "dps")
         {
            obj0 = new DpsTopExtra();
         }
         else if(objType0 == "arms")
         {
            obj0 = new ArmsTopExtra();
         }
         else if(objType0 == "arena")
         {
            obj0 = new ArenaTopExtra();
         }
         else if(objType0 == "unionBattle")
         {
            obj0 = new UnionBattleExtra();
         }
         else if(objType0 == "bossEdit")
         {
            obj0 = new BossEditTopExtra();
         }
         return obj0;
      }
      
      public static function getArenaCode(uid0:String, index0:int, uname0:String) : String
      {
         return Base64.encodeString(uid0 + "_" + index0 + "," + uname0);
      }
      
      public function inData_byObj(obj0:Object, dg0:TopBarDefineGroup) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
         this.topScore = this.score;
         if(obj0.hasOwnProperty("uId"))
         {
            this.uid = obj0["uId"];
         }
         if(Boolean(dg0))
         {
            this.define = dg0;
            if(obj0.hasOwnProperty("score"))
            {
               this.score = Math.round(obj0["score"] * dg0.scoreMul);
            }
            this.fleshExtraObj();
         }
      }
      
      public function inData_byObjNoFlesh(obj0:Object, dg0:TopBarDefineGroup) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
         this.topScore = this.score;
         if(obj0.hasOwnProperty("uId"))
         {
            this.uid = obj0["uId"];
         }
         if(Boolean(dg0))
         {
            this.define = dg0;
         }
      }
      
      public function hideTextB() : Boolean
      {
         var m2:String = null;
         if(ZuoBiMax.hideTextPan(this.uid))
         {
            return true;
         }
         if(Boolean(this.extraObj))
         {
            if(this.nor == 0)
            {
               if(Gaming.isLocal())
               {
                  this.nor = 1;
               }
               else
               {
                  m2 = TopExtra.getMByUidScore(this.uid,this.topScore);
                  if(m2 == this.extraObj.m)
                  {
                     this.nor = 1;
                  }
                  else
                  {
                     this.nor = -1;
                  }
               }
            }
            return this.nor == -1;
         }
         return false;
      }
      
      public function getTopBarSave() : TopBarSave
      {
         if(!this.define)
         {
            INIT.showError("找不到TopBarDefineGroup");
         }
         var s0:TopBarSave = new TopBarSave();
         s0.inData_byObj(this);
         s0.topDefineName = this.define.name;
         return s0;
      }
      
      public function fleshExtraObj(fleshUnameB0:Boolean = true) : void
      {
         var obj0:TopExtra = null;
         if(this.extra != "" && Boolean(this.extra))
         {
            obj0 = getExtraObjByType(this.define.objType);
            obj0.inExtraStr(this.extra);
            this.extraObj = obj0;
            if(fleshUnameB0)
            {
               this.fleshUname();
            }
         }
      }
      
      private function fleshExtra() : void
      {
         this.extra = JSON2.encode(this.extraObj);
      }
      
      private function fleshUname() : void
      {
         if(this.extraObj is ArenaTopExtra)
         {
            this.uname = (this.extraObj as ArenaTopExtra).uname;
         }
      }
      
      private function getProByUrl(url0:String) : *
      {
         var f0:String = null;
         var n0:String = null;
         if(this.hideTextB() && url0 != "rank")
         {
            return "****";
         }
         var v0:* = null;
         var index0:int = int(url0.indexOf("."));
         if(index0 == -1)
         {
            f0 = url0;
            v0 = this[f0];
         }
         else
         {
            f0 = url0.substring(0,index0);
            n0 = url0.substring(index0 + 1);
            v0 = this[f0][n0];
         }
         if(n0 == "player" || n0 == "color")
         {
            v0 = StringMethod.converNumerStr(v0 as String);
         }
         else if(v0 is Number || v0 is int)
         {
            if(n0 != "")
            {
               if(v0 > 99999999999)
               {
                  v0 = "*****";
               }
               else
               {
                  v0 = NumberMethod.toShortLimit(v0 as Number);
               }
            }
         }
         return v0;
      }
      
      public function getStringArr() : Array
      {
         var n:* = undefined;
         var d0:TopBarDefine = null;
         var pro0:* = undefined;
         var d_arr0:Array = this.define.arr;
         var n_arr0:Array = [];
         for(n in d_arr0)
         {
            d0 = d_arr0[n];
            pro0 = this.getProByUrl(d0.proUrl);
            n_arr0.push(String(pro0));
         }
         return n_arr0;
      }
      
      public function setToSimulated(objType0:String) : void
      {
         this.uid = "123456";
         this.index = 0;
         this.uname = "sountoone";
         this.score = 321;
         this.extraObj = getExtraObjByType(objType0);
         this.extraObj.setToSimulated();
         this.fleshExtra();
      }
      
      public function toString() : String
      {
         var pro0:String = null;
         var s0:String = this.rank + "   ";
         var proArr0:Array = ["uname","uid","score","extra"];
         var cnArr0:Array = ["用户名","UID","积分","其他"];
         var index0:int = 0;
         for each(pro0 in proArr0)
         {
            s0 += cnArr0[index0] + ":" + this[pro0] + "，";
            index0++;
         }
         return s0;
      }
      
      public function haveRandData() : Boolean
      {
         return this.rank > 0;
      }
      
      public function getPCGId() : String
      {
         return this.uid + "_" + this.index;
      }
      
      public function setToNoRank(score0:Number, dg0:TopBarDefineGroup, pd0:PlayerData) : void
      {
         this.rank = 10001;
         this.score = score0;
         this.define = dg0;
         this.extra = pd0.getTopUploadData(dg0).extra;
         this.fleshExtraObj(false);
      }
      
      public function inDataByArenaCode(str0:String) : void
      {
         var code0:String = Base64.decodeString(str0);
         var str_arr0:Array = code0.split(",");
         var str_arr1:Array = str_arr0[0].split("_");
         var uid0:String = str_arr1[0];
         var index0:int = int(str_arr1[1]);
         var uname0:String = str_arr0[1];
         this.uid = uid0;
         this.index = index0;
         this.uname = uname0;
      }
      
      public function getNowArenaCode() : String
      {
         return getArenaCode(this.uid,this.index,this.uname);
      }
      
      public function isChallengedB() : Boolean
      {
         return Gaming.PG.da.arena.save.panUidAndUname(this.uid,this.uname);
      }
   }
}

