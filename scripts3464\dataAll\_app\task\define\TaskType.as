package dataAll._app.task.define
{
   import com.common.data.Base64;
   import com.sounto.utils.StringMethod;
   import dataAll.equip.define.EquipType;
   
   public class TaskType
   {
      
      public static const LEVEL:String = "level";
      
      public static const MAIN:String = "main";
      
      public static const DAY:String = "day";
      
      public static const DEPUTY:String = "deputy";
      
      public static const EXTRA:String = "extra";
      
      public static const KING:String = "king";
      
      public static const TREASURE:String = "treasure";
      
      public static const SPREAD:String = "spread";
      
      public static const BWALL:String = "bwall";
      
      public static const CUSTOM:String = "custom";
      
      public static const WEEK:String = "week";
      
      public static const ACTIVE:String = "active";
      
      public static const MEMORY:String = "memory";
      
      public static const labelArr:Array = [DAY,TREASURE,EXTRA,KING,MAIN,MEMORY,DEPUTY,SPREAD,BWALL,CUSTOM,WEEK,ACTIVE];
      
      public static const labelCnArr:Array = ["每日","寻宝","副本","擒王","主线","回忆","支线","外传","破壁","反客","每周","活动"];
      
      public static const aiArr:Array = [DAY,TREASURE,EXTRA,KING,MEMORY,WEEK,ACTIVE];
      
      public static const dayTypeArr:Array = [DAY,TREASURE,KING,EXTRA,WEEK,ACTIVE];
      
      public static const dayClearTypeArr:Array = [TREASURE,KING,EXTRA,ACTIVE];
      
      public static const weekClearTypeArr:Array = [DAY,WEEK];
      
      public static const diffTipArr:Array = [MAIN,MEMORY,SPREAD,BWALL,CUSTOM];
      
      public static const directArr:Array = [DAY,TREASURE,EXTRA];
      
      public static const autoUnlockArr:Array = [MAIN,MEMORY,SPREAD,BWALL,CUSTOM];
      
      public static const autoUnlockMapArr:Array = [MAIN];
      
      public static const noLowerArr:Array = [MAIN,MEMORY,SPREAD,BWALL,CUSTOM];
      
      public static const specialArr:Array = [MAIN,MEMORY,SPREAD,BWALL,DEPUTY,WEEK];
      
      public static const regetArr:Array = [MEMORY,DEPUTY,CUSTOM,SPREAD,BWALL];
      
      public static const showNewArr:Array = [MAIN,ACTIVE];
      
      private static var weekMulList_base64:String = Base64.encodeObject([1,2,5,10]);
      
      private static var weekKeyMustList_base64:String = Base64.encodeObject([1,1.5,2,3]);
      
      public function TaskType()
      {
         super();
      }
      
      private static function getWeekMulList() : Array
      {
         return Base64.decodeObject(weekMulList_base64) as Array;
      }
      
      private static function getWeekKeyMustList() : Array
      {
         return Base64.decodeObject(weekKeyMustList_base64) as Array;
      }
      
      public static function upWeekExtraDropIndex(index0:int) : int
      {
         var list0:Array = getWeekMulList();
         index0++;
         if(index0 > list0.length - 1)
         {
            index0 = 0;
         }
         return index0;
      }
      
      public static function getWeekMulByIndex(index0:int) : Number
      {
         var list0:Array = getWeekMulList();
         return list0[index0];
      }
      
      public static function getWeekKeyMustByIndex(index0:int) : Number
      {
         var list0:Array = getWeekKeyMustList();
         return list0[index0];
      }
      
      public static function getGiftMul(type0:String) : Number
      {
         if(type0 == DAY)
         {
            return 6;
         }
         if(type0 == TREASURE)
         {
            return 4;
         }
         if(type0 == EXTRA)
         {
            return 3;
         }
         return 1;
      }
      
      public static function getPetOpen_M() : int
      {
         return 88;
      }
      
      public static function getWeaponOpen_M() : int
      {
         return getEquipTypeUnlockLv(EquipType.WEAPON);
      }
      
      public static function getDeviceOpen_M() : int
      {
         return getEquipTypeUnlockLv(EquipType.DEVICE);
      }
      
      public static function getVehicleOpen_M() : int
      {
         return getEquipTypeUnlockLv(EquipType.VEHICLE);
      }
      
      public static function getPropsOpen_M() : int
      {
         return 1;
      }
      
      public static function getEquipTypeUnlockLv(partType0:String) : int
      {
         if(partType0 == EquipType.COAT)
         {
            return 78;
         }
         if(partType0 == EquipType.PANTS)
         {
            return 95;
         }
         if(partType0 == EquipType.HEAD)
         {
            return 92;
         }
         if(partType0 == EquipType.BELT)
         {
            return 82;
         }
         if(partType0 == EquipType.WEAPON)
         {
            return 55;
         }
         if(partType0 == EquipType.DEVICE)
         {
            return 45;
         }
         if(partType0 == EquipType.JEWELRY)
         {
            return 100;
         }
         if(partType0 == EquipType.SHIELD)
         {
            return 65;
         }
         if(partType0 == EquipType.FASHION)
         {
            return 85;
         }
         if(partType0 == EquipType.VEHICLE)
         {
            return 75;
         }
         return 100;
      }
      
      public static function getEquipTypeUnlockText(taskLv0:int) : String
      {
         var part0:String = null;
         var unlockLv0:int = 0;
         var parr0:Array = EquipType.TYPE_ARR;
         var arr0:Array = [];
         for each(part0 in parr0)
         {
            unlockLv0 = getEquipTypeUnlockLv(part0);
            if(taskLv0 >= unlockLv0)
            {
               arr0.push(EquipType.getCnName(part0));
            }
         }
         return StringMethod.concatStringArr(arr0,99);
      }
      
      public static function getHeroLife(lv0:int) : Number
      {
         var mul0:Number = 50;
         var v0:Number = Gaming.defineGroup.normal.getEnemyDps(lv0);
         if(lv0 <= 100)
         {
            if(lv0 >= 45)
            {
               mul0 = 90;
            }
            else if(lv0 >= 35)
            {
               mul0 = 60;
            }
         }
         return v0 * mul0;
      }
      
      public static function getPartsUnlock() : int
      {
         return 70;
      }
      
      public static function canPartsB() : Boolean
      {
         return Gaming.LG.getMemoryLvLimit() >= getPartsUnlock();
      }
      
      public static function getSkillUnlockNum(taskLv0:int) : int
      {
         if(taskLv0 >= 100)
         {
            return -1;
         }
         if(taskLv0 >= 99)
         {
            return 10;
         }
         if(taskLv0 >= 80)
         {
            return 10;
         }
         if(taskLv0 >= 75)
         {
            return 9;
         }
         if(taskLv0 >= 70)
         {
            return 8;
         }
         if(taskLv0 >= 60)
         {
            return 7;
         }
         if(taskLv0 >= 50)
         {
            return 6;
         }
         if(taskLv0 >= 40)
         {
            return 5;
         }
         if(taskLv0 >= 30)
         {
            return 4;
         }
         if(taskLv0 >= 20)
         {
            return 3;
         }
         if(taskLv0 >= 10)
         {
            return 2;
         }
         if(taskLv0 >= 5)
         {
            return 1;
         }
         return 0;
      }
   }
}

