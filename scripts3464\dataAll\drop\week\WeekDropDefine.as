package dataAll.drop.week
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll.drop.BodyDropData;
   import dataAll.gift.define.GiftAddDefine;
   
   public class WeekDropDefine
   {
      
      private static const OBJ:Object = {};
      
      private static const bossObj:Object = {};
      
      private static const dayObj:Object = {};
      
      private static const dayBossObj:Object = {};
      
      public var gift:GiftAddDefine = new GiftAddDefine();
      
      public var maxArr:Array = null;
      
      public var proMul:Number = 1;
      
      public var dropArr:Array = null;
      
      public var boss:String = "";
      
      public var map:String = "";
      
      public var lv:int = 0;
      
      public var mapMode:String = "";
      
      public var allMax:Number = 0;
      
      public function WeekDropDefine()
      {
         super();
      }
      
      public static function afterDefine() : void
      {
         initDayDefine();
         var d0:WeekDropDefine = null;
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;intensDrug;1");
         d0.dropArr = [9,10,11,12,13,14,15,16,17,18];
         d0.setMaxByProMul(5);
         d0.boss = "MinersZombie";
         d0.map = "XiShan1";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;falconGun;1");
         d0.dropArr = [13,14,16,17,19,20,22,23,24,25];
         d0.setMaxByProMul(5);
         d0.boss = "HealerZombie";
         d0.map = "XiShan2";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;extremeGun;1");
         d0.dropArr = [13,14,16,17,19,20,22,23,24,25];
         d0.setMaxByProMul(5);
         d0.boss = "ClawsZombie";
         d0.map = "XiShan3";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;yaStone;1");
         d0.dropArr = [6,7,8,9,10,11,12,13,14,15];
         d0.setMaxByProMul(5);
         d0.boss = "DoubleZombie";
         d0.map = "XiShan4";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;yaRock;1");
         d0.dropArr = [8,9,10,11,12,13,14,15,16,17];
         d0.setMaxByProMul(3);
         d0.boss = "WorldSnake";
         d0.map = "XiShan5";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;consVirgo;1");
         d0.dropArr = [15,16,17,18,19,20,22,23,24,25];
         d0.setMaxByProMul(3);
         d0.boss = "Madgod";
         d0.map = "XingPeak";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         addDefine(d0);
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;ghostStone;1");
         d0.dropArr = [7,8,9,10,11,12,13,14,15,16];
         d0.setMaxByProMul(1);
         d0.boss = "BaseballZombie";
         d0.map = "GreenIs1";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         d0.allMax = 200;
         addDefine(d0);
      }
      
      public static function getOBJ() : Object
      {
         return OBJ;
      }
      
      public static function getBossArr(bossName0:String) : Array
      {
         return bossObj[bossName0];
      }
      
      private static function addDefine(d0:WeekDropDefine) : void
      {
         var arr0:Array = null;
         OBJ[d0.name] = d0;
         if(d0.boss != "")
         {
            arr0 = ObjectMethod.getEleAndAdd(bossObj,d0.boss,Array) as Array;
            arr0.push(d0);
         }
      }
      
      private static function initDayDefine() : void
      {
         var d0:WeekDropDefine = null;
         d0 = new WeekDropDefine();
         d0.gift.inData_byStr("things;timeCapsule_1;1");
         d0.dropArr = [6,7,8,9,10,11,12,13,14,15];
         d0.setMaxByProMul(1);
         d0.boss = "CrossBone";
         d0.map = "GreenTown1";
         d0.lv = 99;
         d0.mapMode = MapMode.REGULAR;
         d0.allMax = 300;
         addDayDefine(d0);
      }
      
      public static function getDayObj() : Object
      {
         return dayObj;
      }
      
      public static function getDayBossArr(bossName0:String) : Array
      {
         return dayBossObj[bossName0];
      }
      
      private static function addDayDefine(d0:WeekDropDefine) : void
      {
         var arr0:Array = null;
         dayObj[d0.name] = d0;
         if(d0.boss != "")
         {
            arr0 = ObjectMethod.getEleAndAdd(dayBossObj,d0.boss,Array) as Array;
            arr0.push(d0);
         }
      }
      
      public function get name() : String
      {
         return this.gift.name;
      }
      
      public function setMaxByProMul(mul0:Number) : void
      {
         var arr0:Array = null;
         var pro0:Number = NaN;
         var max0:int = 0;
         if(Boolean(this.dropArr))
         {
            this.proMul = mul0;
            arr0 = [];
            for each(pro0 in this.dropArr)
            {
               max0 = Math.round(pro0 * mul0);
               arr0.push(max0);
            }
            this.maxArr = arr0;
         }
      }
      
      public function getDropPro(bd0:BodyDropData, lg0:IO_PlayerLevelGetter) : Number
      {
         return this.getDropProBy(lg0.getDiff(),bd0.define.name,bd0.bodyLevel,lg0.getMapMode(),lg0.getNowWorldMapName());
      }
      
      public function getDropProBy(diff0:int, boss0:String, lv0:int, mapMode0:String, map0:String) : Number
      {
         if(this.mapMode != "")
         {
            if(this.mapMode != mapMode0)
            {
               return 0;
            }
         }
         if(this.lv > 0)
         {
            if(lv0 != this.lv)
            {
               return 0;
            }
         }
         if(this.boss != "")
         {
            if(this.boss != boss0)
            {
               return 0;
            }
         }
         if(this.map != "")
         {
            if(this.map != map0)
            {
               return 0;
            }
         }
         return this.getDropProByDiff(diff0);
      }
      
      public function getDropProByDiff(diff0:int) : Number
      {
         return ArrayMethod.getElementLimit(this.dropArr,diff0);
      }
      
      public function getMax(diff0:int) : Number
      {
         var owe0:Number = NaN;
         var v0:Number = ArrayMethod.getElementLimit(this.maxArr,diff0);
         if(this.boss == "DoubleZombie")
         {
            if(Boolean(Gaming.PG.da))
            {
               owe0 = Gaming.PG.da.main.save.getOweNuclearStone();
               if(owe0 > 0)
               {
                  v0 -= owe0;
                  if(v0 < 0)
                  {
                     v0 = 0;
                  }
               }
            }
         }
         return v0;
      }
   }
}

