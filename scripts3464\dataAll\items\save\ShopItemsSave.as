package dataAll.items.save
{
   import com.sounto.cf.Sounto64;
   
   public class ShopItemsSave extends LockItemsSave
   {
      
      private var _addLevel:String = "";
      
      public function ShopItemsSave()
      {
         super();
         this.addLevel = 0;
      }
      
      public function set addLevel(v0:Number) : void
      {
         this._addLevel = Sounto64.encode(String(v0));
      }
      
      public function get addLevel() : Number
      {
         return Number(Sounto64.decode(this._addLevel));
      }
      
      override public function getAddLevel() : int
      {
         return this.addLevel;
      }
      
      public function setBuyPrice(v0:Number) : void
      {
      }
   }
}

