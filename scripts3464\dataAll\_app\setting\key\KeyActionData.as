package dataAll._app.setting.key
{
   import com.common.text.TextWay;
   
   public class KeyActionData
   {
      
      public var name:String = "";
      
      public var def:KeyActionDefine;
      
      public var valueArr:Array = [];
      
      public function KeyActionData()
      {
         super();
      }
      
      public static function getCnNameArrByDataArr(arr0:Array) : Array
      {
         var da0:KeyActionData = null;
         var cnNameArr0:Array = [];
         for each(da0 in arr0)
         {
            cnNameArr0.push(da0.def.cnName);
         }
         return cnNameArr0;
      }
      
      public static function getCnName(keyCode0:int) : String
      {
         return Gaming.defineGroup.keyAction.getCnByKetCode(keyCode0);
      }
      
      public function getString() : String
      {
         var v0:int = 0;
         var arr0:Array = [];
         for each(v0 in this.valueArr)
         {
            arr0.push(getCnName(v0));
         }
         return TextWay.mixedStringArr(arr0,9," / ");
      }
      
      public function getOneValue() : int
      {
         if(this.valueArr.length > 0)
         {
            return this.valueArr[0];
         }
         return 0;
      }
      
      public function getValue(index0:int) : int
      {
         if(this.valueArr.length > 0)
         {
            return this.valueArr[index0];
         }
         return 0;
      }
      
      public function getBtnActived() : Boolean
      {
         return this.getOneValue() > 0;
      }
   }
}

