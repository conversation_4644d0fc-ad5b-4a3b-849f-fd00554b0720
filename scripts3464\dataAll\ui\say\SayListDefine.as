package dataAll.ui.say
{
   public class SayListDefine
   {
      
      public var father:String = "";
      
      public var levelName:String = "";
      
      public var name:String = "";
      
      public var arr:Array = [];
      
      public var oneB:Boolean = false;
      
      public function SayListDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, level0:String, father0:String) : void
      {
         var n:* = undefined;
         var say_xml0:XML = null;
         var d0:SayDefine = null;
         this.father = father0;
         this.levelName = level0;
         this.name = xml0.@name;
         this.oneB = Boolean(int(xml0.@oneB));
         var numObj0:Object = {};
         var xmlList0:XMLList = xml0.say;
         for(n in xmlList0)
         {
            say_xml0 = xmlList0[n];
            d0 = new SayDefine();
            d0.inData_byXML(say_xml0);
            this.arr.push(d0);
            if(!numObj0.hasOwnProperty(d0.target))
            {
               numObj0[d0.target] = 0;
            }
            ++numObj0[d0.target];
         }
         this.fleshPointer(numObj0);
      }
      
      public function getId() : String
      {
         return this.levelName + "_" + this.name;
      }
      
      private function fleshPointer(obj0:Object) : void
      {
         var n:* = undefined;
         var d0:SayDefine = null;
         var max0:String = this.findMaxInObj(obj0);
         obj0[max0] = null;
         var max1:String = this.findMaxInObj(obj0);
         for(n in this.arr)
         {
            d0 = this.arr[n];
            if(d0.pointer == "")
            {
               if(d0.target != max0)
               {
                  d0.pointer = max0;
               }
               else if(d0.target != max1)
               {
                  d0.pointer = max1;
               }
            }
         }
      }
      
      private function findMaxInObj(obj0:Object) : String
      {
         var i:* = undefined;
         var max0:String = "";
         for(i in obj0)
         {
            if(obj0[i] != null)
            {
               if(max0 == "")
               {
                  max0 = i;
               }
               else if(obj0[i] > obj0[max0])
               {
                  max0 = i;
               }
            }
         }
         return max0;
      }
   }
}

