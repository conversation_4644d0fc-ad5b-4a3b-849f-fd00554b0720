package dataAll.things.define
{
   public class ThingsName
   {
      
      public static const bossSumCard:String = "bossSumCard";
      
      public static const lifeBottle:String = "lifeBottle";
      
      public static const teamLifeBottle:String = "teamLifeBottle";
      
      public static const caisson:String = "caisson";
      
      public static const teamRebirthCard:String = "teamRebirthCard";
      
      public static const skillFleshCard:String = "skillFleshCard";
      
      public static var gameWorldPropsNameArr:Array = [lifeBottle,teamLifeBottle,caisson,teamRebirthCard,skillFleshCard];
      
      public static const petBookArr:Array = ["PetBoomSkullBook","PetIronChiefBook","PetLakeBook"];
      
      public static const downUIArr:Array = ["lifeBottle","teamLifeBottle","caisson","teamRebirthCard","skillFleshCard"];
      
      public function ThingsName()
      {
         super();
      }
   }
}

