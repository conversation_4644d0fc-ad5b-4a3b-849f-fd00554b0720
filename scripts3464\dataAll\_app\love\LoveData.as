package dataAll._app.love
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldCodeCF;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.love.define.LoveLevelDefine;
   import dataAll._app.love.define.LoveRoleAll;
   import dataAll._app.love.define.LoveTalkDefine;
   import dataAll._app.task.define.TaskType;
   import dataAll._player.PlayerData;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll._player.time.TimeData;
   import dataAll.body.define.BodySex;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.things.define.ThingsDefine;
   import gameAll.drop.bodyDrop.FoolDayBodyDrop;
   import gameAll.hero.HeroBody;
   import gameAll.level.data.LevelData;
   
   public class LoveData implements IO_HavePlayerData
   {
      
      private var codeCF:OldCodeCF = new OldCodeCF();
      
      private var playerData:PlayerData;
      
      private var meData:NormalPlayerData;
      
      public var save:LoveSave;
      
      private var likeName:String = "";
      
      private var hateName:String = "";
      
      private var haveShowNameArr:Array = [];
      
      public var nowLoveAddValue:int = 0;
      
      private var firstTalkB:Boolean = false;
      
      private var beforeIsShowB:Boolean = true;
      
      private var beforeIsDieB:Boolean = false;
      
      private var weekNoGiftB:Boolean = false;
      
      private var cn:String = "";
      
      private var sex:String = "";
      
      private var defAll:LoveRoleAll;
      
      private var roleName:String = "";
      
      public function LoveData()
      {
         super();
         this.nowThingsName = "";
      }
      
      public static function getReductLove() : Number
      {
         return 16000;
      }
      
      public function get nowThingsName() : String
      {
         return this.codeCF.getAttribute("nowThingsName");
      }
      
      public function set nowThingsName(str0:String) : void
      {
         this.codeCF.setAttribute("nowThingsName",str0);
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.meData = pd0;
         this.playerData = pd0.getMainPlayerData();
         this.roleName = pd0.getRoleName();
         this.defAll = Gaming.defineGroup.love.getRole(this.roleName);
         this.sex = pd0.heroData.def.sex;
         this.cn = pd0.heroData.def.getLoveCn(pd0.isP1());
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         this.setPlayerData(pd0);
      }
      
      public function inData_bySave(s0:LoveSave) : void
      {
         this.save = s0;
         if(this.isOpenB())
         {
            if(this.save.talkArr.length == 0)
            {
               this.save.initSave();
               this.newDayCtrl("",false);
            }
            if(this.getLikeName() == "")
            {
               this.randomLikeHate();
            }
         }
      }
      
      public function newDayCtrl(timeStr0:String, timePanB0:Boolean = true) : void
      {
         if(this.isOpenB())
         {
            this.likeName = "";
            this.hateName = "";
            this.haveShowNameArr.length = 0;
            this.beforeIsDieB = false;
            this.weekNoGiftB = false;
            this.beforeIsShowB = true;
            this.save.newDayCtrl(this.defAll,this.playerData);
            this.randomLikeHate();
            if(timePanB0)
            {
               this.timePan(timeStr0);
            }
         }
      }
      
      public function setReadSaveTime(timeData0:TimeData) : void
      {
         if(this.isOpenB())
         {
            if(this.save.fashionAddTime == "")
            {
               this.save.fashionAddTime = timeData0.getReadTime();
            }
         }
      }
      
      private function timePan(timeStr0:String) : void
      {
         var faddDay0:int = 0;
         var add0:int = 0;
         var fadd0:int = this.playerData.moreWay.get_dayLoveAdd();
         if(fadd0 > 0)
         {
            faddDay0 = StringDate.compareDateByStr(this.save.fashionAddTime,timeStr0);
            if(faddDay0 > 1000 || faddDay0 < 0)
            {
               faddDay0 = 0;
            }
            add0 += faddDay0 * fadd0;
         }
         this.save.fashionAddTime = timeStr0;
         var cday0:int = this.getNoGetGiftDayNum();
         var noDeductLoveB0:Boolean = this.playerData.post.isNoDeductLoveB();
         if(cday0 > 7 && !noDeductLoveB0)
         {
            this.weekNoGiftB = true;
            add0 += -100;
            this.fleshBeforeGetGiftTime();
         }
         this.addValue(add0);
      }
      
      private function randomLikeHate() : void
      {
         var likeSave0:LoveLikeSave = this.save.likeHateArr[0];
         this.likeName = likeSave0.getLikeRandomName();
         this.hateName = likeSave0.getHateRandomName();
         this.firstTalkB = true;
      }
      
      public function addValue(v0:int) : void
      {
         this.save.addValue(v0,this.defAll,this.playerData);
      }
      
      public function getValue() : int
      {
         return this.save.value;
      }
      
      public function getCn() : String
      {
         return this.cn;
      }
      
      public function getRoleCn() : String
      {
         return this.meData.getCnName();
      }
      
      public function getSex() : String
      {
         return this.sex;
      }
      
      public function isMaleB() : Boolean
      {
         return this.sex == BodySex.MALE;
      }
      
      public function getLoveRoleAll() : LoveRoleAll
      {
         return this.defAll;
      }
      
      public function hadGivingB() : Boolean
      {
         return this.save.allGivingNum > 0;
      }
      
      public function isOpenB() : Boolean
      {
         return RoleName.loveOpenArr.indexOf(this.roleName) >= 0;
      }
      
      public function getLovePer() : Number
      {
         return this.save.value / this.save.getMaxLoveValue();
      }
      
      public function getLoveStr() : String
      {
         if(this.isOpenB())
         {
            return this.save.value + "";
         }
         return "未开放";
      }
      
      public function getOverTip() : String
      {
         var gift0:GiftAddDefineGroup = null;
         var str0:String = "";
         if(this.isOpenB())
         {
            if(this.meData.isP1() == false)
            {
               str0 += "" + this.meData.heroData.def.cnName + "对你的" + this.cn + "：<yellow " + this.save.value + "/>/" + this.save.getMaxLoveValue() + "\n\n";
            }
            gift0 = this.getCanGetGift();
            if(gift0.haveDataB())
            {
               str0 += "可领取奖励：\n" + gift0.getDescription();
            }
            else if(this.save.getGiftAllB())
            {
               str0 += "今日已领取奖励。";
            }
         }
         return str0;
      }
      
      public function getLikeName() : String
      {
         if(this.save.likeName == "")
         {
            return this.likeName;
         }
         return this.save.likeName;
      }
      
      public function getHateName() : String
      {
         if(this.save.hateName == "")
         {
            return this.hateName;
         }
         return this.save.hateName;
      }
      
      private function likeHateInSave() : void
      {
         this.save.likeName = this.getLikeName();
         this.save.hateName = this.getHateName();
      }
      
      public function getNowThingsMustDefine() : MustDefine
      {
         var d0:MustDefine = null;
         var thingsD0:ThingsDefine = null;
         var num0:int = 0;
         if(this.nowThingsName == "")
         {
            return null;
         }
         d0 = new MustDefine();
         thingsD0 = Gaming.defineGroup.things.getDefine(this.nowThingsName);
         if(thingsD0.isPartsB())
         {
            return null;
         }
         num0 = thingsD0.loveD.getNum(this.playerData.level);
         if(num0 > 0)
         {
            d0.inThingsDataByArr([this.nowThingsName + ";" + num0]);
            return d0;
         }
         return null;
      }
      
      public function clearNowThings() : void
      {
         this.nowThingsName = "";
      }
      
      private function getTalkNameArr() : Array
      {
         var arr0:Array = this.save.talkArr.concat([]);
         if(this.firstTalkB)
         {
            this.firstTalkB = false;
         }
         else
         {
            arr0 = arr0.concat([this.getLikeName(),this.getHateName()]);
         }
         return arr0.concat(this.getReduceLoveTalkNameArr());
      }
      
      public function getReduceLoveTalkNameArr() : Array
      {
         var loveAll0:LoveRoleAll = this.getLoveRoleAll();
         var arr0:Array = [];
         if(!this.beforeIsShowB)
         {
            arr0.push(loveAll0.getRandomNameArrByType("noShow",1)[0]);
         }
         if(this.beforeIsDieB)
         {
            arr0.push(loveAll0.getRandomNameArrByType("die",1)[0]);
         }
         if(this.weekNoGiftB)
         {
            arr0.push(loveAll0.getRandomNameArrByType("noGiftOneWeek",1)[0]);
         }
         return arr0;
      }
      
      public function getRandomTalkDefine() : LoveTalkDefine
      {
         var otherD0:LoveTalkDefine = FoolDayBodyDrop.getLoveTalkDefine(this.roleName);
         if(Boolean(otherD0))
         {
            return otherD0;
         }
         var nameArr0:Array = this.getTalkNameArr();
         if(this.haveShowNameArr.length < nameArr0.length)
         {
            nameArr0 = ComMethod.deductArr(nameArr0,this.haveShowNameArr);
         }
         else
         {
            this.haveShowNameArr.length = 0;
         }
         var name0:String = nameArr0[int(Math.random() * nameArr0.length)];
         this.haveShowNameArr.push(name0);
         if(name0 == this.getHateName() || name0 == this.getLikeName())
         {
            this.likeHateInSave();
         }
         return this.getLoveRoleAll().getTalkDefineByName(name0);
      }
      
      public function getBeforeGiftTalkDefine() : LoveTalkDefine
      {
         var type0:String = "";
         if(this.save.likeName == "")
         {
            type0 = "beforeGift3";
         }
         else
         {
            type0 = "beforeGift";
         }
         var talkName0:String = this.getLoveRoleAll().getRandomNameArrByType(type0,1)[0];
         return this.getLoveRoleAll().getTalkDefineByName(talkName0);
      }
      
      public function overGift() : LoveTalkDefine
      {
         var loveAll0:LoveRoleAll = null;
         loveAll0 = this.getLoveRoleAll();
         var addValue0:int = 0;
         var nowThingsD0:ThingsDefine = Gaming.defineGroup.things.getDefine(this.nowThingsName);
         var likeTalkD0:LoveTalkDefine = loveAll0.getTalkDefineByName(this.getLikeName());
         var hateTalkD0:LoveTalkDefine = loveAll0.getTalkDefineByName(this.getHateName());
         var talkType0:String = "gift1";
         if(likeTalkD0.panThingsCnName(nowThingsD0.cnName))
         {
            addValue0 = 150;
            if(this.save.likeName == "")
            {
               addValue0 *= 4;
               talkType0 = "gift300";
            }
            else
            {
               talkType0 = "gift100";
            }
            this.likeHateInSave();
            ++this.save.likeGiftNum;
         }
         else if(hateTalkD0.panThingsCnName(nowThingsD0.cnName))
         {
            addValue0 = -100;
            talkType0 = "gift_50";
            ++this.save.hateGiftNum;
         }
         else
         {
            addValue0 = 20;
            talkType0 = "gift1";
         }
         addValue0 = Math.ceil(addValue0 * (Math.random() * 0.4 + 0.8));
         addValue0 += this.playerData.getDropMerge().loveAdd;
         if(addValue0 > 0 && Math.random() <= 0.02)
         {
            addValue0 *= 3;
         }
         var setB0:Boolean = this.getSetLoveThingsB(nowThingsD0.name);
         if(setB0)
         {
            addValue0 += nowThingsD0.effectD.value;
            talkType0 = "gift300";
         }
         this.addValue(addValue0);
         this.save.useGivingNum();
         ++this.save.allGivingNum;
         this.fleshBeforeGetGiftTime();
         this.nowLoveAddValue = addValue0;
         this.weekNoGiftB = false;
         loveAll0 = this.getLoveRoleAll();
         var talkName0:String = loveAll0.getRandomNameArrByType(talkType0,1)[0];
         return loveAll0.getTalkDefineByName(talkName0);
      }
      
      private function getSetLoveThingsB(name0:String) : Boolean
      {
         if(this.roleName == RoleName.Girl)
         {
            if(name0 == "whiteRose")
            {
               return true;
            }
         }
         else if(this.roleName == RoleName.XinLing)
         {
            if(name0 == "lily")
            {
               return true;
            }
         }
         else if(this.roleName == RoleName.XiaoMei)
         {
            if(name0 == "teddyBear")
            {
               return true;
            }
         }
         else if(this.roleName == RoleName.WenJie || this.roleName == RoleName.ZangShi)
         {
            if(name0 == "soldiersStamp")
            {
               return true;
            }
         }
         return false;
      }
      
      private function fleshBeforeGetGiftTime() : void
      {
         this.save.beforeGetGiftTimeStr = this.playerData.getSave().time.nowReadTime;
      }
      
      public function overLevel(dat0:LevelData) : void
      {
         var hero0:HeroBody = null;
         var haveFashionB0:Boolean = false;
         var md0:MoreData = null;
         if(!this.isOpenB())
         {
            return;
         }
         var levelD0:LoveLevelDefine = this.defAll.getLevelDefineByValue(this.save.value);
         var isMainTaskB:Boolean = dat0.getNowTaskFather() == TaskType.MAIN;
         var showB0:Boolean = false;
         var dieB0:Boolean = false;
         var mda0:MoreData = this.meData.heroData;
         if(mda0 is MoreData)
         {
            hero0 = mda0.DATA.hero;
            if(hero0 is HeroBody)
            {
               showB0 = true;
               if(hero0.getDie() > 0)
               {
                  dieB0 = true;
               }
            }
         }
         if(dat0.levelTime < 15)
         {
            showB0 = true;
         }
         if(dieB0 && this.save.value > getReductLove() && !isMainTaskB)
         {
            haveFashionB0 = false;
            md0 = this.meData.heroData;
            if(md0 is MoreData)
            {
               if(Boolean(md0.DATA.equip.getDataBySaveName("kagura")))
               {
                  haveFashionB0 = true;
               }
            }
            if(!haveFashionB0)
            {
               this.addValue(-2);
               ++this.save.dieNum;
            }
         }
         this.beforeIsShowB = showB0;
         this.beforeIsDieB = dieB0;
      }
      
      private function getNoGetGiftDayNum() : int
      {
         var nowDa0:StringDate = null;
         var beforeDa0:StringDate = new StringDate(this.save.beforeGetGiftTimeStr);
         if(beforeDa0.fullYear < 2014)
         {
            return 0;
         }
         nowDa0 = this.playerData.time.getReadTimeDate();
         return nowDa0.reductionOne(beforeDa0);
      }
      
      public function getOpenLevelDefineArr() : Array
      {
         return this.getLoveRoleAll().getLevelDefineArrByValue(this.save.value);
      }
      
      public function getCanGetGift() : GiftAddDefineGroup
      {
         var d0:LoveLevelDefine = null;
         var larr0:Array = this.getOpenLevelDefineArr();
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var lvArr0:Array = [];
         for each(d0 in larr0)
         {
            if(this.save.getGiftB(d0) == false && d0.gift.haveDataB())
            {
               g0.merge(d0.gift);
               lvArr0.push(d0);
            }
         }
         g0.setExtraArr(lvArr0);
         return g0;
      }
      
      public function getSkillLabelArr(heroName0:String = "") : Array
      {
         var d0:LoveLevelDefine = null;
         var name0:String = null;
         var skillD0:SkillDefine = null;
         var bb0:Boolean = false;
         var skillArr0:Array = [];
         var arr0:Array = this.getOpenLevelDefineArr();
         for each(d0 in arr0)
         {
            for each(name0 in d0.skillNameArr)
            {
               skillD0 = Gaming.defineGroup.skill.getDefine(name0);
               bb0 = false;
               if(heroName0 == skillD0.valueString)
               {
                  skillArr0.push(name0);
               }
            }
         }
         return skillArr0;
      }
      
      public function getWeaponOpen(heroName0:String) : Boolean
      {
         var d0:LoveLevelDefine = null;
         if(heroName0 == RoleName.Striker)
         {
            return true;
         }
         var arr0:Array = this.getOpenLevelDefineArr();
         for each(d0 in arr0)
         {
            if(d0.appLabel == "girlWeapon")
            {
               if(heroName0 == RoleName.Girl)
               {
                  return true;
               }
            }
            else if(d0.appLabel == "otherWeapon")
            {
               if(heroName0 != RoleName.Girl)
               {
                  return true;
               }
            }
         }
         return false;
      }
   }
}

