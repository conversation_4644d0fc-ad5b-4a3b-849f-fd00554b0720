package dataAll._app.peak
{
   import UI.base.must.ExpendUIAgent;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.TextMethod;
   import dataAll.equip.add.IO_EquipAddGetter;
   
   public class PeakData implements IO_EquipAddGetter, IO_PointData
   {
      
      public var save:PeakSave;
      
      private var uiUpgradeTip:String = "";
      
      public function PeakData()
      {
         super();
      }
      
      private static function getWholeDpsMustNum(num0:int) : int
      {
         if(num0 > 15)
         {
            return 25;
         }
         return 15;
      }
      
      public function inData_bySave(s0:PeakSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
      }
      
      public function get level() : int
      {
         return this.save.lv;
      }
      
      public function setLevel(lv0:int) : void
      {
         this.save.lv = lv0;
      }
      
      public function get dayExp() : Number
      {
         return this.save.dayExp;
      }
      
      public function get exp() : Number
      {
         return this.save.exp;
      }
      
      public function swapPlan(label0:String) : Boolean
      {
         return this.save.swapPlan(label0);
      }
      
      public function getProAddObj() : Object
      {
         var n:* = undefined;
         var d0:PeakProDefine = null;
         var lv0:int = 0;
         var add0:Number = NaN;
         var obj0:Object = {};
         var vObj0:Object = this.save.getNow().getEncodeObj();
         for(n in vObj0)
         {
            d0 = Gaming.defineGroup.peakPro.getDefine(n);
            if(Boolean(d0))
            {
               if(d0.isProB())
               {
                  lv0 = this.getOneLv(d0.name);
                  if(lv0 > 0)
                  {
                     add0 = d0.getProAdd(lv0);
                     obj0[d0.pro] = add0;
                  }
               }
            }
         }
         obj0["dpsWhole"] = this.getWholeDpsAdd();
         return obj0;
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      public function getSkillLabelArr() : Array
      {
         var n:* = undefined;
         var d0:PeakProDefine = null;
         var lv0:int = 0;
         var name0:String = null;
         var arr0:Array = [];
         var vObj0:Object = this.save.getNow().getEncodeObj();
         for(n in vObj0)
         {
            d0 = Gaming.defineGroup.peakPro.getDefine(n);
            if(!d0.isProB())
            {
               lv0 = this.getOneLv(d0.name);
               if(lv0 > 0)
               {
                  name0 = d0.getLevelSkillName(lv0);
                  arr0.push(name0);
               }
            }
         }
         return arr0;
      }
      
      public function usePoint(name0:String) : void
      {
         this.save.getNow().addNum(name0,1);
      }
      
      public function delPoint(name0:String) : void
      {
         var num0:int = this.save.getNow().getAttribute(name0);
         num0 -= 1;
         if(num0 < 0)
         {
            num0 = 0;
         }
         this.save.getNow().setAttribute(name0,num0);
      }
      
      public function resetPoint() : void
      {
         this.save.getNow().clearData();
      }
      
      public function setPoint(name0:String, v0:int) : void
      {
         this.save.getNow().setAttribute(name0,v0);
      }
      
      public function getAllPoint() : int
      {
         return this.save.lv;
      }
      
      public function getSurplusPoint() : int
      {
         return this.getAllPoint() - this.getUsePoint();
      }
      
      public function getUsePoint() : int
      {
         var n:* = undefined;
         var sum0:int = 0;
         var obj0:Object = this.save.getNow().getEncodeObj();
         for(n in obj0)
         {
            sum0 += this.getUsePointOne(n);
         }
         return sum0;
      }
      
      public function getUsePointOne(name0:String) : Number
      {
         var d0:PeakProDefine = null;
         var lv0:int = this.save.getNow().getAttribute(name0);
         if(lv0 > 0)
         {
            d0 = Gaming.defineGroup.peakPro.getDefine(name0);
            return d0.getMustPointSum(lv0);
         }
         return 0;
      }
      
      public function getMustPointOne(name0:String) : Number
      {
         var lv0:int = this.save.getNow().getAttribute(name0);
         var d0:PeakProDefine = Gaming.defineGroup.peakPro.getDefine(name0);
         return d0.getMustPointByLv(lv0 + 1);
      }
      
      public function getOneLv(name0:String) : int
      {
         return int(this.save.getNow().getAttribute(name0));
      }
      
      public function getUITipAndShow() : String
      {
         var s0:String = this.uiUpgradeTip;
         this.uiUpgradeTip = "";
         return s0;
      }
      
      public function addExp(v0:Number) : Boolean
      {
         var level0:int = 0;
         var level2:int = 0;
         var maxLevel0:int = 0;
         var max0:Number = NaN;
         var now0:Number = NaN;
         var over0:Number = NaN;
         var upgradeB0:Boolean = false;
         v0 = Math.round(v0);
         var canDay0:Number = PeakSave.MAX_DAY_EXP - this.save.dayExp;
         if(v0 > canDay0)
         {
            v0 = canDay0;
         }
         if(v0 > 0)
         {
            level0 = this.save.lv;
            if(level0 < 1)
            {
               level0 = 1;
            }
            level2 = level0;
            maxLevel0 = PeakSave.MAX_LV;
            max0 = this.getMaxExp(level0);
            now0 = this.save.exp;
            over0 = 0;
            upgradeB0 = false;
            if(level0 >= maxLevel0)
            {
               now0 += v0;
               if(now0 > max0)
               {
                  over0 = now0 - max0 + 1;
                  now0 = max0 - 1;
               }
               this.save.exp = now0;
            }
            else
            {
               now0 += v0;
               while(now0 >= max0)
               {
                  now0 -= max0;
                  level0++;
                  if(level0 >= maxLevel0)
                  {
                     break;
                  }
                  max0 = this.getMaxExp(level0);
               }
               if(level0 >= maxLevel0)
               {
                  if(now0 > max0)
                  {
                     over0 = now0 - max0 + 1;
                     now0 = max0 - 1;
                  }
               }
               this.save.exp = now0;
               if(level0 > level2)
               {
                  this.save.lv = level0;
                  upgradeB0 = true;
                  this.uiUpgradeTip = "巅峰等级升至" + TextMethod.color(level0 + "级","#FFFF00") + "！";
               }
            }
            this.save.dayExp += v0 - over0;
         }
         return upgradeB0;
      }
      
      public function getNowMaxExp() : int
      {
         var lv0:int = this.save.lv;
         if(lv0 < 0)
         {
            lv0 = 0;
         }
         return this.getMaxExp(lv0);
      }
      
      private function getMaxExp(lv0:int) : int
      {
         return Gaming.defineGroup.dataList.getValue("peakExp",lv0);
      }
      
      public function canWholeDpsB() : Boolean
      {
         return this.save.lv >= PeakSave.WHOLE_DPS_LV;
      }
      
      public function getWholeDpsAdd() : Number
      {
         return this.save.dpN * PeakSave.WHOLE_DPS_ONE;
      }
      
      public function getWholeDpsMustAgent(yesFun0:Function) : ExpendUIAgent
      {
         var a0:ExpendUIAgent = new ExpendUIAgent();
         var num0:int = getWholeDpsMustNum(this.save.dpN);
         a0.must.inThingsDataByArr(["demStone;" + num0,"madheart;" + num0]);
         a0.yesFun = yesFun0;
         a0.btnCn = "提升";
         if(this.save.dpN >= PeakSave.MAX_WHOLE_DPS_NUM)
         {
            a0.info = "已到达加成最大值";
            a0.btnActived = false;
         }
         else if(this.canWholeDpsB())
         {
            if(this.save.dpDay >= PeakSave.WHOLE_DPS_DAY)
            {
               a0.info = "今日次数已用完";
               a0.btnActived = false;
            }
            else
            {
               a0.info = "提升全体战斗力";
               a0.btnActived = true;
            }
            a0.info += "(" + ComMethod.dropColor(this.save.dpDay,PeakSave.WHOLE_DPS_DAY) + ")";
         }
         else
         {
            a0.info = "巅峰等级不足" + PeakSave.WHOLE_DPS_LV + "级";
            a0.btnActived = false;
         }
         return a0;
      }
      
      public function wholeDpsEvent() : void
      {
         ++this.save.dpN;
         ++this.save.dpDay;
      }
   }
}

