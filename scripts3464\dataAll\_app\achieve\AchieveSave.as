package dataAll._app.achieve
{
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.achieve.define.AchieveDefine;
   
   public class AchieveSave
   {
      
      public static var pro_arr:Array = [];
      
      private static const ZERO:AchieveSave = new AchieveSave();
      
      public var name:String = "";
      
      public var state:String = "no";
      
      public var info:String = "";
      
      public var infoValue:Number = 0;
      
      public var gB:Boolean = false;
      
      public var wearB:Boolean = false;
      
      public var wearIndex:Number = 0;
      
      public function AchieveSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inData_byDefine(d0:AchieveDefine) : void
      {
         this.name = d0.name;
      }
      
      public function getSaveObj(oldObj0:Object) : Object
      {
         var obj0:Object = ClassProperty.thinSaveObj(this,ZERO,pro_arr);
         obj0.name = this.name;
         if(Gaming.isLocal())
         {
            this.checkSaveObj(oldObj0,obj0);
         }
         return obj0;
      }
      
      private function checkSaveObj(oldObj0:Object, obj0:Object) : Boolean
      {
         var s0:AchieveSave = new AchieveSave();
         s0.inData_byObj(obj0);
         var a:Object = ClassProperty.copyObj(s0);
         var b:Object = ClassProperty.copyObj(oldObj0);
         var bb0:Boolean = ObjectMethod.samePan(a,b);
         if(bb0 == false)
         {
            INIT.showError("成就 " + this.name + "：检测异常不同");
         }
         return bb0;
      }
   }
}

