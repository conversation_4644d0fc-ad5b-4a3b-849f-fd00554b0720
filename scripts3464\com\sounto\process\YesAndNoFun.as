package com.sounto.process
{
   public class YesAndNoFun
   {
      
      protected var yes_fun:Function = null;
      
      protected var yesFunObj:Object = {};
      
      protected var no_fun:Function = null;
      
      protected var noFunObj:Object = {};
      
      public function YesAndNoFun()
      {
         super();
      }
      
      public function setFun(yesFun0:Function, noFun0:Function = null, target0:String = "") : void
      {
         if(target0 == "")
         {
            this.yes_fun = yesFun0;
            this.no_fun = noFun0;
         }
         else
         {
            this.yesFunObj[target0] = yesFun0;
            this.noFunObj[target0] = noFun0;
         }
      }
      
      public function doYesFun(data0:*, target0:String = "") : void
      {
         var fun0:Function = null;
         if(target0 == "")
         {
            fun0 = this.yes_fun;
            this.yes_fun = null;
         }
         else
         {
            fun0 = this.yesFunObj[target0];
            this.yesFunObj[target0] = null;
         }
         if(fun0 is Function)
         {
            fun0(data0);
         }
      }
      
      public function doNoFun(data0:*, target0:String = "") : void
      {
         var fun0:Function = null;
         if(target0 == "")
         {
            fun0 = this.no_fun;
            this.no_fun = null;
         }
         else
         {
            fun0 = this.noFunObj[target0];
            this.noFunObj[target0] = null;
         }
         if(fun0 is Function)
         {
            fun0(data0);
         }
      }
      
      public function clear() : void
      {
         this.yes_fun = null;
         this.yesFunObj = {};
         this.no_fun = null;
         this.noFunObj = {};
      }
   }
}

