package dataAll.skill.define.otherObj
{
   import com.sounto.utils.ClassProperty;
   
   public class SkillBulletObj
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var nameArr:Array = [];
      
      public var site:String = "";
      
      public var minYB:Boolean = false;
      
      public var flipB:Boolean = false;
      
      public var ranX:Number = 0;
      
      public var ranY:Number = 0;
      
      public var launcherB:Boolean = false;
      
      public var followB:Boolean = false;
      
      public var focoB:Boolean = false;
      
      public var producerShootB:Boolean = false;
      
      public function SkillBulletObj()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function getNameArr() : Array
      {
         var arr0:Array = this.nameArr;
         if(this.name != "")
         {
            arr0 = arr0.concat([this.name]);
         }
         return arr0;
      }
   }
}

