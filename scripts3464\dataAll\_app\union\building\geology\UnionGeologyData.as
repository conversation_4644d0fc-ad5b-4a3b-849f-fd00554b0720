package dataAll._app.union.building.geology
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.union.building.UnionBuildingData;
   import dataAll._app.union.building.define.UnionGeologyDefine;
   import dataAll._app.union.building.define.UnionGeologyThingsDefine;
   
   public class UnionGeologyData
   {
      
      public var buildData:UnionBuildingData = null;
      
      public var thingsObj:Object = {};
      
      public var thingsArr:Array = [];
      
      public var save:UnionGeologySave;
      
      public var def:UnionGeologyDefine;
      
      public var countArr:Array = [];
      
      public function UnionGeologyData()
      {
         super();
      }
      
      public function inData_bySave(s0:UnionGeologySave, buildData0:UnionBuildingData) : void
      {
         var ts0:UnionGeologyThingsSave = null;
         var da0:UnionGeologyThingsData = null;
         this.save = s0;
         this.buildData = buildData0;
         this.thingsObj = {};
         this.def = Gaming.defineGroup.union.building.getGeologyDefineByLv(buildData0.save.lv);
         for each(ts0 in s0.thingsObj)
         {
            da0 = new UnionGeologyThingsData();
            da0.inData_bySave(ts0,this.def.getThings(ts0.name));
            this.thingsObj[ts0.name] = da0;
         }
         this.fleshArr();
      }
      
      public function newDayCtrl() : void
      {
         var da0:UnionGeologyThingsData = null;
         for each(da0 in this.thingsObj)
         {
            da0.newDayCtrl();
         }
         this.save.haveTime = 0;
      }
      
      public function startLevel() : void
      {
         this.fleshDefineByLv();
      }
      
      public function fleshDefineByLv() : void
      {
         var da0:UnionGeologyThingsData = null;
         this.def = Gaming.defineGroup.union.building.getGeologyDefineByLv(this.buildData.save.lv);
         for each(da0 in this.thingsObj)
         {
            da0.setDefine(this.def.getThings(da0.save.name));
         }
      }
      
      private function fleshArr() : void
      {
         var d0:UnionGeologyThingsDefine = null;
         var da0:UnionGeologyThingsData = null;
         this.countArr.length = 0;
         this.thingsArr.length = 0;
         var defineArr0:Array = Gaming.defineGroup.union.building.geologyBase.arr;
         for each(d0 in defineArr0)
         {
            da0 = this.getThingsData(d0.name);
            if(Boolean(da0))
            {
               if(da0.def.isCountB())
               {
                  this.countArr.push(da0);
               }
            }
            this.thingsArr.push(da0);
         }
      }
      
      public function getThingsData(name0:String) : UnionGeologyThingsData
      {
         return this.thingsObj[name0];
      }
      
      private function getAllTime() : Number
      {
         return 45 * 60;
      }
      
      private function getSurplusTime() : Number
      {
         var v0:Number = this.getAllTime() - this.save.haveTime;
         if(v0 < 0)
         {
            v0 = 0;
         }
         return v0;
      }
      
      public function getSurplusTimeMin() : String
      {
         var v0:Number = this.getSurplusTime();
         var min0:int = Math.ceil(v0 / 60);
         return min0 + "分钟";
      }
      
      public function getSurplusTimeSecond() : String
      {
         var v0:Number = this.getSurplusTime();
         return ComMethod.getTimeStrTwo(v0);
      }
      
      public function isTimeOverB() : Boolean
      {
         return this.getSurplusTime() <= 0;
      }
      
      public function addThingsNum(name0:String, num0:int = 1) : void
      {
         var da0:UnionGeologyThingsData = this.getThingsData(name0);
         if(Boolean(da0))
         {
            da0.save.now += num0;
         }
      }
      
      public function isOverB() : Boolean
      {
         var da0:UnionGeologyThingsData = null;
         for each(da0 in this.countArr)
         {
            if(!da0.isOverB())
            {
               return false;
            }
         }
         return true;
      }
      
      public function getOreArr() : Array
      {
         var da0:UnionGeologyThingsData = null;
         var arr0:Array = [];
         for each(da0 in this.thingsArr)
         {
            if(da0.def.orePro > 0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getOreBodyNameArr(panChooseB0:Boolean = false) : Array
      {
         var da0:UnionGeologyThingsData = null;
         var bb0:Boolean = false;
         var bodyName0:String = null;
         var nameArr0:Array = [];
         var allNameArr0:Array = [];
         var arr0:Array = this.getOreArr();
         for each(da0 in arr0)
         {
            bb0 = true;
            if(panChooseB0)
            {
               if(!this.def.hangingChooseB)
               {
                  bb0 = true;
               }
               else
               {
                  bb0 = da0.save.hangingB;
               }
            }
            if(bb0)
            {
               bodyName0 = da0.getOreBodyName();
               nameArr0.push(bodyName0);
            }
            allNameArr0.push(bodyName0);
         }
         if(nameArr0.length == 0)
         {
            nameArr0 = allNameArr0;
         }
         return nameArr0;
      }
      
      public function getOreRadomBodyName() : String
      {
         var da0:UnionGeologyThingsData = null;
         var nameArr0:Array = [];
         var arr0:Array = this.getOreArr();
         for each(da0 in arr0)
         {
            if(!da0.isOverB())
            {
               nameArr0.push(da0.getOreBodyName());
            }
         }
         if(nameArr0.length > 0)
         {
            return nameArr0[int(Math.random() * nameArr0.length)];
         }
         return null;
      }
      
      public function getDataByBodyNameIfDrop(bodyName0:String) : UnionGeologyThingsData
      {
         var da0:UnionGeologyThingsData = this.getDataByBodyName(bodyName0);
         if(Boolean(da0))
         {
            if(!da0.isOverB())
            {
               if(Math.random() <= da0.def.orePro)
               {
                  return da0;
               }
            }
         }
         return null;
      }
      
      private function getDataByBodyName(bodyName0:String) : UnionGeologyThingsData
      {
         var da0:UnionGeologyThingsData = null;
         var arr0:Array = this.getOreArr();
         for each(da0 in arr0)
         {
            if(da0.getOreBodyName() == bodyName0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getTreaArr() : Array
      {
         var da0:UnionGeologyThingsData = null;
         var arr0:Array = [];
         for each(da0 in this.thingsArr)
         {
            if(da0.def.treaPro > 0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function getTreaRandomData() : UnionGeologyThingsData
      {
         var da0:UnionGeologyThingsData = null;
         var index0:int = 0;
         var proArr0:Array = [];
         var arr0:Array = this.getTreaArr();
         for each(da0 in arr0)
         {
            if(da0.isOverB())
            {
               proArr0.push(0);
            }
            else
            {
               proArr0.push(da0.def.treaPro);
            }
         }
         index0 = ComMethod.getPro_byArrSum(proArr0);
         return arr0[index0];
      }
   }
}

