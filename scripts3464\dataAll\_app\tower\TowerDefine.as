package dataAll._app.tower
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import dataAll.ui.text.ProTipType;
   
   public class TowerDefine
   {
      
      private static const LIFE_ARR:Array = [0.005,0.03,0.1,0.3];
      
      private static const DPS_ARR:Array = [0.1,0.2,0.4,1];
      
      public var lv:int = 0;
      
      public var sort:int = 0;
      
      public var bossArr:Array = [];
      
      public var giftStr:String = "";
      
      private var _giftArr:Array = [];
      
      public var oneB:Boolean = false;
      
      public var map:String = "";
      
      public var ldiy:String = "";
      
      public function TowerDefine(lv0:int = 0)
      {
         super();
         this.lv = lv0;
      }
      
      public static function get MAX_DIFF() : int
      {
         return LIFE_ARR.length;
      }
      
      public static function staticInit() : void
      {
      }
      
      public static function getLifeMulByDiff(diff0:int) : Number
      {
         return LIFE_ARR[diff0 - 1];
      }
      
      public static function getDpsMulByDiff(diff0:int) : Number
      {
         return DPS_ARR[diff0 - 1];
      }
      
      public static function getGiftArrByLv(lv0:int, maxGiftStr0:String) : Array
      {
         var g0:GiftAddDefine = null;
         var arr0:Array = [];
         var anniCoin0:int = 8;
         var zodiacCash0:int = 3;
         var demStone0:int = 5;
         if(lv0 < 11)
         {
            anniCoin0 = 8;
            zodiacCash0 = 2;
            demStone0 = 4;
         }
         else if(lv0 < 21)
         {
            anniCoin0 = 10;
            zodiacCash0 = 3;
            demStone0 = 5;
         }
         else if(lv0 < 31)
         {
            anniCoin0 = 12;
            zodiacCash0 = 4;
            demStone0 = 6;
         }
         else if(lv0 < 41)
         {
            anniCoin0 = 14;
            zodiacCash0 = 4;
            demStone0 = 7;
         }
         else if(lv0 < 51)
         {
            anniCoin0 = 16;
            zodiacCash0 = 5;
            demStone0 = 8;
         }
         else
         {
            anniCoin0 = 5;
            zodiacCash0 = 5;
            demStone0 = 8;
         }
         if(lv0 < 51)
         {
            g0 = new GiftAddDefine();
            arr0.push(g0);
            g0.inData_byStr("base;anniCoin;" + anniCoin0);
         }
         else
         {
            g0 = new GiftAddDefine();
            arr0.push(g0);
            g0.inData_byStr("base;partsCoin;" + anniCoin0);
         }
         g0 = new GiftAddDefine();
         arr0.push(g0);
         g0.inData_byStr("things;zodiacCash;" + zodiacCash0);
         g0 = new GiftAddDefine();
         arr0.push(g0);
         g0.inData_byStr("things;demStone;" + demStone0);
         if(maxGiftStr0 != "")
         {
            g0 = new GiftAddDefine();
            arr0.push(g0);
            g0.inData_byStr(maxGiftStr0);
         }
         else
         {
            g0 = new GiftAddDefine();
            arr0.push(g0);
            g0.inData_byStr("base;partsCoin;15");
         }
         return arr0;
      }
      
      public function init() : void
      {
         this._giftArr = getGiftArrByLv(this.lv,this.giftStr);
      }
      
      public function getName() : String
      {
         return String(this.lv);
      }
      
      public function addBossJson(json0:String) : void
      {
         var d0:TowerBossDefine = new TowerBossDefine();
         d0.inJson(json0);
         this.bossArr.push(d0);
      }
      
      public function add(d0:TowerBossDefine) : void
      {
         this.bossArr.push(d0);
      }
      
      public function getFirstBoss() : TowerBossDefine
      {
         if(this.bossArr.length > 0)
         {
            return this.bossArr[0];
         }
         return null;
      }
      
      public function getFirstBodyDef() : NormalBodyDefine
      {
         var d0:TowerBossDefine = this.getFirstBoss();
         if(Boolean(d0))
         {
            return d0.getBodyDefine();
         }
         return null;
      }
      
      public function getMapName() : String
      {
         var d0:NormalBodyDefine = null;
         if(this.map != "")
         {
            return this.map;
         }
         d0 = this.getFirstBodyDef();
         if(Boolean(d0))
         {
            if(d0.map != "")
            {
               return d0.map;
            }
         }
         return "WoTu";
      }
      
      public function getBossIconUrlArr() : Array
      {
         var d0:TowerBossDefine = null;
         var arr0:Array = [];
         for each(d0 in this.bossArr)
         {
            arr0.push(d0.getBodyDefine().headIconUrl);
         }
         return arr0;
      }
      
      public function getBossSkillArr() : Array
      {
         var d0:TowerBossDefine = null;
         var arr0:Array = [];
         for each(d0 in this.bossArr)
         {
            if(Boolean(d0.skillArr))
            {
               ArrayMethod.addNoRepeatArrInArr(arr0,d0.skillArr);
            }
         }
         return arr0;
      }
      
      public function getUnitOrderDefine(default_d0:OneUnitOrderDefine, diff0:int) : UnitOrderDefine
      {
         var d0:TowerBossDefine = null;
         var od0:OneUnitOrderDefine = null;
         var ud0:UnitOrderDefine = new UnitOrderDefine();
         ud0.id = "towerBoss";
         for each(d0 in this.bossArr)
         {
            od0 = d0.getOneUnitOrderDefine(diff0);
            ud0.addOneUnitOrderDefine(od0,default_d0);
         }
         return ud0;
      }
      
      public function getGatherTip() : String
      {
         var d0:TowerBossDefine = null;
         var s0:String = "";
         for each(d0 in this.bossArr)
         {
            if(Boolean(d0.skillArr) && d0.skillArr.length > 0)
            {
               s0 = StringMethod.addNewLine(s0,SkillDescrip.getSkillArrGather(d0.skillArr,GatherColor.blueColor,false,false),"<b><yellow " + d0.cnName + "特殊技能：/></b>\n");
            }
         }
         if(s0 != "")
         {
            s0 += "\n";
         }
         return s0 + "<b><orange 按下F键查看首领原始技能/></b>";
      }
      
      public function getGiftTip(giftDiff0:int, winDiff0:int) : String
      {
         var diff0:int = 0;
         var gift0:GiftAddDefine = null;
         var s0:String = "";
         var g0:GiftAddDefineGroup = this.getCanGift(giftDiff0,winDiff0);
         if(Boolean(g0) && g0.haveDataB())
         {
            s0 += ProTipType.getTitleMixed("当前可领取奖励",GatherColor.yellow);
            s0 += "\n" + g0.getDescription(4) + "\n\n";
         }
         s0 += ProTipType.getTitleMixed("难度奖励");
         var max0:int = TowerDefine.MAX_DIFF;
         for(var i:int = 0; i < max0; i++)
         {
            diff0 = i + 1;
            gift0 = this.getGift(diff0);
            s0 += "\n" + LevelDiffGetting.getCnName(i,MapMode.REGULAR) + "：";
            s0 += gift0.getDescription();
         }
         s0 += "\n<gray 通关指定难度，可获得对应低难度的所有奖励。/>";
         return s0 + "\n\n<b><orange 按下F键查看最高难度奖励说明/></b>";
      }
      
      public function getFTip() : String
      {
         var d0:TowerBossDefine = null;
         var s0:String = "";
         for each(d0 in this.bossArr)
         {
            s0 = StringMethod.addNewLine(s0,d0.getBodyDefine().getBossSkillTip(GatherColor.blueColor),"<b><yellow " + d0.cnName + "原始技能：/></b>\n");
         }
         return s0;
      }
      
      public function getMaxGift() : GiftAddDefine
      {
         return this._giftArr[MAX_DIFF - 1];
      }
      
      public function getGift(diff0:int) : GiftAddDefine
      {
         return this._giftArr[diff0 - 1];
      }
      
      public function getNoMaxGift() : GiftAddDefineGroup
      {
         return this.getCanGift(0,MAX_DIFF - 1);
      }
      
      public function getCanGift(giftDiff0:int, winDiff0:int) : GiftAddDefineGroup
      {
         var gift0:GiftAddDefine = null;
         var g0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for(var i:int = giftDiff0 + 1; i <= winDiff0; i++)
         {
            gift0 = this.getGift(i);
            g0.mergeOne(gift0);
         }
         return g0;
      }
   }
}

