package com.sounto.net
{
   import com.sounto.pool.Pool;
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import mx.core.MovieClipLoaderAsset;
   
   public class SWFLoaderManager extends EventDispatcher
   {
      
      private static const GATHER_ARR:Array = ["equipGather","uiGather","gunGather","effectGather"];
      
      public var loadState:String = "no";
      
      private var pool:Pool = new Pool();
      
      private var waitingArr:Array = new Array();
      
      private var loaderObj:Object = {};
      
      private var firstWaitingNum:int = 0;
      
      private var nowLoader:SWFLoader = null;
      
      private var perNum:Number = 0;
      
      private var onePerNum:Number = 0;
      
      public function SWFLoaderManager()
      {
         super();
      }
      
      public function addSWFList_byURL(url1:String, arr0:Array, info0:String = "") : *
      {
         var n:* = undefined;
         var label0:String = null;
         for(n in arr0)
         {
            label0 = arr0[n];
            this.addSWFLoader(url1 + label0 + ".swf",label0,info0);
         }
      }
      
      public function addSWFList_byURLArr(arr0:Array) : void
      {
         var n:* = undefined;
         var da0:SWFLoaderUrl = null;
         for(n in arr0)
         {
            da0 = arr0[n];
            this.addSWFLoader(da0.url,da0.label,da0.info);
         }
      }
      
      public function addSWFLoader(url0:String, label0:String, info0:String = "") : *
      {
         var sl:SWFLoader = null;
         var sl0:SWFLoader = this.getSWFLoader_byURL(url0);
         if(!sl0)
         {
            sl = new SWFLoader();
            sl.url = url0;
            sl.label = label0;
            sl.info = info0;
            this.waitingArr.push(sl);
         }
         else
         {
            this.TRACE("该地址已经加载过了：" + url0);
         }
      }
      
      public function panNoLoad_byURLArr(arr0:Array) : int
      {
         var da0:SWFLoaderUrl = null;
         var sl0:SWFLoader = null;
         var num0:int = 0;
         for each(da0 in arr0)
         {
            sl0 = this.getSWFLoader_byURL(da0.url);
            if(!(sl0 is SWFLoader))
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function startLoad() : void
      {
         var completeEvent:Event = null;
         if(this.waitingArr.length > 0)
         {
            this.firstWaitingNum = this.waitingArr.length;
            this.loadState = "ing";
            this.continueLoad();
         }
         else
         {
            this.TRACE("startLoad 列表中没有swf可以加载了");
            this.loadState = "no";
            completeEvent = new Event(Event.COMPLETE);
            dispatchEvent(completeEvent);
         }
      }
      
      private function continueLoad() : void
      {
         if(this.waitingArr.length > 0)
         {
            this.nowLoader = this.waitingArr[0];
            this.nowLoader.loadMe();
            this.addEvent(this.nowLoader);
            this.TRACE("列表中有" + this.waitingArr.length + "个，开始加载：" + this.nowLoader.url);
         }
      }
      
      public function stopLoad() : void
      {
         if(Boolean(this.nowLoader))
         {
            this.removeEvent(this.nowLoader);
            this.nowLoader.unloadAndStop();
         }
         this.nowLoader = null;
         this.waitingArr.length = 0;
         this.loadState = "no";
      }
      
      private function addEvent(l0:SWFLoader) : void
      {
         l0.contentLoaderInfo.addEventListener(Event.COMPLETE,this.loadCompleteHandler);
         l0.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.loadProcessHandler);
         l0.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.loadErrorHandler);
         l0.contentLoaderInfo.addEventListener("status",this.statusEventFun);
      }
      
      private function removeEvent(l0:SWFLoader) : void
      {
         l0.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.loadCompleteHandler);
         l0.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.loadProcessHandler);
         l0.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this.loadErrorHandler);
         l0.contentLoaderInfo.removeEventListener("status",this.statusEventFun);
      }
      
      private function statusEventFun(e:* = null) : void
      {
      }
      
      private function loadCompleteHandler(event:Event) : *
      {
         var completeEvent:Event = null;
         this.removeEvent(this.nowLoader);
         this.waitingArr.shift();
         this.loaderObj[this.nowLoader.label] = this.nowLoader;
         this.TRACE("列表中有" + this.waitingArr.length + "个，加载完毕：" + this.nowLoader.url);
         this.nowLoader = null;
         if(this.waitingArr.length > 0)
         {
            this.onePerNum = 1;
            this.continueLoad();
         }
         else
         {
            this.TRACE("loadCompleteHandler 列表中没有swf可以加载了");
            this.onePerNum = 1;
            this.perNum = 1;
            this.loadState = "no";
            completeEvent = new Event(Event.COMPLETE);
            dispatchEvent(completeEvent);
         }
      }
      
      private function loadProcessHandler(event:ProgressEvent) : *
      {
         var wnum:int = int(this.waitingArr.length);
         var lnum:int = this.firstWaitingNum;
         this.onePerNum = event.bytesLoaded / event.bytesTotal;
         this.perNum = 1 - wnum / lnum + 1 / lnum * this.onePerNum;
      }
      
      private function loadErrorHandler(event:IOErrorEvent) : *
      {
         this.TRACE("加载swf错误: " + event);
         dispatchEvent(event);
      }
      
      public function getSWFLoader_byURL(url0:String) : SWFLoader
      {
         var n:* = undefined;
         var m:* = undefined;
         var sl0:SWFLoader = null;
         var sl1:SWFLoader = null;
         for(n in this.loaderObj)
         {
            sl0 = this.loaderObj[n];
            if(Boolean(sl0))
            {
               if(url0 == sl0.url)
               {
                  return sl0;
               }
            }
         }
         for(m in this.waitingArr)
         {
            sl1 = this.waitingArr[m];
            if(url0 == sl1.url)
            {
               return sl1;
            }
         }
         return null;
      }
      
      public function getSWFLoader(label0:String) : SWFLoader
      {
         return this.loaderObj[label0];
      }
      
      public function getLoader(label0:String) : Loader
      {
         var lname0:String = null;
         var father0:SWFLoader = null;
         var asset0:MovieClipLoaderAsset = null;
         var l0:Loader = this.loaderObj[label0];
         if(!l0)
         {
            for each(lname0 in GATHER_ARR)
            {
               father0 = this.getSWFLoader(lname0);
               if(father0.content.hasOwnProperty(label0))
               {
                  asset0 = father0.content[label0] as MovieClipLoaderAsset;
                  if(Boolean(asset0))
                  {
                     return asset0.getChildAt(0) as Loader;
                  }
               }
            }
         }
         return l0;
      }
      
      public function getResourceFull(fullName0:String) : *
      {
         var index0:int = int(fullName0.indexOf("/"));
         var s0:String = fullName0.substring(0,index0);
         var n0:String = fullName0.substring(index0 + 1);
         return this.getResource(s0,n0);
      }
      
      public function getResource(swf_label:String, name_label:String) : *
      {
         var errorState:int = 0;
         var sl0:SWFLoader = null;
         var bb:Boolean = false;
         var newClass:Class = null;
         var obj0:* = this.pool.getObject(swf_label,name_label,true);
         if(!obj0)
         {
            errorState = 0;
            sl0 = this.getSWFLoader(swf_label);
            if(Boolean(sl0))
            {
               bb = sl0.contentLoaderInfo.applicationDomain.hasDefinition(name_label);
               if(bb)
               {
                  newClass = sl0.contentLoaderInfo.applicationDomain.getDefinition(name_label) as Class;
                  obj0 = new newClass();
               }
               else
               {
                  errorState = 2;
               }
            }
            else
            {
               errorState = 1;
            }
            if(!obj0)
            {
               obj0 = this.getResourceInGather(swf_label,name_label);
            }
            if(!obj0)
            {
               if(swf_label != "" || name_label != "")
               {
                  if(errorState == 1)
                  {
                     this.TRACE("SWFLoaderManager：没找到swf【" + swf_label + "】");
                  }
                  else if(errorState == 2)
                  {
                     this.TRACE("SWFLoaderManager：在swf【" + swf_label + "】里没找到【" + name_label + "】");
                  }
               }
            }
         }
         return obj0;
      }
      
      private function getResourceInGather(swf_label:String, name_label:String) : *
      {
         var lname0:String = null;
         var father0:SWFLoader = null;
         var asset0:MovieClipLoaderAsset = null;
         var loader0:Loader = null;
         var bb:Boolean = false;
         var newClass:Class = null;
         var obj0:* = undefined;
         for each(lname0 in GATHER_ARR)
         {
            father0 = this.getSWFLoader(lname0);
            if(father0.content.hasOwnProperty(swf_label))
            {
               asset0 = father0.content[swf_label] as MovieClipLoaderAsset;
               if(Boolean(asset0))
               {
                  loader0 = asset0.getChildAt(0) as Loader;
                  bb = loader0.contentLoaderInfo.applicationDomain.hasDefinition(name_label);
                  if(bb)
                  {
                     newClass = loader0.contentLoaderInfo.applicationDomain.getDefinition(name_label) as Class;
                     return new newClass();
                  }
               }
            }
         }
         return null;
      }
      
      public function addInPool(swf_label:String, name_label:String, obj0:*) : void
      {
         this.pool.addObject(obj0,swf_label,name_label);
      }
      
      public function haveResourceFull(fullName0:String) : *
      {
         var index0:int = int(fullName0.indexOf("/"));
         var s0:String = fullName0.substring(0,index0);
         var n0:String = fullName0.substring(index0 + 1);
         return this.haveResource(s0,n0);
      }
      
      public function haveResource(swf_label:String, name_label:String) : *
      {
         var bb:Boolean = false;
         var sl0:SWFLoader = this.getSWFLoader(swf_label);
         if(Boolean(sl0))
         {
            bb = sl0.contentLoaderInfo.applicationDomain.hasDefinition(name_label);
            if(bb)
            {
               return true;
            }
         }
         return this.haveResourceInGather(swf_label,name_label);
      }
      
      private function haveResourceInGather(swf_label:String, name_label:String) : *
      {
         var lname0:String = null;
         var father0:SWFLoader = null;
         var asset0:MovieClipLoaderAsset = null;
         var loader0:Loader = null;
         var bb:Boolean = false;
         for each(lname0 in GATHER_ARR)
         {
            father0 = this.getSWFLoader(lname0);
            if(father0.content.hasOwnProperty(swf_label))
            {
               asset0 = father0.content[swf_label] as MovieClipLoaderAsset;
               if(Boolean(asset0))
               {
                  loader0 = asset0.getChildAt(0) as Loader;
                  bb = loader0.contentLoaderInfo.applicationDomain.hasDefinition(name_label);
                  if(bb)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function getLoadingText() : String
      {
         var wnum:int = int(this.waitingArr.length);
         var lnum:int = this.firstWaitingNum;
         var str0:String = "";
         if(Boolean(this.nowLoader))
         {
            str0 = "正在加载" + this.nowLoader.info + (INIT.errorB ? this.nowLoader.url : "");
            str0 += "（" + (lnum - wnum + 1) + "/" + lnum + "）";
         }
         else
         {
            str0 = "加载完毕";
            str0 += "（" + lnum + "/" + lnum + "）";
         }
         return str0;
      }
      
      public function getOneLoadingPer() : Number
      {
         return this.onePerNum;
      }
      
      public function getLoadingPer() : Number
      {
         return this.perNum;
      }
      
      public function TRACE(v:*) : void
      {
         INIT.TRACE(v);
      }
      
      public function checkFatherArr(arr0:Array, exceptArr0:Array = null) : String
      {
         var url0:String = null;
         var father0:String = null;
         var l0:Loader = null;
         var str0:String = "";
         for each(url0 in arr0)
         {
            father0 = url0.split("/")[0];
            l0 = this.getLoader(father0);
            if(!l0)
            {
               if(exceptArr0 is Array)
               {
                  if(exceptArr0.indexOf(father0) >= 0)
                  {
                     continue;
                  }
               }
               str0 += url0 + "  ;  ";
            }
         }
         return str0;
      }
   }
}

