package dataAll._app.achieve
{
   import com.sounto.utils.ClassProperty;
   
   public class AchieveSaveGroup
   {
      
      public static var pro_arr:Array = [];
      
      public var obj:Object = {};
      
      public var onlyNoCompleteB:Boolean = false;
      
      public function AchieveSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],AchieveSave);
      }
      
      public function changeOnlyNoComplete() : Boolean
      {
         this.onlyNoCompleteB = !this.onlyNoCompleteB;
         return this.onlyNoCompleteB;
      }
      
      public function dealSaveObj(obj0:Object) : void
      {
         var n:* = undefined;
         var oldObj0:Object = null;
         var s0:AchieveSave = null;
         var newArr0:Object = {};
         for(n in this.obj)
         {
            oldObj0 = obj0.obj[n];
            s0 = this.obj[n];
            newArr0[n] = s0.getSaveObj(oldObj0);
         }
         obj0.obj = newArr0;
      }
   }
}

