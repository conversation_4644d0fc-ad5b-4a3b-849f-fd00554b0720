package dataAll._app.edit.boss
{
   import UI.test.SaveTestBox;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import com.sounto.utils.TextMethod;
   import dataAll._app.edit.EditData;
   import dataAll._app.edit.EditSave;
   import dataAll._app.edit.TorSave;
   import dataAll._app.edit._def.EditIDDefine;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll._app.edit._def.EditProPos;
   import dataAll._app.edit.arms.ArmsTorData;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._app.task.TaskState;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.extra.BossEditTopExtra;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.IO_UserGetter;
   import dataAll._player.PlayerData;
   import dataAll._player.define.MainPlayerType;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.HeroDefine;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDescrip;
   import dataAll.ui.GatherColor;
   import dataAll.ui.text.LinkTextAgent;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.data.HeroData;
   
   public class BossEditData extends EditData
   {
      
      protected var bodyDef:NormalBodyDefine;
      
      protected var tempCode:String = "";
      
      protected var topData:TopBarData = null;
      
      protected var topReturnData:PlayerTopReturnData = null;
      
      protected var taskDef:BossTaskDefine = null;
      
      protected var taskState:String = "";
      
      protected var bossSaveG:BossEditSaveGroup = null;
      
      protected var sumBody:IO_NormalBody;
      
      public function BossEditData()
      {
         super();
         gather = EditProGather.boss;
      }
      
      override public function inData_bySave(s0:EditSave, outB0:Boolean) : void
      {
         super.inData_bySave(s0,outB0);
         this.bodyDef = Gaming.defineGroup.body.getDefine(name);
         this.inSaveArmsTor();
         this.armsRangeDeal();
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return this.bodyDef;
      }
      
      public function haveDataB() : Boolean
      {
         var mapD0:WorldMapDefine = null;
         if(Boolean(this.bodyDef) && this.mp != "")
         {
            mapD0 = Gaming.defineGroup.worldMap.getDefine(this.mp);
            if(Boolean(mapD0))
            {
               return true;
            }
         }
         return false;
      }
      
      public function setTaskDefine(d0:BossTaskDefine, state0:String, bossSaveG0:BossEditSaveGroup) : void
      {
         this.taskDef = d0;
         this.taskState = state0;
         this.bossSaveG = bossSaveG0;
      }
      
      public function getTaskDefine() : BossTaskDefine
      {
         return this.taskDef;
      }
      
      public function getTaskGift(userg0:IO_UserGetter) : GiftAddDefineGroup
      {
         if(this.taskDef == null)
         {
            return null;
         }
         var diff0:int = this.getTaskDiff();
         var authorB0:Boolean = userg0.getUidx() == this.getUidx();
         return this.taskDef.getGift(diff0,authorB0);
      }
      
      public function taskGiftEvent() : void
      {
         this.bossSaveG.taskGiftEvent(this.taskDef.name);
      }
      
      public function getTaskDiff() : int
      {
         var v0:int = 0;
         if(Boolean(this.taskDef))
         {
            v0 = this.bossSaveG.getTaskDiff(this.taskDef.name);
            if(v0 == -1)
            {
               v0 = this.taskDef.getDiffLen() - 1;
            }
            return v0;
         }
         return 0;
      }
      
      public function setTaskDiff(diff0:int) : void
      {
         if(Boolean(this.taskDef))
         {
            this.bossSaveG.setTaskDiff(this.taskDef.name,diff0);
         }
      }
      
      public function getTaskState() : String
      {
         if(Boolean(this.taskDef))
         {
            if(this.taskState != "")
            {
               return this.taskState;
            }
            return this.bossSaveG.getTaskState(this.taskDef.name);
         }
         return TaskState.no;
      }
      
      public function setTaskState(s0:String) : void
      {
         this.taskState = s0;
      }
      
      public function getTaskTip(userg0:IO_UserGetter) : String
      {
         if(this.taskDef == null)
         {
            return "";
         }
         var diff0:int = this.getTaskDiff();
         var g0:GiftAddDefineGroup = this.getTaskGift(userg0);
         var s0:String = this.taskDef.getTip(g0,diff0);
         s0 += "\n\n" + this.getNormalTip();
         if(Gaming.isLocal())
         {
            if(Boolean(this.taskDef.noSkillArr))
            {
               s0 = "删除技能：" + SkillDescrip.getCnText(this.taskDef.noSkillArr) + "\n" + s0;
            }
            s0 = this.taskDef.name + "\n" + s0;
         }
         return s0;
      }
      
      private function dealTaskLevelDefine(d0:LevelDefine) : void
      {
         var diff0:int = 0;
         var lifeMul0:Number = NaN;
         var dpsMul0:Number = NaN;
         var ud0:OneUnitOrderDefine = d0.unitG.getBossOne();
         if(Boolean(ud0))
         {
            diff0 = this.getTaskDiff();
            lifeMul0 = this.taskDef.getLifeMul(diff0);
            dpsMul0 = this.taskDef.getDpsMul(diff0);
            ud0.lifeMul *= lifeMul0;
            ud0.dpsMul *= dpsMul0;
            if(diff0 <= 6 && Boolean(this.taskDef.noSkillArr))
            {
               ud0.skillArr = ArrayMethod.deductArr(ud0.skillArr,this.taskDef.noSkillArr);
            }
            if(d0.info.tm > 0)
            {
               if(this.getSkillNameArr().indexOf("killMeTimeOver") >= 0)
               {
                  d0.info.tm *= this.taskDef.getTmMul(diff0);
               }
            }
         }
      }
      
      public function setTempCode(code0:String) : void
      {
         this.tempCode = code0;
      }
      
      public function getTempCode() : String
      {
         return this.tempCode;
      }
      
      public function getShareCode(userg0:IO_UserGetter, meUidB0:Boolean = true) : String
      {
         if(this.tempCode != "")
         {
            return this.tempCode;
         }
         return this.getMeCode(userg0,meUidB0);
      }
      
      private function getMeCode(userg0:IO_UserGetter, meUidB0:Boolean = true) : String
      {
         var armsTor0:ArmsTorData = null;
         var obj0:Object = ClassProperty.copyObj(save.obj);
         if(meUidB0)
         {
            obj0[BossEditPro.ud] = userg0.getUidx();
            obj0[BossEditPro.pn] = userg0.getPlayerName();
         }
         var armsRange0:ArmsRangeDefine = this.getArmsRangeDef();
         if(Boolean(armsRange0))
         {
            armsTor0 = userg0.getPD().armsTor.getDataByName(armsRange0.def.name) as ArmsTorData;
            if(Boolean(armsTor0))
            {
               obj0[BossEditPro.armsTor] = ClassProperty.copyObj(armsTor0.getTorSave());
            }
         }
         return BossEditMethod.objToCode(obj0);
      }
      
      public function getUploadTopName() : String
      {
         var star0:int = this.getStar();
         if(star0 <= 2)
         {
            star0 = 1;
         }
         return "bossEdit_" + star0;
      }
      
      public function tempInitTopData(topDa0:TopBarData, code0:String, obj0:Object) : void
      {
         var save0:EditSave = new EditSave();
         save0.obj = obj0;
         this.setTempCode(code0);
         this.inData_bySave(save0,true);
         this.topData = topDa0;
      }
      
      public function getTempTopData() : TopBarData
      {
         return this.topData;
      }
      
      public function getTopExtra(userg0:IO_UserGetter) : BossEditTopExtra
      {
         var e0:BossEditTopExtra = new BossEditTopExtra();
         e0.o = this.getMeCode(userg0);
         return e0;
      }
      
      public function getTopBarTip() : String
      {
         var s0:String = "<b><yellow 点击挑战首领/></b>";
         return s0 + ("\n\n" + this.getNormalTip());
      }
      
      private function getNormalTip() : String
      {
         var pro0:String = null;
         var d0:EditProDefine = null;
         var v0:* = undefined;
         var init0:* = undefined;
         var s0:String = "";
         s0 += "<blue 等级：/>" + this.lv;
         s0 += "\n<blue 生命：/>" + this.getLifeUI();
         s0 += "\n<blue 伤害：/>" + this.getDpsUI();
         if(Boolean(this.ar) && this.ar.length > 0)
         {
            s0 += "\n<blue 武器：/>" + this.getArmsRangeCn();
         }
         var skillCnArr0:Array = this.getSkillCnArr();
         if(skillCnArr0.length > 0)
         {
            s0 += "\n<blue 额外技能：/><green " + StringMethod.concatStringArr(skillCnArr0,99) + "/>";
         }
         var proArr0:Array = BossEditPro.levelUIArr;
         var limitArr0:Array = [];
         for each(pro0 in proArr0)
         {
            d0 = getDefine(pro0);
            v0 = this.getUIValueByDef(d0);
            init0 = d0.getInit();
            if(v0 is Boolean && init0 is Boolean)
            {
               if(v0 == true)
               {
                  limitArr0.push(d0.cnName);
               }
            }
            else
            {
               s0 += "\n<blue " + d0.cnName + "：/>" + v0 + d0.unit;
            }
         }
         if(limitArr0.length > 0)
         {
            s0 += "\n<blue 限制：/>" + StringMethod.concatStringArr(limitArr0,99);
         }
         return s0;
      }
      
      public function uploadScoreEvent(obj0:Object) : void
      {
         var da0:PlayerTopReturnData = new PlayerTopReturnData();
         da0.inData_byObj(obj0);
         this.topReturnData = da0;
      }
      
      public function getTopReturnData() : PlayerTopReturnData
      {
         return this.topReturnData;
      }
      
      public function getTopLeftText() : String
      {
         var s0:String = this.getCnName();
         s0 += "\n" + ComMethod.color(this.getScoreUI(),GatherColor.gray2Color);
         if(Boolean(this.topReturnData))
         {
            s0 += ComMethod.color("\n已上传",GatherColor.greenColor);
         }
         return s0;
      }
      
      private function valueChangeEvent() : void
      {
         this.setWinB(false);
      }
      
      public function outSetValue(pro0:String, v0:*) : void
      {
         var before0:* = getValueByDefName(pro0);
         if(before0 != v0)
         {
            this.valueChangeEvent();
         }
         setValue(pro0,v0);
      }
      
      override public function swapBoolean(name0:String) : Boolean
      {
         this.valueChangeEvent();
         return super.swapBoolean(name0);
      }
      
      override protected function getUIValueByDef(proD0:EditProDefine) : *
      {
         var v0:* = super.getUIValueByDef(proD0);
         if(proD0.name == BossEditPro.mp)
         {
            return this.getMapCn();
         }
         if(proD0.name == BossEditPro.ar)
         {
            return this.getArmsRangeCn();
         }
         return v0;
      }
      
      public function get lv() : int
      {
         return getValueByDefName(BossEditPro.lv);
      }
      
      public function set lv(v0:int) : void
      {
         setValue(BossEditPro.lv,v0);
      }
      
      public function getLife() : Number
      {
         var mul0:Number = NaN;
         var lv0:Number = NaN;
         var baseLife0:Number = NaN;
         var bossMul0:Number = NaN;
         var taskMul0:Number = NaN;
         var v0:Number = NaN;
         if(Boolean(this.bodyDef))
         {
            mul0 = getValueByDefName(BossEditPro.li);
            lv0 = this.lv;
            baseLife0 = Gaming.defineGroup.normal.getEnemyLife(lv0);
            bossMul0 = UnitType.getLifeMul(UnitType.BOSS);
            taskMul0 = 1;
            if(Boolean(this.taskDef))
            {
               taskMul0 = this.taskDef.getLifeMul(this.getTaskDiff());
            }
            return Math.ceil(baseLife0 * mul0 * bossMul0 * taskMul0);
         }
         return 0;
      }
      
      public function getDps() : Number
      {
         var mul0:Number = NaN;
         var lv0:Number = NaN;
         var base0:Number = NaN;
         var bossMul0:Number = NaN;
         var taskMul0:Number = NaN;
         var v0:Number = NaN;
         if(Boolean(this.bodyDef))
         {
            mul0 = getValueByDefName(BossEditPro.dp);
            lv0 = this.lv;
            base0 = Gaming.defineGroup.normal.getEnemyDps(lv0);
            bossMul0 = UnitType.getDpsMul(UnitType.BOSS);
            taskMul0 = 1;
            if(Boolean(this.taskDef))
            {
               taskMul0 = this.taskDef.getDpsMul(this.getTaskDiff());
            }
            return Math.ceil(base0 * mul0 * bossMul0 * taskMul0);
         }
         return 0;
      }
      
      public function getDpsUI() : String
      {
         var v0:Number = this.getDps();
         return NumberMethod.toShortLimit(v0);
      }
      
      public function getLifeUI() : String
      {
         var v0:Number = this.getLife();
         return NumberMethod.toShortLimit(v0);
      }
      
      public function getIconUrl() : String
      {
         if(Boolean(this.bodyDef))
         {
            return this.bodyDef.headIconUrl;
         }
         return "";
      }
      
      private function get mp() : String
      {
         return getValueByDefName(BossEditPro.mp);
      }
      
      private function set mp(v0:String) : void
      {
         setValue(BossEditPro.mp,v0);
      }
      
      public function setMapName(name0:String) : void
      {
         if(this.mp != name0)
         {
            this.valueChangeEvent();
         }
         this.mp = name0;
      }
      
      public function getMapName() : String
      {
         return this.mp;
      }
      
      protected function getMapCn() : String
      {
         var mapD0:WorldMapDefine = null;
         var v0:String = this.mp;
         if(!(v0 == null || v0 == ""))
         {
            mapD0 = Gaming.defineGroup.worldMap.getDefine(v0);
            if(Boolean(mapD0))
            {
               return mapD0.cnName;
            }
         }
         return "无";
      }
      
      private function inSaveArmsTor() : ArmsTorData
      {
         var s0:TorSave = null;
         var da0:ArmsTorData = null;
         var obj0:Object = save.getValue(BossEditPro.armsTor);
         if(Boolean(obj0))
         {
            s0 = new TorSave();
            da0 = new ArmsTorData();
            s0.inData_byObj(obj0);
            da0.inData_bySave(s0);
            return da0;
         }
         return null;
      }
      
      public function armsRangeDeal() : void
      {
         var d0:ArmsRangeDefine = null;
         var name0:String = this.getArmsRangeName();
         if(name0 != "")
         {
            d0 = this.getArmsRangeDef();
            if(d0 == null)
            {
               this.setArmsRange("");
            }
         }
      }
      
      private function get ar() : Array
      {
         return getValueByDefName(BossEditPro.ar);
      }
      
      public function isTorArmsB() : Boolean
      {
         var name0:String = this.getArmsRangeName();
         if(name0 != "")
         {
            return Gaming.defineGroup.bullet.getRangeOnlyRange(name0) == null;
         }
         return false;
      }
      
      public function getArmsRangeName() : String
      {
         var arr0:Array = this.ar;
         if(Boolean(arr0) && arr0.length > 0)
         {
            return arr0[0];
         }
         return "";
      }
      
      public function getArmsRangeDef() : ArmsRangeDefine
      {
         var ad0:ArmsRangeDefine = null;
         var name0:String = this.getArmsRangeName();
         if(name0 != "")
         {
            return Gaming.defineGroup.bullet.getArmsRangeDefine(name0);
         }
         return null;
      }
      
      public function setArmsRange(name0:String) : void
      {
         this.valueChangeEvent();
         if(name0 == "")
         {
            setValue(BossEditPro.ar,[]);
         }
         else
         {
            setValue(BossEditPro.ar,[name0]);
         }
      }
      
      public function getPreArmsNameArr() : Array
      {
         if(Boolean(this.ar))
         {
            return this.ar;
         }
         return null;
      }
      
      protected function getArmsRangeCn() : String
      {
         var ad0:ArmsRangeDefine = this.getArmsRangeDef();
         if(Boolean(ad0))
         {
            return ad0.def.cnName;
         }
         return "未设定";
      }
      
      public function getMinByProDefine(proD0:EditProDefine) : Number
      {
         var min0:Number = proD0.min;
         if(Boolean(this.bodyDef))
         {
            if(proD0.name != BossEditPro.lv)
            {
               if(proD0.name == BossEditPro.li)
               {
                  if(this.bodyDef.father == BodyFather.vehicle)
                  {
                     min0 = 10;
                  }
               }
            }
         }
         return min0;
      }
      
      public function getCnName() : String
      {
         var s0:String = null;
         if(Boolean(this.bodyDef))
         {
            s0 = this.bodyDef.cnName;
            if(this.cn != "")
            {
               if(s0.length > 4)
               {
                  s0 = this.cn + s0.substr(2);
               }
               else
               {
                  s0 = this.cn + "·" + s0;
               }
            }
            return s0;
         }
         return "";
      }
      
      public function setCnFirst(cn0:String) : void
      {
         this.cn = cn0;
      }
      
      private function get cn() : String
      {
         return getValueByDefName(BossEditPro.cn);
      }
      
      private function set cn(v0:String) : void
      {
         setValue(BossEditPro.cn,v0);
      }
      
      public function getPlayerName() : String
      {
         return getValueByDefName(BossEditPro.pn);
      }
      
      public function getUidx() : String
      {
         return getValueByDefName(BossEditPro.ud);
      }
      
      public function getScore() : Number
      {
         return BossEditMethod.getScore(this);
      }
      
      public function getScoreUI(starB0:Boolean = true) : String
      {
         var star0:int = 0;
         var starStr0:String = null;
         var s0:Number = this.getScore();
         if(s0 > 0 && starB0)
         {
            star0 = BossEditMethod.getStarByScore(s0);
            starStr0 = star0 + "星";
            if(Boolean(this.taskDef))
            {
               starStr0 = "难度" + (this.getTaskDiff() + 1);
            }
            return s0 + TextMethod.color("（" + starStr0 + "）",GatherColor.yellowColor);
         }
         return String(s0);
      }
      
      public function getStar() : int
      {
         var s0:Number = this.getScore();
         return BossEditMethod.getStarByScore(s0);
      }
      
      public function winEvent() : void
      {
         this.setWinB(true);
         if(Boolean(this.taskDef))
         {
            this.bossSaveG.taskWinEvent(this.taskDef.name);
         }
      }
      
      public function getWinB() : Boolean
      {
         return save.winB;
      }
      
      public function setWinB(bb0:Boolean) : void
      {
         save.winB = bb0;
      }
      
      public function getLockB() : Boolean
      {
         return save.lockB;
      }
      
      public function setLockB(bb0:Boolean) : void
      {
         save.lockB = bb0;
      }
      
      protected function getSkillIdArr() : Array
      {
         return save.getValueArrayAndNoSet(BossEditPro.sk);
      }
      
      public function addSkillName(name0:String) : void
      {
         var ed0:EditIDDefine = null;
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine(name0);
         if(Boolean(d0))
         {
            ed0 = d0.getEditD();
            if(Boolean(ed0))
            {
               this.addSkillById(ed0.id);
               this.valueChangeEvent();
            }
         }
      }
      
      protected function addSkillById(id0:String) : void
      {
         var idArr0:Array = this.getSkillIdArr();
         idArr0.push(id0);
      }
      
      public function removeSkillName(name0:String) : Boolean
      {
         var ed0:EditIDDefine = null;
         var idArr0:Array = null;
         var num0:int = 0;
         var d0:SkillDefine = Gaming.defineGroup.skill.getDefine(name0);
         if(Boolean(d0))
         {
            ed0 = d0.getEditD();
            if(Boolean(ed0))
            {
               idArr0 = this.getSkillIdArr();
               num0 = ArrayMethod.remove(idArr0,ed0.id);
               this.valueChangeEvent();
               return num0 > 0;
            }
         }
         return false;
      }
      
      public function getSkillNameArr() : Array
      {
         var id0:String = null;
         var d0:SkillDefine = null;
         var idArr0:Array = this.getSkillIdArr();
         var nameArr0:Array = [];
         for each(id0 in idArr0)
         {
            d0 = BossEditSkill.getSkillById(id0);
            if(Boolean(d0))
            {
               nameArr0.push(d0.name);
            }
         }
         return nameArr0;
      }
      
      private function getSkillCnArr() : Array
      {
         var id0:String = null;
         var d0:SkillDefine = null;
         var idArr0:Array = this.getSkillIdArr();
         var nameArr0:Array = [];
         for each(id0 in idArr0)
         {
            d0 = BossEditSkill.getSkillById(id0);
            if(Boolean(d0))
            {
               nameArr0.push(d0.cnName);
            }
         }
         return nameArr0;
      }
      
      public function getBaseSkillArr() : Array
      {
         if(Boolean(this.bodyDef))
         {
            return this.bodyDef.getEditSkillArr();
         }
         return null;
      }
      
      public function getSkillAddMax() : int
      {
         return 14;
      }
      
      public function addSkillPan() : String
      {
         var idArr0:Array = this.getSkillIdArr();
         var now0:int = int(idArr0.length);
         var max0:int = this.getSkillAddMax();
         if(now0 >= max0)
         {
            return "最多只能添加" + max0 + "个技能。";
         }
         return "";
      }
      
      public function getSkillListAgent() : EditListAgent
      {
         var a0:EditListAgent = BossEditSkill.getListAgent();
         var skillArr0:Array = this.getSkillNameArr();
         var skillLen0:int = int(skillArr0.length);
         var baseArr0:Array = this.getBaseSkillArr();
         if(Boolean(baseArr0))
         {
            skillArr0 = skillArr0.concat(baseArr0);
         }
         a0.setNoLinkArr(skillArr0);
         a0.info = "添加技能（" + skillLen0 + "/" + this.getSkillAddMax() + "）";
         a0.createAllText();
         return a0;
      }
      
      public function getBaseTextAgent() : LinkTextAgent
      {
         var d0:NormalBodyDefine = this.bodyDef;
         var a0:LinkTextAgent = new LinkTextAgent();
         a0.addData(LinkTextAgent.titleColor(this.getCnName()),BossEditMethod.changeCn);
         a0.addData("评分 " + this.getScoreUI(),"","作为首领难度的大致参考值，并不准确。");
         proArrInText(d0 is HeroDefine ? BossEditPro.heroBaseUIArr : BossEditPro.baseUIArr,a0);
         var baseSkillArr0:Array = this.getBaseSkillArr();
         if(Boolean(baseSkillArr0))
         {
            a0.addData(LinkTextAgent.secColor("原始技能x" + baseSkillArr0.length),BossEditMethod.baseSkill,baseSkillArr0);
         }
         return a0;
      }
      
      public function getSkillTextAgent() : LinkTextAgent
      {
         var a0:LinkTextAgent = new LinkTextAgent();
         a0.addData(LinkTextAgent.titleColor("添加技能"),BossEditMethod.addSkill);
         a0.addSkillNameArr(this.getSkillNameArr(),true);
         return a0;
      }
      
      public function getLevelTextAgent() : LinkTextAgent
      {
         var a0:LinkTextAgent = new LinkTextAgent();
         a0.addData(LinkTextAgent.titleColor("关卡参数"));
         proArrInText(BossEditPro.levelUIArr,a0);
         return a0;
      }
      
      public function getTextTip(txtIndex0:int, link0:String, data0:*) : String
      {
         var proD0:EditProDefine = null;
         var skillD0:SkillDefine = null;
         var tip0:String = "";
         if(link0 == BossEditMethod.baseSkill)
         {
            tip0 = SkillDescrip.getSkillArrGather(data0 as Array,GatherColor.blueColor,false,false,true);
         }
         else if(link0 == BossEditMethod.addSkill)
         {
            tip0 = "最多只能添加" + this.getSkillAddMax() + "个技能。";
         }
         else if(data0 is String)
         {
            tip0 = data0 as String;
         }
         else
         {
            proD0 = data0 as EditProDefine;
            skillD0 = data0 as SkillDefine;
            if(Boolean(proD0))
            {
               if(proD0.name == BossEditPro.li)
               {
                  tip0 = "生命值：" + this.getLifeUI();
               }
               else if(proD0.name == BossEditPro.dp)
               {
                  tip0 = "伤害：" + this.getDpsUI();
               }
               else
               {
                  tip0 = proD0.tip;
               }
            }
            else if(Boolean(skillD0))
            {
               tip0 = skillD0.getDescription(true);
               if(txtIndex0 == 1)
               {
                  tip0 = TextMethod.color("点击删除技能",GatherColor.orangeColor) + "\n\n" + tip0;
               }
            }
         }
         return tip0;
      }
      
      public function getNewLevelDefine() : LevelDefine
      {
         var baseD0:LevelDefine = null;
         var mapD0:WorldMapDefine = Gaming.defineGroup.worldMap.getDefine(this.mp);
         if(!mapD0)
         {
            return null;
         }
         var d0:LevelDefine = Gaming.defineGroup.level.getDefineBy("bossEdit").clone();
         var base0:String = mapD0.getLevelName();
         if(base0 != "")
         {
            baseD0 = Gaming.defineGroup.level.getDefineBy(base0);
            d0.rectG = baseD0.getCloneRectG();
         }
         this.levelDefineInEditObj(d0);
         if(Boolean(this.taskDef))
         {
            this.dealTaskLevelDefine(d0);
         }
         return d0;
      }
      
      private function levelDefineInEditObj(d0:LevelDefine) : void
      {
         var proD0:EditProDefine = null;
         var v0:* = undefined;
         var pos0:String = null;
         var defObj0:Object = null;
         var proDefArr0:Array = Gaming.defineGroup.editPro.getArrByGather(gather);
         for each(proD0 in proDefArr0)
         {
            v0 = null;
            if(proD0.defValueFun != "")
            {
               v0 = this[proD0.defValueFun]();
            }
            else
            {
               v0 = save.getValueByDef(proD0);
            }
            pos0 = proD0.getBossProPos();
            defObj0 = EditProPos.getLevelPos(d0,pos0);
            if(Boolean(defObj0))
            {
               defObj0[proD0.defPro] = v0;
            }
         }
         d0.info.noTreasureB = true;
         d0.fleshDataByOutChange();
      }
      
      public function sumPan() : Boolean
      {
         var typeB0:Boolean = false;
         var nameB0:Boolean = false;
         if(this.haveDataB())
         {
            typeB0 = BodyFather.sumArr.indexOf(this.bodyDef.father) >= 0;
            nameB0 = BodyFather.noSumNameArr.indexOf(name) == -1;
            return typeB0 && nameB0;
         }
         return false;
      }
      
      public function getSumLevelDefine(pd0:PlayerData) : LevelDefine
      {
         var base0:LevelDefine = null;
         var d0:LevelDefine = null;
         var mapS0:WorldMapSave = pd0.worldMap.saveGroup.getSave(this.mp);
         if(Boolean(mapS0) && mapS0.winB)
         {
            base0 = mapS0.getLevelDefine();
            if(Boolean(base0))
            {
               if(base0.fixed.target != "")
               {
                  base0 = Gaming.defineGroup.level.getDefine(base0.fixed.target);
               }
               d0 = base0.clone();
               d0.info.dumB = true;
               d0.drop.noB = true;
               d0.name = BossEditMethod.semmonBoss;
               return d0;
            }
         }
         return null;
      }
      
      public function getSumSkillArr() : Array
      {
         var bossArr0:Array = null;
         var arr0:Array = this.getSkillNameArr();
         if(Boolean(this.bodyDef))
         {
            bossArr0 = this.bodyDef.getBossSkillArr(false,false);
            ArrayMethod.addNoRepeatArrInArr(arr0,bossArr0);
         }
         arr0.push("sumBossAtten");
         arr0.push("alloyShell_8");
         return arr0;
      }
      
      public function setSumBody(b0:IO_NormalBody) : void
      {
         var armsRange0:Array = null;
         var dat0:NormalBodyData = null;
         var heroDat0:HeroData = null;
         this.sumBody = b0;
         if(Boolean(b0))
         {
            armsRange0 = this.ar;
            dat0 = b0.getData();
            heroDat0 = dat0 as HeroData;
            dat0.inBossEditData(this);
            dat0.bodyLevel = this.lv;
            if(Boolean(heroDat0))
            {
               if(Boolean(armsRange0) && armsRange0.length > 0)
               {
                  heroDat0.inNewArmsSaveGroup(Gaming.defineGroup.armsCreator.getLevelSaveGroup(armsRange0,true));
               }
            }
            dat0.setDpsFactor(this.getSumDps());
            dat0.setNewMaxLife(this.getSumLife());
            b0.getSkill().addSkill_byNameArr(this.getSumSkillArr());
            SaveTestBox.addText("召唤首领：" + dat0.define.cnName + "：life=" + dat0.maxLife + "  dps=" + dat0.dpsFactor);
            SaveTestBox.addText("技能：" + Gaming.defineGroup.skill.getCnArrByNameArr(this.getSumSkillArr()));
         }
      }
      
      public function getSumBody() : IO_NormalBody
      {
         return this.sumBody;
      }
      
      public function overGamingClear() : void
      {
         this.sumBody = null;
      }
      
      private function getSumLife() : Number
      {
         var mul0:Number = getValueByDefName(BossEditPro.li);
         var lv0:Number = this.lv;
         var baseLife0:Number = 5 * Gaming.defineGroup.normal.getEnemyDps(lv0);
         return Math.ceil(baseLife0 * mul0);
      }
      
      private function getSumDps() : Number
      {
         var mul0:Number = getValueByDefName(BossEditPro.dp);
         var lv0:Number = this.lv;
         var base0:Number = 0.2 * Gaming.defineGroup.normal.getEnemyLife(lv0);
         return Math.ceil(base0 * mul0);
      }
      
      public function getSumUnitOrderDefine(heroId0:String, camp0:String, pd0:PlayerData) : OneUnitOrderDefine
      {
         var bodyD0:NormalBodyDefine = this.getBodyDefine();
         var d0:OneUnitOrderDefine = new OneUnitOrderDefine();
         d0.aiOrder = "followBodyAttack:" + heroId0;
         d0.warningRange = 3000;
         d0.camp = camp0;
         d0.name = bodyD0.name;
         d0.fleshBodyName();
         d0.id = "sumBoss_" + bodyD0.cnName;
         if(pd0.mainPlayerType != MainPlayerType.ME)
         {
            d0.id = heroId0 + "_" + d0.id;
         }
         return d0;
      }
   }
}

