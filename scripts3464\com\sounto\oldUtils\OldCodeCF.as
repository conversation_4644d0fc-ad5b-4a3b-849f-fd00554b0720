package com.sounto.oldUtils
{
   import com.common.text.TextWay;
   
   public class OldCodeCF
   {
      
      private var varObj:Object;
      
      public function OldCodeCF()
      {
         super();
      }
      
      public function setAttribute(varName:String, varValue:String) : *
      {
         var i:* = undefined;
         var tmpObj:Object = new Object();
         tmpObj = {"value":TextWay.toCode32(varValue)};
         var tmpObj2:Object = new Object();
         for(i in this.varObj)
         {
            tmpObj2[i] = this.varObj[i];
         }
         tmpObj2[varName] = tmpObj.value;
         tmpObj = null;
         this.varObj = null;
         this.varObj = tmpObj2;
      }
      
      public function getAttribute(varName:String) : String
      {
         if(this.varObj == null || !this.varObj.hasOwnProperty(varName))
         {
            return null;
         }
         return TextWay.getText32(this.varObj[varName]);
      }
   }
}

