package dataAll.things.define
{
   import dataAll.level.DemonDataCtrl;
   import gameAll.drop.bodyDrop.MadBodyDrop;
   
   public class ThingsType
   {
      
      public static const PROPS:String = "props";
      
      public static const MATERIALS:String = "materials";
      
      public static const BLACK_CHIP:String = "blackChip";
      
      public static const RARE_CHIP:String = "rareChip";
      
      public static const NORMAL_CHIP:String = "normalChip";
      
      public static const SKILL_CHIP:String = "skillChip";
      
      public static const PARTS:String = "parts";
      
      public static const PARTS_CHEST:String = "partsChest";
      
      public static const purgoldArr:Array = [DemonDataCtrl.demStone,MadBodyDrop.MADHEART];
      
      public function ThingsType()
      {
         super();
      }
   }
}

