package dataAll.gift.level
{
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class GiftLevelGiftCreator
   {
      
      private static var armsNameArr:Array = [];
      
      public function GiftLevelGiftCreator()
      {
         super();
      }
      
      public static function conver(dg0:GiftAddDefineGroup) : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = null;
         var dg2:GiftAddDefineGroup = null;
         var fun0:Function = null;
         var newArr0:Array = [];
         for each(d0 in dg0.arr)
         {
            fun0 = GiftLevelGiftCreator[d0.type];
            if(fun0 is Function)
            {
               newArr0 = newArr0.concat(fun0(d0));
            }
            else
            {
               newArr0.push(d0.clone());
            }
         }
         dg2 = new GiftAddDefineGroup();
         dg2.inData_byObj(dg0);
         dg2.arr = newArr0;
         return dg2;
      }
      
      private static function parts(d0:GiftAddDefine) : Array
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var d2:GiftAddDefine = null;
         var arr0:Array = [];
         if(d0.name == "all")
         {
            nameArr0 = Gaming.defineGroup.things.normalPartsNameArr;
            for each(name0 in nameArr0)
            {
               d2 = new GiftAddDefine();
               d2.inData_byStr("parts;" + name0 + "_" + d0.lv + ";" + d0.num);
               arr0.push(d2);
            }
         }
         else
         {
            arr0.push(d0.clone());
         }
         return arr0;
      }
      
      private static function arms(d0:GiftAddDefine) : Array
      {
         d0 = d0.clone();
         var armsD0:ArmsDefine = getArmsDefine(d0.childType);
         var specialArr0:Array = ArmsSpecialAndSkill.getSpecialNameArr(armsD0);
         specialArr0 = specialArr0.slice(0,3);
         var skillArr0:Array = armsD0.skillArr.concat([]);
         var godArr0:Array = armsD0.godSkillArr.concat([]);
         if(d0.color == EquipColor.RED)
         {
            godArr0 = godArr0.slice(0,1);
         }
         else if(EquipColor.firstMax(EquipColor.RED,d0.color))
         {
            godArr0.length = 0;
         }
         var s0:ArmsSave = Gaming.defineGroup.armsCreator.getSuperSave(d0.color,int(d0.lv),d0.childType,"min",specialArr0,skillArr0,godArr0);
         s0.upgradeB = false;
         d0.itemsSave = s0;
         return [d0];
      }
      
      private static function getArmsDefine(type0:String) : ArmsDefine
      {
         var r0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var arr0:Array = Gaming.defineGroup.bullet.getArmsRangeDefineArr(type0);
         for each(r0 in arr0)
         {
            d0 = r0.def;
            if(d0.rareDropLevel == 0 && d0.skillArr.length == 2 && d0.godSkillArr.length == 2)
            {
               return d0;
            }
         }
         return null;
      }
   }
}

