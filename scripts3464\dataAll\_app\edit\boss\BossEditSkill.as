package dataAll._app.edit.boss
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.edit._def.EditIDDefine;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll.pro.dataList.DataListDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.ui.GatherColor;
   
   public class BossEditSkill
   {
      
      public static const attack:String = "attack";
      
      public static const limit:String = "limit";
      
      public static const defence:String = "defence";
      
      public static const other:String = "other";
      
      public static const gatherArr:Array = [attack,limit,defence,other];
      
      private static const gatherCnArr:Array = ["攻击","限制","防御","其他"];
      
      private static const fatherArrObj:Object = {};
      
      private static const fatherCnArrObj:Object = {};
      
      private static var skillArrObjObj:Object = {};
      
      private static var obj:Object = null;
      
      private static var nameObj:Object = null;
      
      private static var cnDefObj:Object = null;
      
      private static var skillAgent:EditListAgent = null;
      
      private static var cnAgent:EditListAgent = null;
      
      public function BossEditSkill()
      {
         super();
      }
      
      public static function init() : void
      {
         fatherArrObj[attack] = ["trans","attackBuff","bullet","attackBody","attackOther"];
         fatherCnArrObj[attack] = ["变身召唤","攻击状态","攻击子弹","针对队友","其他"];
         fatherArrObj[limit] = ["limitSkill","limitMot","limitAttack"];
         fatherCnArrObj[limit] = ["技能限制","运动限制","攻击限制"];
         fatherArrObj[defence] = ["defNormal","addLife","sensitive","defenceAttackType","defenceSkill"];
         fatherCnArrObj[defence] = ["未归类","回血","敏感","针对攻击","针对技能"];
         fatherArrObj[other] = ["mot","hiding","image","other"];
         fatherCnArrObj[other] = ["运动","隐身","图像和天气","测试专用"];
         inSkillList();
      }
      
      private static function inSkillList() : void
      {
         var str0:String = null;
         var f0:int = 0;
         var id0:String = null;
         var name0:String = null;
         var d0:SkillDefine = null;
         var idD0:EditIDDefine = null;
         if(Boolean(obj))
         {
            return;
         }
         obj = {};
         nameObj = {};
         cnDefObj = {};
         var dataList0:DataListDefine = Gaming.defineGroup.dataList.getDefine("bossEditSkill");
         var strArr0:Array = dataList0.getTrueValueArr();
         for each(str0 in strArr0)
         {
            if(str0 != "")
            {
               f0 = int(str0.indexOf(":"));
               id0 = str0.substring(0,f0);
               name0 = str0.substring(f0 + 1);
               if(id0 != "" && name0 != "")
               {
                  d0 = Gaming.defineGroup.skill.getDefine(name0);
                  if(Boolean(d0))
                  {
                     idD0 = new EditIDDefine();
                     idD0.inId(id0);
                     d0.setEditD(idD0);
                     obj[id0] = d0;
                     nameObj[name0] = id0;
                     cnDefObj[d0.cnName] = d0;
                     addInGather(d0);
                  }
               }
            }
         }
      }
      
      private static function addInGather(d0:SkillDefine) : void
      {
         var idD0:EditIDDefine = d0.getEditD();
         var gather0:String = gatherArr[idD0.gather - 1];
         var fatherArr0:Array = fatherArrObj[gather0];
         var father0:String = fatherArr0[idD0.father - 1];
         var fobj0:Object = skillArrObjObj[gather0];
         if(!fobj0)
         {
            fobj0 = {};
            skillArrObjObj[gather0] = fobj0;
         }
         var arr0:Array = fobj0[father0];
         if(!arr0)
         {
            arr0 = [];
            fobj0[father0] = arr0;
         }
         arr0.push(d0);
      }
      
      public static function getSkillByCn(cn0:String) : SkillDefine
      {
         return cnDefObj[cn0];
      }
      
      public static function cnArrToNameArr(cnArr0:Array, first0:String = "", last0:String = "") : Array
      {
         var cn0:String = null;
         var d0:SkillDefine = null;
         var name0:String = null;
         var arr0:Array = [];
         for each(cn0 in cnArr0)
         {
            d0 = getSkillByCn(cn0);
            name0 = cn0;
            if(Boolean(d0))
            {
               name0 = d0.name;
            }
            name0 = first0 + name0 + last0;
            arr0.push(name0);
         }
         return arr0;
      }
      
      public static function getSkillById(id0:String) : SkillDefine
      {
         return obj[id0];
      }
      
      public static function getSkillArrByIdArr(idArr0:Array) : Array
      {
         var id0:String = null;
         var d0:SkillDefine = null;
         var arr0:Array = [];
         for each(id0 in idArr0)
         {
            d0 = getSkillById(id0);
            if(Boolean(d0))
            {
               arr0.push(d0.name);
            }
         }
         return arr0;
      }
      
      public static function getIdSkillBySkillName(name0:String) : String
      {
         return nameObj[name0];
      }
      
      public static function haveSkillNameB(name0:String) : Boolean
      {
         return nameObj.hasOwnProperty(name0);
      }
      
      private static function getSkillArr(gather0:String, father0:String) : Array
      {
         return skillArrObjObj[gather0][father0];
      }
      
      public static function getListAgent() : EditListAgent
      {
         var gather0:String = null;
         var fatherArr0:Array = null;
         var fatherCnArr0:Array = null;
         var len0:int = 0;
         var i:int = 0;
         var father0:String = null;
         var fatherCn0:String = null;
         var skillArr0:Array = null;
         var skillD0:SkillDefine = null;
         var a0:EditListAgent = skillAgent;
         if(!a0)
         {
            a0 = new EditListAgent();
            skillAgent = a0;
            a0.inTitle(gatherArr,gatherCnArr);
            for each(gather0 in gatherArr)
            {
               fatherArr0 = fatherArrObj[gather0];
               fatherCnArr0 = fatherCnArrObj[gather0];
               len0 = int(fatherArr0.length);
               for(i = 0; i < len0; i++)
               {
                  father0 = fatherArr0[i];
                  fatherCn0 = fatherCnArr0[i];
                  skillArr0 = getSkillArr(gather0,father0);
                  a0.newDataArr(gather0);
                  a0.addNormalLast("",fatherCn0,gather0,GatherColor.orangeColor);
                  for each(skillD0 in skillArr0)
                  {
                     a0.addDataLast(skillD0,gather0);
                  }
               }
            }
         }
         else
         {
            a0.clearFun();
         }
         return a0;
      }
      
      public static function getCnAgent() : EditListAgent
      {
         var gatherArr0:Array = null;
         var gatherCnArr0:Array = null;
         var a0:EditListAgent = cnAgent;
         if(!a0)
         {
            a0 = new EditListAgent();
            cnAgent = a0;
            a0.info = "选择一个名称前缀";
            gatherArr0 = ["bossEditCn2","bossEditCn1","random"];
            gatherCnArr0 = ["二字","一字","随机"];
            a0.inTitle(gatherArr0,gatherCnArr0);
            inCnDataList(a0,"bossEditCn1");
            inCnDataList(a0,"bossEditCn2","random");
            a0.createAllText();
         }
         else
         {
            a0.clearFun();
         }
         return a0;
      }
      
      private static function inCnDataList(a0:EditListAgent, dataListName0:String, ranFather0:String = "") : void
      {
         var str0:String = null;
         var s1:String = null;
         var s2:String = null;
         var len0:int = 0;
         var i:int = 0;
         var ran1:String = null;
         var ran2:String = null;
         var dataList0:DataListDefine = Gaming.defineGroup.dataList.getDefine(dataListName0);
         var strArr0:Array = dataList0.getTrueValueArr();
         var ranArr0:Array = [];
         for each(str0 in strArr0)
         {
            if(str0 != "")
            {
               a0.addNormalLast(str0,str0,dataListName0);
               if(ranFather0 != "")
               {
                  s1 = str0.substr(0,1);
                  if(ranArr0.indexOf(s1) == -1)
                  {
                     ranArr0.push(s1);
                  }
                  if(str0.length > 1)
                  {
                     s2 = str0.substr(1,1);
                     if(ranArr0.indexOf(s2) == -1)
                     {
                        ranArr0.push(s2);
                     }
                  }
               }
            }
         }
         if(ranFather0 != "")
         {
            len0 = EditListAgent.MAX_LINE * 5;
            for(i = 0; i < len0; i++)
            {
               ran1 = ArrayMethod.getRandomOne(ranArr0);
               ran2 = ArrayMethod.getRandomOne(ranArr0);
               str0 = ran1 + ran2;
               a0.addNormalLast(str0,str0,ranFather0);
            }
         }
      }
   }
}

