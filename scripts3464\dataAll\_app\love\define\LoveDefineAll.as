package dataAll._app.love.define
{
   import dataAll._player.role.RoleName;
   
   public class LoveDefineAll
   {
      
      private var obj:Object;
      
      public function LoveDefineAll()
      {
         var name0:String = null;
         var p0:LoveRoleAll = null;
         this.obj = {};
         super();
         var parr0:Array = RoleName.arr;
         for each(name0 in parr0)
         {
            p0 = new LoveRoleAll();
            p0.role = name0;
            this.obj[name0] = p0;
         }
      }
      
      public function getRole(roleName0:String) : LoveRoleAll
      {
         return this.obj[roleName0];
      }
      
      public function inLevelData_byXML(xml0:XML) : void
      {
         var roleName0:String = xml0.@name;
         var r0:LoveRoleAll = this.getRole(roleName0);
         r0.inLevelData_byXML(xml0);
      }
      
      public function inTalkData_byXML(xml0:XML) : void
      {
         var roleName0:String = xml0.@name;
         var r0:LoveRoleAll = this.getRole(roleName0);
         r0.inTalkData_byXML(xml0);
      }
   }
}

