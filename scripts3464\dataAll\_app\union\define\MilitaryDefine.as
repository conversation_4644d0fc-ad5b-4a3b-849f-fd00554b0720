package dataAll._app.union.define
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class MilitaryDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function MilitaryDefine()
      {
         super();
         this.must = 0;
         this.dpsMul = 0;
         this.totalMust = 0;
         this.giftMul = 1;
      }
      
      public function get must() : Number
      {
         return this.CF.getAttribute("must");
      }
      
      public function set must(v0:Number) : void
      {
         this.CF.setAttribute("must",v0);
      }
      
      public function get totalMust() : Number
      {
         return this.CF.getAttribute("totalMust");
      }
      
      public function set totalMust(v0:Number) : void
      {
         this.CF.setAttribute("totalMust",v0);
      }
      
      public function get dpsMul() : Number
      {
         return this.CF.getAttribute("dpsMul");
      }
      
      public function set dpsMul(v0:Number) : void
      {
         this.CF.setAttribute("dpsMul",v0);
      }
      
      public function get giftMul() : Number
      {
         return this.CF.getAttribute("giftMul");
      }
      
      public function set giftMul(v0:Number) : void
      {
         this.CF.setAttribute("giftMul",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.gift.inData_byXML(xml0.gift);
      }
      
      public function getDpsMulString() : String
      {
         return TextWay.numberToPer(this.dpsMul,0);
      }
      
      public function setGiftByFirst(gift0:GiftAddDefineGroup) : void
      {
         var arr2:Array = null;
         var mul0:Number = NaN;
         var d0:GiftAddDefine = null;
         var d2:GiftAddDefine = null;
         var num0:int = 0;
         if(this.gift.arr.length == 0)
         {
            arr2 = [];
            mul0 = this.giftMul;
            for each(d0 in gift0.arr)
            {
               d2 = d0.clone();
               num0 = d2.num * mul0;
               if(num0 >= 1)
               {
                  d2.num = num0;
                  arr2.push(d2);
               }
            }
            this.gift.arr = arr2;
         }
      }
   }
}

