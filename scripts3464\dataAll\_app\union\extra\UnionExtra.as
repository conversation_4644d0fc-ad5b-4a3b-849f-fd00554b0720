package dataAll._app.union.extra
{
   import com.common.data.Base64;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class UnionExtra
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var notice:String = "";
      
      public var url:String = "";
      
      public var life:Number = 0;
      
      public var dps:Number = 0;
      
      public var money:Number = 0;
      
      public var enemyUnionId:int = 0;
      
      public var weUnionId:int = 0;
      
      public var kingName:String = "";
      
      public var dpsLimit:Number = 0;
      
      public var vipLimit:int = 0;
      
      public function UnionExtra()
      {
         super();
      }
      
      public function get bs() : Number
      {
         return this.CF.getAttribute("bs");
      }
      
      public function set bs(v0:Number) : void
      {
         this.CF.setAttribute("bs",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function setNotice(str0:String) : void
      {
         this.notice = Base64.encodeString(str0);
      }
      
      public function getNotice() : String
      {
         return Base64.decodeString(this.notice);
      }
      
      public function setUrl(str0:String) : void
      {
         this.url = Base64.encodeString(str0);
      }
      
      public function getUrl() : String
      {
         if(this.url == "")
         {
            return "";
         }
         return Base64.decodeString(this.url);
      }
   }
}

