package dataAll._app.edit.boss
{
   public class BossEditPro
   {
      
      public static const baseUIArr:Array = ["lv","dp","li"];
      
      public static const heroBaseUIArr:Array = baseUIArr.concat("ar");
      
      public static const levelUIArr:Array = ["mp","tm","hb","pa","pe","ve","pr","si"];
      
      public static const mp:String = "mp";
      
      public static const lv:String = "lv";
      
      public static const li:String = "li";
      
      public static const dp:String = "dp";
      
      public static const cn:String = "cn";
      
      public static const sk:String = "sk";
      
      public static const ar:String = "ar";
      
      public static const ud:String = "ud";
      
      public static const pn:String = "pn";
      
      public static const armsTor:String = "armsTor";
      
      public function BossEditPro()
      {
         super();
      }
   }
}

