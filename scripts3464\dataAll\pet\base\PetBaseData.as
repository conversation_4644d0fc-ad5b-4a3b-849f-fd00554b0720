package dataAll.pet.base
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerBaseData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.must.define.MustDefine;
   import dataAll.pet.gene.GeneData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.ui.bar.NormalBarData;
   
   public class PetBaseData
   {
      
      public var save:PetBaseSave;
      
      public var PD:PlayerData;
      
      public var proData:EquipPropertyData = null;
      
      public function PetBaseData()
      {
         super();
      }
      
      public function inData_bySave(s0:PetBaseSave, pd0:PlayerData) : void
      {
         this.PD = pd0;
         this.save = s0;
      }
      
      public function initData_byGeneData(geneDa0:GeneData, pd0:PlayerData) : void
      {
         this.PD = pd0;
         this.save = new PetBaseSave();
         this.save.initData_byGeneData(geneDa0);
      }
      
      public function inProData(da0:EquipPropertyData) : void
      {
         this.proData = da0;
      }
      
      public function simpleClone() : PetBaseData
      {
         var da0:PetBaseData = new PetBaseData();
         da0.save = new PetBaseSave();
         da0.save.inData_byObj(this.save);
         da0.PD = this.PD;
         da0.proData = this.proData;
         return da0;
      }
      
      public function getMaxStrenLv() : int
      {
         var max0:int = this.save.dpsAdd;
         if(this.save.lifeAdd > max0)
         {
            max0 = this.save.lifeAdd;
         }
         if(this.save.headAdd > max0)
         {
            max0 = this.save.headAdd;
         }
         return max0;
      }
      
      public function addExp(v0:Number) : Boolean
      {
         var heroLv0:int = this.PD.level;
         var MAX_LEVEL:int = PlayerBaseData.MAX_LEVEL;
         if(MAX_LEVEL > heroLv0 + 10)
         {
            MAX_LEVEL = heroLv0 + 10;
         }
         v0 = Math.round(v0);
         var level0:int = this.save.level;
         var level2:int = level0;
         var max0:Number = this.getNextMaxExpByLevel(level0);
         var now0:Number = this.save.exp;
         if(level0 >= MAX_LEVEL)
         {
            now0 += v0;
            if(now0 > max0)
            {
               now0 = max0 - 1;
            }
            this.save.exp = now0;
         }
         else
         {
            now0 += v0;
            while(now0 >= max0)
            {
               now0 -= max0;
               level0++;
               if(level0 >= MAX_LEVEL)
               {
                  break;
               }
               max0 = this.getNextMaxExpByLevel(level0);
            }
            if(level0 >= MAX_LEVEL)
            {
               if(now0 > max0)
               {
                  now0 = max0 - 1;
               }
            }
            this.save.exp = now0;
            if(level0 > level2)
            {
               this.save.level = level0;
               return true;
            }
         }
         return false;
      }
      
      public function getNextMaxExp() : Number
      {
         return Gaming.defineGroup.normal.getPetExp(this.save.level);
      }
      
      public function getNextMaxExpByLevel(lv0:int) : Number
      {
         return Gaming.defineGroup.normal.getPetExp(lv0);
      }
      
      public function getNowExp() : Number
      {
         return this.save.exp;
      }
      
      public function upLevel() : void
      {
         var max0:int = PlayerBaseData.MAX_LEVEL;
         if(this.save.level < max0)
         {
            ++this.save.level;
         }
      }
      
      public function getShowDps() : Number
      {
         return this.getDps();
      }
      
      private function getSuperMul() : Number
      {
         var d0:GeneDefine = Gaming.defineGroup.gene.getDefine(this.save.name);
         if(Boolean(d0))
         {
            if(d0.superB)
            {
               return 1.3;
            }
         }
         return 1;
      }
      
      public function getDps(haveIntensB0:Boolean = true) : Number
      {
         var base0:Number = this.getBaseDps();
         var stren0:Number = this.getStrengthenValue("dps",haveIntensB0);
         var mul0:Number = (1 + this.proData.dpsMul + this.proData.hurtMul) * this.getSuperMul();
         var v0:Number = (base0 + stren0) * mul0 + this.proData.dps;
         if(haveIntensB0)
         {
            if(Boolean(this.PD))
            {
               v0 *= 1 + this.PD.getDpsWholeAdd();
            }
         }
         return Math.ceil(v0);
      }
      
      private function getBaseDps() : Number
      {
         return Gaming.defineGroup.normal.getArmsDps(this.save.level);
      }
      
      public function getMaxLife(haveIntensB0:Boolean = true) : Number
      {
         var b0:Number = this.getBaseLife();
         var stren0:Number = this.getStrengthenValue("life",haveIntensB0);
         return Math.ceil((b0 + this.proData.life + stren0) * (1 + this.proData.lifeMul) * this.getSuperMul());
      }
      
      private function getBaseLife() : Number
      {
         return Gaming.defineGroup.normal.getPetLife(this.save.level);
      }
      
      public function getLifeRate() : Number
      {
         return Math.ceil(this.proData.lifeRate * (1 + this.proData.lifeRateMul));
      }
      
      public function getHeadDefence() : Number
      {
         return Math.ceil((this.proData.head + this.getStrengthenValue("head")) * (1 + this.proData.headMul));
      }
      
      public function getHeadHurtMul() : Number
      {
         var b0:Number = this.getBaseLife();
         var v0:Number = this.getHeadDefence() / b0 / 2;
         if(v0 > 0.8)
         {
            v0 = 0.8;
         }
         return v0;
      }
      
      public function getStrengthenValue(name0:String, haveIntensB0:Boolean = true) : Number
      {
         var lv0:int = this.save.getAddLv(name0);
         if(haveIntensB0 == false)
         {
            if(lv0 > 99)
            {
               lv0 = 99;
            }
         }
         return Gaming.defineGroup.gene.getAllStrengthenValueByLv(name0,lv0);
      }
      
      public function getBaseValueByLabel(label0:String) : Number
      {
         if(label0 == "dps")
         {
            return this.getBaseDps();
         }
         if(label0 == "life")
         {
            return this.getBaseLife();
         }
         if(label0 == "head")
         {
            return 0;
         }
         return 0;
      }
      
      public function getValueByLabel(label0:String) : Number
      {
         if(label0 == "dps")
         {
            return this.getDps();
         }
         if(label0 == "life")
         {
            return this.getMaxLife();
         }
         if(label0 == "head")
         {
            return this.getHeadDefence();
         }
         return 0;
      }
      
      public function getUplevelMustDefine() : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var num0:int = Math.ceil(Gaming.defineGroup.gene.getPlayerStrengthenDrugNum(this.save.level) / 2);
         d0.inThingsDataByArr(["strengthenDrug;" + num0]);
         return d0;
      }
      
      public function getBarDataArr() : Array
      {
         var name0:String = null;
         var da0:NormalBarData = null;
         var proD0:PropertyArrayDefine = null;
         var lv0:int = 0;
         var maxLv0:int = 0;
         var arr0:Array = [];
         for each(name0 in PetBaseSave.addArr)
         {
            da0 = new NormalBarData();
            proD0 = Gaming.defineGroup.gene.strengthenPro.getDefine(name0);
            lv0 = this.save.getAddLv(name0);
            maxLv0 = proD0.getDataLen() - 1;
            da0.label = name0;
            da0.text = proD0.cnName;
            da0.otherText = ComMethod.dropColor(lv0,maxLv0) + " 级";
            arr0.push(da0);
         }
         return arr0;
      }
      
      public function getNowUseStrengthenDrugNum() : int
      {
         var pro0:String = null;
         var lv0:int = 0;
         var proArr0:Array = PetBaseSave.addArr;
         var num0:int = 0;
         for each(pro0 in proArr0)
         {
            lv0 = this.save.getAddLv(pro0);
            num0 += Gaming.defineGroup.gene.getAllStrengthenDrugNum(pro0,lv0);
         }
         return num0;
      }
   }
}

