package dataAll.bullet
{
   import com.sounto.utils.ClassProperty;
   
   public class BulletBoomDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var floorB:Boolean = false;
      
      public var bodyB:Boolean = false;
      
      public var selfB:Boolean = false;
      
      public var radius:Number = 0;
      
      public var hurtMul:Number = 1;
      
      public var maxHurtNum:int = 7;
      
      public var noExcludeBodyB:Boolean = false;
      
      public var haveBodyHitEffectB:Boolean = false;
      
      public function BulletBoomDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function copy() : BulletBoomDefine
      {
         var d0:BulletBoomDefine = new BulletBoomDefine();
         ClassProperty.inData(d0,this,pro_arr);
         return d0;
      }
   }
}

