package dataAll._app.union.extra
{
   import com.sounto.utils.ClassProperty;
   
   public class UnionExtraPer
   {
      
      public static var pro_arr:Array = [];
      
      public function UnionExtraPer()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(!obj0)
         {
            return;
         }
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}

