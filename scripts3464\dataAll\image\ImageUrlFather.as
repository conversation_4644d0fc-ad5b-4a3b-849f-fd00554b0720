package dataAll.image
{
   import com.sounto.utils.ArrayMethod;
   import dataAll.bullet.BulletDefine;
   
   public class ImageUrlFather
   {
      
      public static const normal:String = "normal";
      
      public static const bulletLine:String = "bulletLine";
      
      public static const outArr:Array = [normal,bulletLine];
      
      public static const bullet:String = "bullet";
      
      public static const bulletLeft:String = "bulletLeft";
      
      public static const smoke:String = "smoke";
      
      public static const hit:String = "hit";
      
      public static const hitFloor:String = "hitFloor";
      
      public static const boom:String = "boom";
      
      public static const fire:String = "fire";
      
      public static const state:String = "state";
      
      public static const state2:String = "state2";
      
      public static const add:String = "add";
      
      public static const target:String = "target";
      
      public static const me:String = "me";
      
      public static const other:String = "other";
      
      public static const point:String = "point";
      
      public static const ALL_ARR:Array = [normal,bullet,bulletLine,smoke,hit,boom,state,target,other];
      
      public static const ALL_CN:Array = ["常规","子弹","射线","尾烟","击打","爆炸火光","状态","瞬发","其他"];
      
      public static const BULLET_EDIT:Array = [normal,bullet,smoke,hit,boom];
      
      public static const BULLET_LINE_EDIT:Array = [normal,bulletLine];
      
      public static const bulletArr:Array = [bullet,bulletLeft,smoke,hit,hitFloor,fire,boom];
      
      public static const bulletCnArr:Array = ["","左","尾烟","击中","击地","火光","自爆"];
      
      public static const skillArr:Array = [add,me,target,point,other,state,state2];
      
      public static const skillCnArr:Array = ["添加","释放","目标","点","其他","状态","状态2"];
      
      BulletDefine.imgNameArr;
      
      public function ImageUrlFather()
      {
         super();
      }
      
      public static function getCn(father0:String) : String
      {
         var f0:int = int(ALL_ARR.indexOf(father0));
         return ArrayMethod.getElement(ALL_CN,f0,"","") as String;
      }
      
      public static function getCnArr(fatherArr0:Array) : Array
      {
         var father0:String = null;
         var cnArr0:Array = [];
         for each(father0 in fatherArr0)
         {
            cnArr0.push(getCn(father0));
         }
         return cnArr0;
      }
      
      public static function getFather(d0:ImageUrlDefine, posId0:String) : String
      {
         if(Boolean(d0) && d0.isBulletLineB())
         {
            return bulletLine;
         }
         if(posId0 == bulletLeft)
         {
            return bullet;
         }
         if(posId0 == hitFloor)
         {
            return hit;
         }
         if(posId0 == fire)
         {
            return boom;
         }
         if(posId0 == state2 || posId0 == add)
         {
            return state;
         }
         if(posId0 == me)
         {
            return target;
         }
         if(posId0 == point)
         {
            return other;
         }
         return posId0;
      }
   }
}

